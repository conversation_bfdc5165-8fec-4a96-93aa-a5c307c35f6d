package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> 陈俊明
 * @since : 2019-03-12
 * create at : 2019-03-12 18:38
 */

@RestController
@Slf4j
@Api(value = "LockSkuStrategyController", description = "店铺锁库条码特殊设置")
public class LockSkuStrategyController {
   /* @Autowired
    private LockSkuStrategySaveCmd lockSkuStrategySaveCmd;*/

    /*@Autowired
    private LockSkuStrategyDelCmd lockSkuStrategyDelCmd;*/

    /*@Autowired
    private LockSkuStrategyVoidCmd lockSkuStrategyVoidCmd;*/

    @Autowired
    private LockSkuStrategyAuditCmd lockSkuStrategyAuditCmd;

    /*@Autowired
    private LockSkuStrategyCancelAuditCmd lockSkuStrategyCancelAuditCmd;*/

    @Autowired
    private LockSkuStrategyFinishCmd lockSkuStrategyFinishCmd;

    @Autowired
    private LockSkuStrategyDelayCmd lockSkuStrategyDelayCmd;


   /* @ApiOperation(value = "店铺锁库条码特殊设置添加保存")
    @RequestMapping(path = "/api/cs/st/saveLockSkuStrategy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveLockSkuStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = lockSkuStrategySaveCmd.execute(querySession);
        return result;
    }*/

    /*@ApiOperation(value = "店铺锁库条码特殊设置删除")
    @RequestMapping(path = "/api/cs/st/delLockSkuStrategy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder delLockSkuStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = lockSkuStrategyDelCmd.execute(querySession);
        return result;
    }*/

   /* @ApiOperation(value = "店铺锁库条码特殊设置作废")
    @RequestMapping(path = "/api/cs/st/voidLockSkuStrategy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidLockSkuStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = lockSkuStrategyVoidCmd.execute(querySession);
        return result;
    }*/

    @ApiOperation(value = "店铺锁库条码特殊设置审核")
    @RequestMapping(path = "/api/cs/st/auditLockSkuStrategy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder auditLockSkuStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = lockSkuStrategyAuditCmd.execute(querySession);
        return result;
    }

   /* @ApiOperation(value = "店铺锁库条码特殊设置反审核")
    @RequestMapping(path = "/api/cs/st/cancelAuditLockSkuStrategy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder cancelAuditLockSkuStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = lockSkuStrategyCancelAuditCmd.execute(querySession);
        return result;
    }*/

    @ApiOperation(value = "店铺锁库条码特殊设置结案")
    @RequestMapping(path = "/api/cs/st/finishLockSkuStrategy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder finishLockSkuStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = lockSkuStrategyFinishCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "店铺锁库条码特殊设置延迟")
    @RequestMapping(path = "/api/cs/st/DelayLockSkuStrategy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 DelayLockSkuStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolderV14 holderV14 = lockSkuStrategyDelayCmd.execute(JSON.parseObject(param), user);
        return holderV14;
    }
}
