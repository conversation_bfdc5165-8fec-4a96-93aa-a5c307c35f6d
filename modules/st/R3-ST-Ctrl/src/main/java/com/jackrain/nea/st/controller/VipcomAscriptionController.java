package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.VipcomAscriptionProvideCmd;
import com.jackrain.nea.st.api.VipcomAscriptionSaveCmd;
import com.jackrain.nea.st.api.VipcomAscriptionVoidCmd;
import com.jackrain.nea.st.model.table.StCVipcomAscriptionDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2019/3/8 14:13
 */
@RestController
@Slf4j
@Api(value = "VipcomAscriptionController", description = "日程归属设置")
public class VipcomAscriptionController {
    @Autowired
    VipcomAscriptionSaveCmd vipcomAscriptionSaveCmd;

    @Autowired
    VipcomAscriptionVoidCmd vipcomAscriptionVoidCmd;

    @Autowired
    VipcomAscriptionProvideCmd vipcomAscriptionProvideCmd;



    @ApiOperation(value = "日程归属设置保存")
    @RequestMapping(path = "/saveVipcomAscription", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveVipcomAscription(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = vipcomAscriptionSaveCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "日程归属设置作废")
    @RequestMapping(path = "/batchVoidVipcomAscription", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder batchVoidVipcomAscription(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = vipcomAscriptionVoidCmd.execute(querySession);
        return result;
    }


    @ApiOperation(value = "日程归属查询")
    @RequestMapping(path = "/api/cs/st/queryVipcomAscriptionById", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14<StCVipcomAscriptionDO> queryVipcomAscriptionById(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        JSONObject paramObj = JSON.parseObject(param);
        log.debug(LogUtil.format("queryVipcomAscriptionById Received param:") + param);
        Object id = paramObj.getString("id");
        if (id == null) {
            Long objid = Long.valueOf(paramObj.getString("id"));
            ValueHolderV14<StCVipcomAscriptionDO> result = vipcomAscriptionProvideCmd.selectVipcomById(objid);
            return result;
        } else {
            ValueHolderV14<StCVipcomAscriptionDO> holder = new ValueHolderV14<StCVipcomAscriptionDO>();
            holder.setCode(-1);
            holder.setMessage("参数id不能为空！");
            return holder;
        }
    }
}
