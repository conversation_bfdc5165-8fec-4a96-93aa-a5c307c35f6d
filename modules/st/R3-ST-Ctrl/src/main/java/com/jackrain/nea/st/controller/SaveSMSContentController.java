package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.StCMsgStrategyCmd;
import com.jackrain.nea.st.api.StCMsgStrategyQueryCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Descroption 短信策略-短信内容保存
 * <AUTHOR>
 * @Date 2020/8/29
 */
@RestController
@Slf4j
@Api(value = "SaveSMSContentController", description = "短信内容保存")
public class SaveSMSContentController {

    @Autowired
    private StCMsgStrategyCmd stCMsgStrategyCmd;

    @Autowired
    private StCMsgStrategyQueryCmd stCMsgStrategyQueryCmd;

    @ApiOperation(value = "短信内容查询")
    @RequestMapping(path = "/api/cs/st/v1/querySmsContent", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14<String> queryContent(@RequestParam(value = "objid", required = true) Long objid) {
        return stCMsgStrategyQueryCmd.querySmsContent(objid);
    }

    @ApiOperation(value = "短信内容添加保存")
    @RequestMapping(path = "/api/cs/st/v1/saveSmsContent", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 saveContent(HttpServletRequest request, String param) {
        User user = Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);

        return stCMsgStrategyCmd.saveContent(querySession);
    }
}
