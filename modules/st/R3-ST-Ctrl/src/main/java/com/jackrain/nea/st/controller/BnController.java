package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.StBnQueryCmd;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName BnController
 * @Description 班牛查询
 * <AUTHOR>
 * @Date 2024/11/15 16:25
 * @Version 1.0
 */
@RestController
@Slf4j
public class BnController {

    @Autowired
    private StBnQueryCmd stBnQueryCmd;

    @ApiOperation(value = "查询班牛物流问题")
    @RequestMapping(value = "/api/cs/st/bn/queryLogisticsProblem", method = RequestMethod.POST)
    public JSONObject queryLogisticsProblem(HttpServletRequest request) {
        return stBnQueryCmd.queryBnProblem().toJSONObject();
    }
}
