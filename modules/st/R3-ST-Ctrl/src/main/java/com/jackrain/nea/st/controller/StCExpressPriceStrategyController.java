package com.jackrain.nea.st.controller;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCExpressPriceImportAndExportCmd;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyPoi;
import com.jackrain.nea.st.model.result.StImportErrorMsgResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/6/13 15:08
 */
@RestController
@Slf4j
@Api(value = "StCExpressPriceStrategyController", description = "快递费用设置")
public class StCExpressPriceStrategyController {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();


    @Autowired
    private StCExpressPriceImportAndExportCmd stCExpressPriceImportAndExportCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 测试用户
     */
    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("测试用户");
        user.setEname("test");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }


    @ApiOperation(value = "快递费用设置-批量导入")
    @RequestMapping(path = "/api/cs/st/importExpressPrice", method = RequestMethod.POST)
    public ValueHolderV14 importSyncUnfullcarCost(HttpServletRequest request,
                                                  @RequestParam(value = "file", required = true) MultipartFile file)
            throws IOException {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {

            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//            User user = getRootUser();

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" importExpressPrice fileName：") + file.getOriginalFilename());
            }
            //1.传入数据校验
            if (file == null) {
                throw new NDSException(Resources.getMessage("请求参数不能为空!"));
            }
            InputStream inputStream = null;
            Map<String, InputStream> inputStreamMap = new HashMap<>();
            try {
                inputStream = file.getInputStream();
                inputStreamMap.put("inputStream", inputStream);
                log.info(LogUtil.format(" File Conversion InputStream：") + inputStream);
            } catch (Exception e) {
                log.error(LogUtil.format("File Conversion InputStream Fail：{}"), Throwables.getStackTraceAsString(e));
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("文件转换成流失败!");
                return holderV14;
            }
            //2.解析Excel
            Workbook hssfWorkbook = null;
            try {
                hssfWorkbook = getWorkbookForImportFile(inputStream, file);
            } catch (IOException e) {
                log.error(LogUtil.format("快递费用设置-导入解析Excel失败！错误信息为：{}"), Throwables.getStackTraceAsString(e));
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("文件解析Excel失败!");
                return holderV14;
            } finally {
                inputStream.close();
            }
            //获取sheet页数
            log.info(LogUtil.format(" =========>>>>>>[llf]获取sheet页数！"));
            int sheetNum = hssfWorkbook.getNumberOfSheets();
            if (sheetNum != 1) {
                throw new NDSException(Resources.getMessage("快递费用设置-模板不正确!"));
            }
            ValueHolderV14<List<StImportErrorMsgResult>> importV14 = getSyncUnfullcarCostList(hssfWorkbook, holderV14, user);
            holderV14.setCode(importV14.getCode());
            holderV14.setMessage(importV14.getMessage());
            if (!importV14.isOK()) {
                List<StImportErrorMsgResult> errorMsgList = importV14.getData();
                if (CollectionUtils.isNotEmpty(errorMsgList)) {
                    String errorFilePath = stCExpressPriceImportAndExportCmd.downloadImportErrMsg(user, errorMsgList);
                    holderV14.setData(errorFilePath);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("快递费用设置-导入Excel失败！错误信息：{}"), Throwables.getStackTraceAsString(e));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("快递费用设置-导入Excel失败！错误信息：" + e.getMessage());
            return holderV14;
        }
        return holderV14;
    }


    @ApiOperation(value = "快递报价设置-批量导出")
    @RequestMapping(path = "/api/cs/st/exportExpressPrice", method = RequestMethod.POST)
    public ValueHolderV14 exportSyncUnfullcarCost(HttpServletRequest request,
                                                  @RequestParam(value = "ids", required = true) String ids)
            throws IOException {
        if (StringUtils.isBlank(ids)) {
            ValueHolderV14<Object> holderV14 = new ValueHolderV14<>();
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请选择需要导出的数据");
            return holderV14;
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = getRootUser();

        return this.export(ids,user);
    }

    private ValueHolderV14 export(String ids,User user) {
        return stCExpressPriceImportAndExportCmd.exportExpressPriceCost(ids,user);
    }


    private ValueHolderV14<List<StImportErrorMsgResult>> importExecute(List<StCExpressPriceStrategyPoi> importRequests, User user) {
        return stCExpressPriceImportAndExportCmd.importExpressPriceCost(importRequests,user);
    }


    /**
     * 获得workbook
     * @param inputStream
     * @param file
     * @return
     * @throws IOException
     */
    private Workbook getWorkbookForImportFile(InputStream inputStream, MultipartFile file) throws IOException {
        Workbook workbook = null;
        String fileName = file.getOriginalFilename();
        if (fileName.toLowerCase().endsWith("xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    /**
     * 解析获取
     * @param hssfWorkbook
     * @param vh
     * @param user
     * @return
     */
    private ValueHolderV14<List<StImportErrorMsgResult>> getSyncUnfullcarCostList(Workbook hssfWorkbook, ValueHolderV14 vh, User user) {
        List<StCExpressPriceStrategyPoi> importRequests = Lists.newArrayList();
        List<Map<String, String>> execlList;
        try {
            execlList = readExcel(0, hssfWorkbook);
            if (execlList == null) {
                throw new NDSException("读取Eexel文件失败");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("快递报价设置导入Excel文件失败！错误信息为：{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            if (index == 1) {
                // 校验excel字段
                if (columnMap.size() != 13
                        || !"仓库".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"物流公司".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"开始时间".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"结束时间".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"送货上门费".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"目的省份".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"目的城市".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"起始重量（KG/不包含）".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"结束重量（KG/包含）".equals(columnMap.get(rowStr + index + cellStr + 9))
                        || !"快递费用（元）".equals(columnMap.get(rowStr + index + cellStr + 10))
                        || !"首重1KG（元/单）".equals(columnMap.get(rowStr + index + cellStr + 11))
                        || !"续重（元/KG）".equals(columnMap.get(rowStr + index + cellStr + 12))
                ) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("导入文件与模板不符,请重新下载模板后导入");
                    return vh;
                }
            }
            if (index > 1) {
                StCExpressPriceStrategyPoi importRequest = getImportModel(index, columnMap);
                importRequests.add(importRequest);
            }
            index++;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" excel.data：") + importRequests);
        }
        return this.importExecute(importRequests, user);
    }




    /**
     * 导入方法
     * @param sheetIndex
     * @param hssfWorkbook
     * @return
     */
    private List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;
            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }

    /**
     * 转成对象
     * @param index
     * @param columnMap
     * @return
     */
    private StCExpressPriceStrategyPoi getImportModel(int index, Map<String, String> columnMap) {
        StCExpressPriceStrategyPoi importRequest = new StCExpressPriceStrategyPoi();
        importRequest.setWarehouseName(columnMap.get(rowStr + index + cellStr + 0));
        importRequest.setLogisticsName(columnMap.get(rowStr + index + cellStr + 1));
        importRequest.setStartDate(columnMap.get(rowStr + index + cellStr + 2));
        importRequest.setEndDate(columnMap.get(rowStr + index + cellStr + 3));
        importRequest.setPriceHomeDelivery(columnMap.get(rowStr + index + cellStr + 4));
        importRequest.setRemark(columnMap.get(rowStr + index + cellStr + 5));
        importRequest.setProvinceName(columnMap.get(rowStr + index + cellStr + 6));
        importRequest.setStartWeight(columnMap.get(rowStr + index + cellStr + 7));
        importRequest.setEndWeight(columnMap.get(rowStr + index + cellStr + 8));
        importRequest.setPriceExpress(columnMap.get(rowStr + index + cellStr + 9));
        importRequest.setPriceFirstWeight(columnMap.get(rowStr + index + cellStr + 10));
        importRequest.setPriceContinuedWeight(columnMap.get(rowStr + index + cellStr + 11));
        return importRequest;
    }




}
