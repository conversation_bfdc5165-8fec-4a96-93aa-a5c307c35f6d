package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.EwaybillDelCmd;
import com.jackrain.nea.st.api.EwaybillSaveCmd;
import com.jackrain.nea.st.api.EwaybillVoidCmd;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Descroption 电子面单策略
 * <AUTHOR>
 * @Date 2019/3/11 20:17
 */
@RestController
@Slf4j
@Api(value = "EwaybillController", description = "电子面单策略")
public class EwaybillController {
    @Autowired
    private EwaybillSaveCmd ewaybillSaveCmd;

    @Autowired
    private EwaybillDelCmd ewaybillDelCmd;

    @Autowired
    private EwaybillVoidCmd ewaybillVoidCmd;

    @ApiOperation(value = "添加电子面单策略")
    @RequestMapping(path = "/api/cs/st/ewaybillSaveCmd", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder ewaybillSaveCmd(HttpServletRequest request, String param) throws Exception {
        // 第一步：记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        log.debug(LogUtil.format("Start EwaybillController.ewaybillSaveCmd. ReceiveParams: {}"), param);

        // 第二步：获取当前登录系统用户信息。
        User loginUser = Security4Utils.getUser("root");

        // 第三步：检查参数合法性
//        if (StringUtils.isEmpty(param)) {
//            return JSON.toJSONStringWithDateFormat(ValueHolderUtils.getFailValueHolder("用户参数非法！").toJSONObject(),
//                    "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);
//        }

        // 第四步：调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);

        ValueHolder result;
        try {
            result = ewaybillSaveCmd.execute(querySession);
        } catch (Exception ex) {
            result = ValueHolderUtils.getFailValueHolder("保存异常");
        }
//
//        String result = JSON.toJSONStringWithDateFormat(callAPIResult.toJSONObject(),
//                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);


        // 第五步：记录日志信息。Finish 标记结束
        log.debug(LogUtil.format("Finish EwaybillController.ewaybillSaveCmd. ReceiveParams: {}"), param);

        // 第六步：返回信息
        return result;
    }

    @ApiOperation(value = "删除电子面单策略")
    @RequestMapping(path = "/api/cs/st/ewaybillDelCmd", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder ewaybillDelCmd(HttpServletRequest request, String param) throws Exception {
        // 第一步：记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        log.debug(LogUtil.format("Start EwaybillController.ewaybillDelCmd. ReceiveParams: {}"), param);

        // 第二步：获取当前登录系统用户信息。
        User loginUser = Security4Utils.getUser("root");

        // 第三步：检查参数合法性
//        if (StringUtils.isEmpty(param)) {
//            return JSON.toJSONStringWithDateFormat(ValueHolderUtils.getFailValueHolder("用户参数非法！").toJSONObject(),
//                    "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);
//        }

        // 第四步：调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);

        ValueHolder result;
        try {
            result = ewaybillDelCmd.execute(querySession);
        } catch (Exception ex) {
            result = ValueHolderUtils.getFailValueHolder("删除异常");
        }
//
//        String result = JSON.toJSONStringWithDateFormat(callAPIResult.toJSONObject(),
//                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);


        // 第五步：记录日志信息。Finish 标记结束
        log.debug(LogUtil.format("Finish EwaybillController.ewaybillDelCmd. ReceiveParams: {}"), param);

        // 第六步：返回信息
        return result;
    }

    @ApiOperation(value = "电子面单策略作废")
    @RequestMapping(path = "/api/cs/st/ewaybillVoidCmd", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder delResolveRule(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = ewaybillVoidCmd.execute(querySession);
        return result;
    }
}
