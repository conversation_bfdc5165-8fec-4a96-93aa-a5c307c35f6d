package com.jackrain.nea.st.controller;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCUnfullcarCostImportCmd;
import com.jackrain.nea.st.model.request.SyncUnfullcarCostImportRequest;
import com.jackrain.nea.st.result.StImportErrorMsgResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/6/13 15:08
 */
@RestController
@Slf4j
@Api(value = "SyncUnfullcarCostController", description = "零担费用设置")
public class SyncUnfullcarCostController {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();


    @Autowired
    private StCUnfullcarCostImportCmd stCUnfullcarCostImportCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 测试用户
     */
    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("测试用户");
        user.setEname("test");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }


    @ApiOperation(value = "零担费用设置-批量导入")
    @RequestMapping(path = "/api/cs/st/importSyncUnfullcarCost", method = RequestMethod.POST)
    public ValueHolderV14<String> importSyncUnfullcarCost(HttpServletRequest request,
                                                          @RequestParam(value = "file", required = true) MultipartFile file) {
        ValueHolderV14<String> holderV14 = new ValueHolderV14();
        try {

            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//            User user = getRootUser();

            log.info(user == null ? "用户信息为空" : "用户信息：" + user.getName());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" importSyncUnfullcarCost fileName：") + file.getOriginalFilename());
            }
            //1.传入数据校验
            if (file == null) {
                throw new NDSException(Resources.getMessage("请求参数不能为空!"));
            }
            InputStream inputStream = null;
            Map<String, InputStream> inputStreamMap = new HashMap<>();
            try {
                inputStream = file.getInputStream();
                inputStreamMap.put("inputStream", inputStream);
                log.info(LogUtil.format(" File Conversion InputStream：") + inputStream);
            } catch (Exception e) {
                log.error(LogUtil.format("File Conversion InputStream Fail：{}"), Throwables.getStackTraceAsString(e));
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("文件转换成流失败!");
                return holderV14;
            }
            //2.解析Excel
            Workbook hssfWorkbook = null;
            try {
                hssfWorkbook = getWorkbookForImportFile(inputStream, file);
            } catch (IOException e) {
                log.error(LogUtil.format("零担费用设置-导入解析Excel失败！错误信息为：{}"), Throwables.getStackTraceAsString(e));
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("文件解析Excel失败!");
                return holderV14;
            } finally {
                inputStream.close();
            }
            //获取sheet页数
            log.info(LogUtil.format(" =========>>>>>>[llf]获取sheet页数！"));
            int sheetNum = hssfWorkbook.getNumberOfSheets();
            if (sheetNum != 1) {
                throw new NDSException(Resources.getMessage("零担费用设置-模板不正确!"));
            }
            ValueHolderV14<List<StImportErrorMsgResult>> importV14 = getSyncUnfullcarCostList(hssfWorkbook, user);
            holderV14.setCode(importV14.getCode());
            holderV14.setMessage(importV14.getMessage());
            if (!importV14.isOK()) {
                List<StImportErrorMsgResult> errorMsgList = importV14.getData();
                if (CollectionUtils.isNotEmpty(errorMsgList)) {
                    String errorFilePath = stCUnfullcarCostImportCmd.downloadImportErrMsg(user, errorMsgList);
                    holderV14.setData(errorFilePath);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("零担费用设置-导入Excel失败！错误信息：{}"), Throwables.getStackTraceAsString(e));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("零担费用设置-导入Excel失败！错误信息：" + e.getMessage());
            return holderV14;
        }
        return holderV14;
    }


    @ApiOperation(value = "零担费用设置-批量导出")
    @RequestMapping(path = "/api/cs/st/exportSyncUnfullcarCost", method = RequestMethod.POST)
    public ValueHolderV14<String> exportSyncUnfullcarCost(HttpServletRequest request,
                                                          @RequestParam(value = "ids", required = true) String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请选择需要导出的数据!");
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        log.info(user == null ? "用户信息为空" : "用户信息：" + user.getName());
        SyncUnfullcarCostController unfullcarCostController = ApplicationContextHandle.getBean(SyncUnfullcarCostController.class);
        return unfullcarCostController.export(ids, user);
    }

    public ValueHolderV14<String> export(String ids, User user) {
        return stCUnfullcarCostImportCmd.exportUnfullcarCost(ids, user);
    }


    public ValueHolderV14<List<StImportErrorMsgResult>> importExecute(List<SyncUnfullcarCostImportRequest> importRequests, User user) {
        return stCUnfullcarCostImportCmd.importUnfullcarCost(importRequests, user);
    }


    /**
     * 获得workbook
     * @param inputStream
     * @param file
     * @return
     * @throws IOException
     */
    private Workbook getWorkbookForImportFile(InputStream inputStream, MultipartFile file) throws IOException {
        Workbook workbook = null;
        String fileName = file.getOriginalFilename();
        if (fileName.toLowerCase().endsWith("xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    /**
     * 解析获取
     *
     * @param hssfWorkbook
     * @param user
     * @return
     */
    private ValueHolderV14<List<StImportErrorMsgResult>> getSyncUnfullcarCostList(Workbook hssfWorkbook, User user) {
        List<SyncUnfullcarCostImportRequest> importRequests = Lists.newArrayList();
        List<Map<String, String>> execlList;
        try {
            execlList = readExcel(0, hssfWorkbook);
            if (execlList == null) {
                throw new NDSException("读取Eexel文件失败");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("零担费用设置导入Excel文件失败！错误信息为：{}"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            if (index == 1) {
                // 校验excel字段
                if (columnMap.size() != 17
                        || !"仓库".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"物流公司".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"开始日期".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"结束日期".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"油价联动（%）".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"目的省份".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"目的城市".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"到货天数".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"起始重量（T/不包含）".equals(columnMap.get(rowStr + index + cellStr + 9))
                        || !"结束重量（T/包含）".equals(columnMap.get(rowStr + index + cellStr + 10))
                        || !"干线运费（元/T）".equals(columnMap.get(rowStr + index + cellStr + 11))
                        || !"提货费（元/票）".equals(columnMap.get(rowStr + index + cellStr + 12))
                        || !"送货费（元/票）".equals(columnMap.get(rowStr + index + cellStr + 13))
                        || !"保费（元/T）".equals(columnMap.get(rowStr + index + cellStr + 14))
                        || !"卸货费（元/T）".equals(columnMap.get(rowStr + index + cellStr + 15))
                        || !"其他费用".equals(columnMap.get(rowStr + index + cellStr + 16))
                ) {
                    return new ValueHolderV14<>(ResultCode.FAIL, "导入文件与模板不符,请重新下载模板后导入");
                }
            }
            if (index > 1) {
                SyncUnfullcarCostImportRequest importRequest = getImportModel(index, columnMap);
                importRequests.add(importRequest);
            }
            index++;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" excel.data：") + importRequests);
        }

        return importExecute(importRequests, user);
    }




    /**
     * 导入方法
     * @param sheetIndex
     * @param hssfWorkbook
     * @return
     */
    private List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) {
        List<Map<String, String>> list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;
            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }

    /**
     * 转成对象
     * @param index
     * @param columnMap
     * @return
     */
    private SyncUnfullcarCostImportRequest getImportModel(int index, Map<String, String> columnMap) {
        SyncUnfullcarCostImportRequest importRequest = new SyncUnfullcarCostImportRequest();
        importRequest.setWarehouseName(columnMap.get(rowStr + index + cellStr + 0));
        importRequest.setLogisticsName(columnMap.get(rowStr + index + cellStr + 1));
        importRequest.setStartDate(columnMap.get(rowStr + index + cellStr + 2));
        importRequest.setEndDate(columnMap.get(rowStr + index + cellStr + 3));
        importRequest.setOilPriceLinkage(columnMap.get(rowStr + index + cellStr + 4));
        importRequest.setRemark(columnMap.get(rowStr + index + cellStr + 5));
        importRequest.setProvince(columnMap.get(rowStr + index + cellStr + 6));
        importRequest.setCity(columnMap.get(rowStr + index + cellStr + 7));
        importRequest.setArrivalDays(columnMap.get(rowStr + index + cellStr + 8));
        importRequest.setStartWeight(columnMap.get(rowStr + index + cellStr + 9));
        importRequest.setEndWeight(columnMap.get(rowStr + index + cellStr + 10));
        importRequest.setTrunkFreight(columnMap.get(rowStr + index + cellStr + 11));
        importRequest.setDeliveryFee(columnMap.get(rowStr + index + cellStr + 12));
        importRequest.setFreight(columnMap.get(rowStr + index + cellStr + 13));
        importRequest.setPremium(columnMap.get(rowStr + index + cellStr + 14));
        importRequest.setUnloadingFee(columnMap.get(rowStr + index + cellStr + 15));
        importRequest.setOtherFee(columnMap.get(rowStr + index + cellStr + 16));
        return importRequest;
    }




}
