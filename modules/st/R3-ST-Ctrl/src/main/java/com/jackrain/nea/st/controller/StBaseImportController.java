package com.jackrain.nea.st.controller;

import com.burgeon.r3.impl.oss.AliOssStorageFile;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * @Descroption 导入基础Controller
 * <AUTHOR>
 * @Date 2019/8/15 13:17
 */
@RestController
@Slf4j
public class StBaseImportController {
    public static final String CELL_STR = "cell_";
    public static final String ROW_STR = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();

    /**
     * @param inputStream
     * @param file
     * @return org.apache.poi.ss.usermodel.Workbook
     * @Description 分版本处理Excel数据
     * <AUTHOR>
     * @date 2019/7/27 15:05
     */
    public Workbook getWorkbookForImportFile(InputStream inputStream, MultipartFile file) throws IOException {
        Workbook workbook = null;
        String fileName = file.getName();
        if (fileName.toLowerCase().endsWith("xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }
        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(ROW_STR + cell.getRowIndex() + CELL_STR + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;
            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }

    public String generateResultExcel(User user, String fileName, List<String> errors) throws FileNotFoundException {
        String randomId = UUID.randomUUID().toString().replace("-", "");
        String virtualBaseDir = "/import/" + user.getId() + "/Result/" + randomId + "/";

        String virtualPath = virtualBaseDir + fileName + ".xlsx";
        String physicalBaseDir = ResourceUtils.getURL("/home/<USER>/tmp/").getPath() + virtualBaseDir;
        String fullFileName = physicalBaseDir + fileName + ".xlsx";

        File svrDir = new File(physicalBaseDir);
        if (!svrDir.isDirectory()) {
            svrDir.mkdirs();
        }

        try {
            Workbook wb = new XSSFWorkbook();
            Sheet sheet = wb.createSheet(fileName);

            sheet.createFreezePane(0, 1, 0, 1);

            CellStyle titleStyle = wb.createCellStyle();
            Font titleFont = wb.createFont();
            titleFont.setFontHeightInPoints((short) 10);
            titleFont.setFontName("宋体");
            titleFont.setColor(IndexedColors.DARK_BLUE.index);
            titleFont.setBold(true);
            titleStyle.setFont(titleFont);

            CellStyle style = wb.createCellStyle();
            Font font = wb.createFont();
            font.setFontHeightInPoints((short) 10);
            font.setFontName("宋体");
            style.setFont(font);
            //font.setColor(HSSFColor.DARK_BLUE.index);
            style.setWrapText(true);

            Row titleRow = sheet.createRow(0);
            createCell(titleStyle, titleRow, 0, "行号");
            createCell(titleStyle, titleRow, 1, "错误信息");

            int index = 0;
            for (String error : errors) {
                index++;
                Row row = sheet.createRow(index);
                createCell(style, row, 0, Integer.toString(index));
                createCell(style, row, 1, error);
                index++;
            }
            //sheet.autoSizeColumn(0);
            try {
                sheet.autoSizeColumn(1);
            } catch (Exception ex) {
                log.error(LogUtil.format("generateResultExcel.AutoSizeColumn.Error{}"),
                        Throwables.getStackTraceAsString(ex));
            }

            index = 1;
            for (String error : errors) {
                Row row = sheet.getRow(index);
                if (row == null) {
                    continue;
                }
                try {
                    calcAndSetRowHeigt(row, font, error);
                } catch (Exception e) {
                    log.error(LogUtil.format("calcAndSetRowHeigt报错：{}") , Throwables.getStackTraceAsString(e));
                }
                index++;
            }
            FileOutputStream fileOut = new FileOutputStream(fullFileName);
            wb.write(fileOut);
            fileOut.close();


            String uploadOssPath = "import/result/" + user.getId() + "/";
            String uploadOssFileName = new AliOssStorageFile().uploadFile(uploadOssPath, fileName,
                    ".xlsx", fullFileName, user.getName());
            if (StringUtils.isNotEmpty(uploadOssFileName)) {
                virtualPath = uploadOssFileName;
            }
        } catch (Exception e) {
            log.debug(LogUtil.format("generateResultExcel:{}") , Throwables.getStackTraceAsString(e));
        }
        return virtualPath;
    }

    private static void createCell(CellStyle style, Row row, int columnIndex, String value) {
        Cell cell = row.createCell(columnIndex);

        cell.setCellStyle(style);
        cell.setCellType(XSSFCell.CELL_TYPE_STRING);
        cell.setCellValue(value);
    }

    public static void calcAndSetRowHeigt(Row sourceRow, Font font, String message) {
        //行高
        double maxHeight = sourceRow.getHeight();
        Cell sourceCell = sourceRow.getCell(1);
        //单元格的内容
        String cellContent = message;
        if (null == cellContent || "".equals(cellContent)) {
            return;
        }

        //单元格的宽高及单元格信息
        Map<String, Object> cellInfoMap = getCellInfo(sourceCell);
        Integer cellWidth = (Integer) cellInfoMap.get("width");
        Integer cellHeight = (Integer) cellInfoMap.get("height");
        if (cellHeight > maxHeight) {
            maxHeight = cellHeight;
        }
        //字体的高度
        short fontHeight = font.getFontHeight();

        //cell内容字符串总宽度
        double cellContentWidth = cellContent.getBytes().length * 2 * 256;

        //字符串需要的行数 不做四舍五入之类的操作
        double stringNeedsRows = cellContentWidth / cellWidth;
        //小于一行补足一行
        if (stringNeedsRows < 1.0) {
            stringNeedsRows = 1.0;
        }

        //需要的高度             (Math.floor(stringNeedsRows) - 1) * 40 为两行之间空白高度
        double stringNeedsHeight = (double) fontHeight * stringNeedsRows;
        //需要重设行高
        if (stringNeedsHeight > maxHeight) {
            maxHeight = stringNeedsHeight;
            //超过原行高三倍 则为5倍 实际应用中可做参数配置
            if (maxHeight / cellHeight > 5) {
                maxHeight = 5 * cellHeight;
            }
            //最后取天花板防止高度不够
            maxHeight = Math.ceil(maxHeight);
            //重新设置行高 同时处理多行合并单元格的情况
            sourceRow.setHeight((short) maxHeight);
        }
    }

    private static Map<String, Object> getCellInfo(Cell cell) {
        Sheet sheet = cell.getSheet();
        int rowIndex = cell.getRowIndex();
        int columnIndex = cell.getColumnIndex();

        boolean isPartOfRegion = false;
        int firstColumn = 0;
        int lastColumn = 0;
        int firstRow = 0;
        int lastRow = 0;
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress ca = sheet.getMergedRegion(i);
            firstColumn = ca.getFirstColumn();
            lastColumn = ca.getLastColumn();
            firstRow = ca.getFirstRow();
            lastRow = ca.getLastRow();
            if (rowIndex >= firstRow && rowIndex <= lastRow) {
                if (columnIndex >= firstColumn && columnIndex <= lastColumn) {
                    isPartOfRegion = true;
                    break;
                }
            }
        }
        Map<String, Object> map = new HashMap<String, Object>();
        Integer width = 0;
        Integer height = 0;
        boolean isPartOfRowsRegion = false;
        if (isPartOfRegion) {
            for (int i = firstColumn; i <= lastColumn; i++) {
                width += sheet.getColumnWidth(i);
            }
            for (int i = firstRow; i <= lastRow; i++) {
                height += sheet.getRow(i).getHeight();
            }
            if (lastRow > firstRow) {
                isPartOfRowsRegion = true;
            }
        } else {
            width = sheet.getColumnWidth(columnIndex);
            height += cell.getRow().getHeight();
        }
        map.put("isPartOfRowsRegion", isPartOfRowsRegion);
        map.put("firstRow", firstRow);
        map.put("lastRow", lastRow);
        map.put("width", width);
        map.put("height", height);
        return map;
    }
}
