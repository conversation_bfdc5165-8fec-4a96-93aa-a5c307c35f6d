package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.st.api.*;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Descroption 分销代销策略
 * <AUTHOR>
 * @Date 2019/3/6 20:17
 */
@RestController
@Slf4j
@Api(value = "DistributionController", description = "分销代销策略")
public class DistributionController {
    @Autowired
    private DistributionSaveCmd distributionSaveCmd;

    @Autowired
    private DistributionDelCmd distributionDelCmd;

    @Autowired
    private DistributionVoidCmd distributionVoidCmd;

    @Autowired
    private DistributionAuditCmd distributionAuditCmd;

    @Autowired
    private DistributionUnAuditCmd distributionUnAuditCmd;

    @Autowired
    private DistributionFinishCmd distributionFinishCmd;

    @Autowired
    private DistributionDelayCmd distributionDelayCmd;

    @Autowired
    private DistributionCopyCmd distributionCopyCmd;

    @Autowired
    private DistributionQueryCmd distributionQueryCmd;

    @ApiOperation(value = "添加分销代销策略")
    @RequestMapping(path = "/api/cs/st/saveDistributionInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder distributionSaveCmd(HttpServletRequest request, @RequestParam(value = "param", required = true)String param) {
        // 1.记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("Start DistributionController.distributionSaveCmd. ReceiveParams: {}"), param);
        }

        // 2.获取当前登录系统用户信息。
        User loginUser = Security4Utils.getUser("root");
        // 3.调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result;
        result = distributionSaveCmd.execute(querySession);
        // 4.记录日志信息。Finish 标记结束
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish DistributionController.distributionSaveCmd. ReceiveParams: {}"), param);
        }
        // 5.返回信息
        return result;
    }

    @ApiOperation(value = "删除分销代销策略")
    @RequestMapping(path = "/distributionDelCmd", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder distributionDelCmd(HttpServletRequest request, String param) {
        // 1.记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start DistributionController.distributionDelCmd. ReceiveParams: {}"), param);
        }

        // 2.获取当前登录系统用户信息。
        User loginUser = Security4Utils.getUser("root");
        // 3.调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result;
        result = distributionDelCmd.execute(querySession);
        // 4.记录日志信息。Finish 标记结束
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish DistributionController.distributionDelCmd. ReceiveParams: {}"), param);
        }
        // 5.返回信息
        return result;
    }

    @ApiOperation(value = "作废分销代销策略")
    @RequestMapping(path = "/api/cs/st/voidDistributionInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder distributionVoidCmd(HttpServletRequest request, String param) {
        // 1.记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start DistributionController.distributionVoidCmd. ReceiveParams: {}"), param);
        }

        // 2.获取当前登录系统用户信息。
        User loginUser = Security4Utils.getUser("root");

        // 3.调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);

        ValueHolder result;
        result = distributionVoidCmd.execute(querySession);

        // 4.记录日志信息。Finish 标记结束
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish DistributionController.distributionVoidCmd. ReceiveParams: {}"), param);
        }

        // 5.返回信息
        return result;
    }

    @ApiOperation(value = "审核分销代销策略")
    @RequestMapping(path = "/api/cs/st/auditDistributionInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder distributionAuditCmd(HttpServletRequest request, String param){
        // 1.记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start DistributionController.distributionAuditCmd. ReceiveParams: {}"), param);
        }
        // 2.获取当前登录系统用户信息。
        User loginUser = Security4Utils.getUser("root");
        // 3.调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result;
        result = distributionAuditCmd.execute(querySession);
        // 4.记录日志信息。Finish 标记结束
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish DistributionController.distributionAuditCmd. ReceiveParams: {}"), param);
        }
        // 5.返回信息
        return result;
    }

    @ApiOperation(value = "反审核分销代销策略")
    @RequestMapping(path = "/api/cs/st/unAuditDistributionInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder distributionUnAuditCmd(HttpServletRequest request, String param){
        // 1.记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start DistributionController.distributionUnAuditCmd. ReceiveParams: {}"), param);
        }
        // 2.获取当前登录系统用户信息。
        User loginUser = Security4Utils.getUser("root");
        // 3.调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result;
        result = distributionUnAuditCmd.execute(querySession);
        // 4.记录日志信息。Finish 标记结束
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish DistributionController.distributionUnAuditCmd. ReceiveParams: {}"), param);
        }
        // 5.返回信息
        return result;
    }

    @ApiOperation(value = "结案分销代销策略")
    @RequestMapping(path = "/api/cs/st/finishDistributionInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder distributionFinishCmd(HttpServletRequest request, String param) {
        // 1.记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start DistributionController.distributionFinishCmd. ReceiveParams: {}"), param);
        }
        // 2.获取当前登录系统用户信息。
        User loginUser = Security4Utils.getUser("root");
        // 3.调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result;
        result = distributionFinishCmd.execute(querySession);
        // 4.记录日志信息。Finish 标记结束
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish DistributionController.distributionFinishCmd. ReceiveParams: {}"), param);
        }
        // 5.返回信息
        return result;
    }
    @ApiOperation(value = "分销代销方案复制")
    @RequestMapping(path = "/api/cs/st/distributionCopy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14<List<StCDistributionDO>> distributionCopyCmd(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        // 1.记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start DistributionController.distributionCopyCmd. ReceiveParams: {}"), param);
        }
        User user = Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        return  distributionCopyCmd.execute(querySession);
    }

    @ApiOperation(value = "获取分销代销信息接口")
    @RequestMapping(path = "/api/cs/st/getDistributionInfo", method = {RequestMethod.POST})
    public JSONObject getDistributionInfo(HttpServletRequest request,
                                          @RequestParam(value = "param", required = true)
                                                       String param) {
        QuerySessionImpl querySession = new QuerySessionImpl();
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder vh = distributionQueryCmd.execute(querySession);
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }
}
