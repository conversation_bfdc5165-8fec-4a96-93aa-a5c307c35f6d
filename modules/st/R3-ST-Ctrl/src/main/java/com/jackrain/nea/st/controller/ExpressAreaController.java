package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.ExpressAreaItemImportCmd;
import com.jackrain.nea.st.model.table.StCExpressAreaItemDO;
import com.jackrain.nea.st.services.*;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @Descroption 物流区域设置
 * <AUTHOR>
 * @Date 2019/3/13 13:17
 */
@RestController
@Slf4j
@Api(value = "ExpressAreaController", description = "物流区域设置")
public class ExpressAreaController extends StBaseImportController {
    @Autowired
    private ExpressAreaSaveCmdImpl expressAreaSaveCmd;

    @Autowired
    private ExpressAreaDelCmdImpl expressAreaDelCmd;

    @Autowired
    private ExpressAreaVoidCmdImpl expressAreaVoidCmd;

    @Autowired
    private ExpressAreaQueryCmdImpl expressAreaQueryCmd;

    @Autowired
    private ExpressAreaItemExportCmdImpl expressAreaItemExportCmd;

    @Autowired
    private ExpressAreaItemImportCmd expressAreaItemImportCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "添加物流区域设置")
    @RequestMapping(path = "/api/cs/st/expressAreaSaveCmd", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder expressAreaSaveCmd(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        //获取当前登录系统用户信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = Security4Utils.getUser("root");
        //调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);

        ValueHolder result;
        try {
            result = expressAreaSaveCmd.execute(querySession);
        } catch (Exception ex) {
            result = ValueHolderUtils.getFailValueHolder("保存异常");
        }
        //返回信息
        return result;
    }

    @ApiOperation(value = "删除物流区域设置")
    @RequestMapping(path = "/api/cs/st/expressAreaDelCmd", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder ewaybillDelCmd(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        //获取当前登录系统用户信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = Security4Utils.getUser("root");
        //调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);

        ValueHolder result;
        try {
            result = expressAreaDelCmd.execute(querySession);
        } catch (Exception ex) {
            result = ValueHolderUtils.getFailValueHolder("删除异常");
        }
        //返回信息
        return result;
    }

    @ApiOperation(value = "作废物流区域设置")
    @RequestMapping(path = "/api/cs/st/expressAreaVoidCmd", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder expressAreaVoidCmd(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        //获取当前登录系统用户信息。
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User loginUser = Security4Utils.getUser("root");
        //调用服务
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);

        ValueHolder result = expressAreaVoidCmd.execute(querySession);
        //返回信息
        return result;
    }

    @ApiOperation(value = "获取物流区域树")
    @RequestMapping(value = "/api/cs/st/getExpressAreaTree", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject queryExpressAreaTree(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = expressAreaQueryCmd.queryExpressAreaTree(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "获取物流区域明细数据")
    @RequestMapping(value = "/api/cs/st/getExpressAreaItemTable", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject queryExpressAreaItemTable(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = expressAreaQueryCmd.queryExpressAreaItemTable(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "模糊地址获取物流区域明细数据")
    @RequestMapping(value = "/api/cs/st/getExpressAreaItemLikeTable", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject queryExpressAreaItemLikeTable(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = expressAreaQueryCmd.queryExpressAreaItemLikeTable(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "列表导出")
    @RequestMapping(path = "/api/cs/st/exportExpressAreaItem", method = RequestMethod.POST)
    public ValueHolderV14 getItemList(HttpServletRequest request,@RequestBody JSONObject obj){
        UserImpl user = (UserImpl)r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = Security4Utils.getUser("root");
        return expressAreaItemExportCmd.exportExpressAreaItem(obj, user);
    }

    @ApiOperation(value = "物流区域明细下载导入模板")
    @RequestMapping(path = "/api/cs/st/downloadExpressAreaItem", method = {RequestMethod.POST})
    public ValueHolderV14 downloadExpressAreaItem() {
        return expressAreaItemImportCmd.downloadTemp();
    }

    @ApiOperation(value = "物流区域明细导入接口")
    @RequestMapping(path = "/api/cs/st/importExpressAreaItem", method = RequestMethod.POST)
    public ValueHolderV14 importExpressAreaItem(HttpServletRequest request,
                                                @RequestParam(value = "file", required = true) MultipartFile file,
                                                @RequestParam(value = "objid", required = true) Long objid) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = Security4Utils.getUser("root");
        //1.传入数据校验
        ValueHolderV14 vh = new ValueHolderV14();
        if (file == null || objid == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不能为空!");
            return vh;
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            log.error(LogUtil.format("导入文件装换成流失败！错误信息为：{}") , Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件转换成流失败!");
            return vh;
        }
        //2.解析Excel
        Workbook workbook = null;
        try {
            workbook = getWorkbookForImportFile(inputStream,file);
        } catch (IOException e) {
            log.error(LogUtil.format("解析Excel失败！错误信息为：{}") , Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件解析Excel失败!");
            return vh;
        }

        List<Map<String, String>> execlList = null;
        try{
            execlList = readExcel(0, workbook);
            if(execlList == null){
                throw new NDSException("读取Eexel文件失败");
            }
        }catch(Exception e){
            log.error(LogUtil.format("读取Excel文件失败！错误信息为：{}") , Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
        //3.生成信息集合
        int index = 0;
        List<StCExpressAreaItemDO> expressAreaItemList = Lists.newArrayList();
        for (Map<String, String> columnMap : execlList) {
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 5
                        || !"省".equals(columnMap.get(ROW_STR  + index + CELL_STR + 0))
                        || !"市".equals(columnMap.get(ROW_STR + index + CELL_STR + 1))
                        || !"区".equals(columnMap.get(ROW_STR + index + CELL_STR + 2))
                        || !"排除区域".equals(columnMap.get(ROW_STR + index + CELL_STR + 3))
                        || !"是否到达".equals(columnMap.get(ROW_STR+ index + CELL_STR + 4))) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("导入文件与模板不符,请重新下载模板后导入");
                    return vh;
                }
            } else {
                // 组装model
                StCExpressAreaItemDO importExpressAreaItem = getImportExpressAreaItemModel(index, columnMap);
                expressAreaItemList.add(importExpressAreaItem);
            }
            index++;
        }
        return expressAreaItemImportCmd.importExpressAreaItem(objid, expressAreaItemList, user);
    }

    private StCExpressAreaItemDO getImportExpressAreaItemModel(int index, Map<String, String> columnMap){
        StCExpressAreaItemDO expressAreaItem = new StCExpressAreaItemDO();
        expressAreaItem.setCpCRegionProvinceEname(columnMap.get(ROW_STR + index + CELL_STR + 0));
        expressAreaItem.setCpCRegionCityEname(columnMap.get(ROW_STR + index + CELL_STR + 1));
        expressAreaItem.setCpCRegionAreaEname(columnMap.get(ROW_STR + index + CELL_STR  + 2));
        expressAreaItem.setExclusionArea(columnMap.get(ROW_STR + index + CELL_STR  + 3));
        expressAreaItem.setIsArrive(columnMap.get(ROW_STR + index + CELL_STR  + 4));
        return expressAreaItem;
    }
}
