package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.AutoInvoiceSaveCmd;
import com.jackrain.nea.st.api.AutoInvoiceVoidCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2019/9/2 17:43
 */
@RestController
@Slf4j
@Api(value = "AutoInvoiceController", description = "自动开票策略")
public class AutoInvoiceController {

    @Autowired
    AutoInvoiceSaveCmd autoInvoiceSaveCmd;

    @Autowired
    AutoInvoiceVoidCmd autoInvoiceVoidCmd;

    @ApiOperation(value = "自动开票策略保存")
    @RequestMapping(path = "/api/cs/st/saveAutoInvoice", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveAutoInvoice(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = autoInvoiceSaveCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "自动开票策略作废")
    @RequestMapping(path = "/api/cs/st/voidAutoInvoice", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidAutoInvoice(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = autoInvoiceVoidCmd.execute(querySession);
        return result;
    }
}
