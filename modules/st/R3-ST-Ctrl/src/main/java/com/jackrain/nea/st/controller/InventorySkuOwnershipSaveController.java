package com.jackrain.nea.st.controller;

import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.st.services.SendPlanSaveCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @since : 2019-03-08
 * create at : 2019-03-08 15:55
 */

@RestController
@Slf4j
@Api(value = "InventorySkuOwnershipSaveController")
public class InventorySkuOwnershipSaveController {
    @Autowired
    private SendPlanSaveCmdImpl sendPlanSaveCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "库存条码归属策略新增")
    @RequestMapping(path = "/api/cs/st/v1/InventorySkuOwnershipSave", method = {RequestMethod.POST})
    public ValueHolder saveSendPlan(HttpServletRequest request, @RequestParam(value = "param")
            String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        ValueHolder result = sendPlanSaveCmd.execute(querySession);
        return result;
    }


}




