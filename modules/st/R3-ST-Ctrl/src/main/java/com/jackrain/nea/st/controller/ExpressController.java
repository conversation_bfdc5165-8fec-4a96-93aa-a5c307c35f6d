package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.api.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2019/3/7 17:43
 */
@RestController
@Slf4j
@Api(value = "ExpressController", description = "物流方案")
public class ExpressController {
    @Autowired
    ExpressSaveCmd expressSaveCmd;

    @ApiOperation(value = "物流方案保存")
    @RequestMapping(path = "/saveExpress", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveExpress(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = expressSaveCmd.execute(querySession);
        return result;
    }

    @Autowired
    ExpressVoidCmd expressVoidCmd;

    @ApiOperation(value = "物流方案作废")
    @RequestMapping(path = "/voidExpress", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidExpress(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = expressVoidCmd.execute(querySession);
        return result;
    }

    @Autowired
    ExpressDelCmd expressDelCmd;
    @ApiOperation(value = "物流方案删除")
    @RequestMapping(path = "/delExpress", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder delExpress(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = expressDelCmd.execute(querySession);
        return result;
    }

    @Autowired
    ExpressAuditCmd expressAuditCmd;
    @ApiOperation(value = "物流方案审核")
    @RequestMapping(path = "/auditExpress", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder auditExpress(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = expressAuditCmd.execute(querySession);
        return result;
    }

    @Autowired
    ExpressCancleAuditCmd expressCancleAuditCmd;
    @ApiOperation(value = "物流方案反审核")
    @RequestMapping(path = "/cancleAuditExpress", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder cancleAuditExpress(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = expressCancleAuditCmd.execute(querySession);
        return result;
    }

    @Autowired
    ExpressFinishCmd expressFinishCmd;
    @ApiOperation(value = "物流方案结案")
    @RequestMapping(path = "/funishExpress", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder funishExpress(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = expressFinishCmd.execute(querySession);
        return result;
    }

    @Autowired
    ExpressQueryServiceCmd expressQueryServiceCmd;
    @ApiOperation(value = "物流方案-查询")
    @RequestMapping(path = "/api/cs/st/expressQueryService", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 expressQueryService(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolderV14 holder = new ValueHolderV14();
        holder.setCode(ResultCode.SUCCESS);
        holder.setMessage("success");
        try{
            holder.setData(expressQueryServiceCmd.selectExpressResult());
        }catch(Exception e){
            log.error(LogUtil.format("物流方案查询异常：{}") , Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("fail");
        }
        return holder;
    }
}
