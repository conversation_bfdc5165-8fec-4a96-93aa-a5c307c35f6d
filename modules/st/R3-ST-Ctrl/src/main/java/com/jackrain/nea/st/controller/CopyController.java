package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.SyncStockStrategyCopyCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 方案复制设置弹窗 确定按钮公用controller
 *
 * @author: huang.zaizai
 * @since: 2019-08-19
 * @create at : 2019-08-19 10:01
 */
@Api(value = "CopyCtrl", description = "复制设置确定按钮(public)")
@Slf4j
@RestController
public class CopyController {

    @Autowired
    private SyncStockStrategyCopyCmd syncStockStrategyCopyCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "延期设置确定按钮")
    @RequestMapping(value = "/api/cs/st/copyButtonFunction", method = RequestMethod.POST)
    public JSONObject copyButtonFunction(HttpServletRequest request, @RequestBody JSONObject obj) {
        JSONObject jsonObject = null;
        //复制按钮信息
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("copyButtonFunction.ReceiveParams=") + obj);
        }
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj != null) {
            String tableName = obj.getString("tablename").toUpperCase();
            //ST_C_LOCK_SKU_STRATEGY 店铺锁库条码特殊设置 主表
            try {
                switch (tableName.toUpperCase()) {
                    case "ST_C_SYNC_STOCK_STRATEGY":
                        vh = syncStockStrategyCopyCmd.execute(obj, user);
                        break;
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("Finish copyButtonFunction. Return result=") + vh.toJSONObject());
                }
                return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                        SerializerFeature.WriteMapNullValue));
            } catch (NDSException e) {
                vh = new ValueHolderV14();
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage(e.getMessage()));
                return vh.toJSONObject();
            }
        } else {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("无数据"));
        }
        return vh.toJSONObject();

    }
}
