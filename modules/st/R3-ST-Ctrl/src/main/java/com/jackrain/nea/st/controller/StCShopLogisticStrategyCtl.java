package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.model.vo.StCShopLogisticStrategyImpVo;
import com.jackrain.nea.st.services.StCShopLogisticStrategyImportCmdImpl;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * @program: r3-st
 * @description: 物流物流设置导入
 * @author: caomalai
 * @create: 2022-06-27 17:34
 **/
@RestController
@Api(value = "StCShopLogisticStrategyImportCtl", description = "物流物流设置导入")
@Slf4j
public class StCShopLogisticStrategyCtl {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private StCShopLogisticStrategyImportCmdImpl stCShopLogisticStrategyImportCmd;

    @ApiOperation(value = "获取导入模板地址")
    @RequestMapping(path = "/api/cs/st/shopLogisticStrategy/template/url", method = RequestMethod.GET)
    public ValueHolderV14 queryInvoiceImportUrl(){
        ValueHolderV14 vh = stCShopLogisticStrategyImportCmd.queryTemplateDownloadUrl();
        return vh;
    }

    @ApiOperation(value = "导入")
    @RequestMapping(value = "/api/cs/st/shopLogisticStrategy/import", method = RequestMethod.POST)
    public ValueHolderV14 selectLogData(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("店铺物流设置导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        return asyncImport(asyncTaskBody, user, file);
    }

    public ValueHolderV14 asyncImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        CompletableFuture.runAsync(() -> {
            try {
                if (file == null) {
                    throw new NDSException("导入文件不能为空!");
                }
                InputStream inputStream = file.getInputStream();
                Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
                if (hssfWorkbook.getNumberOfSheets() != 1) {
                    throw new NDSException("导入模板不正确");
                }
                List<StCShopLogisticStrategyImpVo> invoiceImpVos = getImportDataList(hssfWorkbook);
                if (CollectionUtils.isEmpty(invoiceImpVos)) {
                    throw new NDSException("导入数据不能为空!");
                }
//                if (invoiceImpVos.size() > 2000) {
//                    throw new NDSException("导入条数请勿超过2000！");
//                }

                ValueHolderV14 valueHolder = stCShopLogisticStrategyImportCmd.importInvoiceList(invoiceImpVos, user);

                retMap.put("code", ResultCode.SUCCESS);
                retMap.put("data", valueHolder.getData());
                retMap.put("path", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskBody.setTaskType("导出");
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception ex) {
                log.error("导入异常",ex);
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + ex.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("店铺物流设置导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }


    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public List<StCShopLogisticStrategyImpVo> getImportDataList(Workbook hssfWorkbook) {
        List<StCShopLogisticStrategyImpVo> ipBVoicesImpVoList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();

        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;

        for (Map<String, String> columnMap : execlList) {
            StCShopLogisticStrategyImpVo ipBVoicesImpVo = new StCShopLogisticStrategyImpVo();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 8
                        || !"店铺".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"物流公司".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"四级分类".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"商品".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"省".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"市".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"仓库".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"卖家备注".equals(columnMap.get(rowStr + index + cellStr + 7))
                ) {

                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))) {
                    StCShopLogisticStrategyImpVo ipBInvoiceImpVo = StCShopLogisticStrategyImpVo.importCreate(index, ipBVoicesImpVo, columnMap);
                    ipBVoicesImpVoList.add(ipBInvoiceImpVo);
                }
            }
            index++;
        }
        return ipBVoicesImpVoList;
    }



    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }
}
