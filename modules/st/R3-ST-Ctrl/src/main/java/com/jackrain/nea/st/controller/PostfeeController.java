package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.*;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Descroption 运费方案
 * <AUTHOR>
 * @Date 2019/3/11 21:56
 */
@RestController
@Slf4j
@Api(value = "PostfeeController", description = "运费方案接口")
public class PostfeeController {

    @Autowired
    private PostfeeSaveCmd postfeeSaveCmd;

    @Autowired
    private PostfeeDelCmd postfeeDelCmd;

    @Autowired
    private PostfeeAuditCmd postfeeAuditCmd;

    @Autowired
    private PostfeeCancelCmd postfeeCancelCmd;

    @Autowired
    private PostfeeFinishCmd postfeeFinishCmd;

    @Autowired
    private PostfeeVoidCmd postfeeVoidCmd;

    @ApiOperation(value = "新增接口-运费方案")
    @RequestMapping(path = "/api/cs/st/addPostfee", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder addPostfee(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = postfeeSaveCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "删除接口-运费方案")
    @RequestMapping(path = "/api/cs/st/delPostfee", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder delPostfee(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = postfeeDelCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "审核接口-运费方案")
    @RequestMapping(path = "/api/cs/st/auditPostfee", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder auditPostfee(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = postfeeAuditCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "反审接口-运费方案")
    @RequestMapping(path = "/api/cs/st/cancelPostfee", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder cancelPostfee(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = postfeeCancelCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "结案接口-运费方案")
    @RequestMapping(path = "/api/cs/st/finishPostfee", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder finishPostfee(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = postfeeFinishCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "作废接口-运费方案")
    @RequestMapping(path = "/api/cs/st/voidPostfee", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidPostfee(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = postfeeVoidCmd.execute(querySession);
        return result;
    }
}
