package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.model.result.SendRuleAddressRankResult;
import com.jackrain.nea.st.model.result.SendRuleWarehouseInfoResult;
import com.jackrain.nea.st.model.table.StCSendRuleAddressRentDO;
import com.jackrain.nea.st.model.table.StCSendRuleWarehouseRateDO;
import com.jackrain.nea.st.services.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 陈俊明
 * @since : 2019-03-11
 * create at : 2019-03-11 18:46
 */

@RestController
@Slf4j
@Api(value = "SendRuleController", description = "订单派单规则")
public class SendRuleController extends StBaseImportController {

    @Autowired
    private SendRuleQueryCmdImpl queryCmd;

    @Autowired
    private SendRuleWarehouseExportCmdImpl exportCmd;

    @Autowired
    private SendRuleWarehouseImportCmdImpl importCmd;

    @Autowired
    private SendRuleSaveCmdImpl sendRuleSaveCmd;

    @Autowired
    private SendRuleDelCmdImpl sendRuleDelCmd;

    @Autowired
    private SendRuleVoidCmdImpl sendRuleVoidCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "订单派单规则添加保存")
    @RequestMapping(path = "/api/cs/st/saveSendRule", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveSendRule(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
//        User user = (User) Security4Utils.getUser("root");
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = sendRuleSaveCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "订单派单规则删除")
    @RequestMapping(path = "/api/cs/st/delSendRule", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder delSendRule(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
//        User user = (User) Security4Utils.getUser("root");
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = sendRuleDelCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "订单派单规则作废")
    @RequestMapping(path = "/api/cs/st/voidSendRule", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidSendRule(HttpServletRequest request, @RequestParam(value = "param", required = true)
            String param) {
        ValueHolder valueHolder = new ValueHolder();
//        User user = (User) Security4Utils.getUser("root");
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = sendRuleVoidCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "获取订单派单规则主表信息")
    @RequestMapping(value = "/api/cs/st/getSendRuleMain", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject querySendRule(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = queryCmd.querySendRule(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "获取订单派单规则省市树")
    @RequestMapping(value = "/api/cs/st/getSendRuleTree", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject querySendRuleTree(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = queryCmd.querySendRuleTree(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "获取订单派单规则唯品会仓库树")
    @RequestMapping(value = "/api/cs/st/getSendRuleVipWarehouseTree", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject querySendRuleVipWarehouseTree(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = queryCmd.querySendRuleVipWarehouseTree(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "获取唯品会仓库优先级明细数据")
    @RequestMapping(value = "/api/cs/st/getVipWarehouseRankResultTable", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject queryVipWarehouseRankResultTable(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = queryCmd.queryVipWarehouseRankResultTable(obj);
        return JSONObject.parseObject(JSONObject.toJSONString(vh));
    }

    @ApiOperation(value = "获取省市仓库优先级明细数据")
    @RequestMapping(value = "/api/cs/st/getWarehouseRankResultTable", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject queryRankResultTable(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = queryCmd.queryRankResultTable(obj);
        return JSONObject.parseObject(JSONObject.toJSONString(vh));
    }

    @ApiOperation(value = "获取仓库信息数据")
    @RequestMapping(value = "/api/cs/st/getSendRuleWarehouseInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject queryWarehouseInfo(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = queryCmd.queryWarehouseInfo(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "获取分仓比例明细数据")
    @RequestMapping(value = "/api/cs/st/getWarehouseRateResultTable", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject queryRateResultTable(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = queryCmd.queryRateResultTable(obj);
        return JSONObject.parseObject(JSONObject.toJSONString(vh));
    }

    @ApiOperation(value = "获取地址模糊查询仓库优先级数据")
    @RequestMapping(value = "/api/cs/st/getLikeRankResultTable", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject queryLikeRankResultTable(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = queryCmd.queryLikeRankResultTable(obj);
        return JSONObject.parseObject(JSONObject.toJSONString(vh));
    }

    @ApiOperation(value = "仓库优先级列表导出")
    @RequestMapping(path = "/api/cs/st/exportSendRuleWarehouseRank", method = RequestMethod.POST)
    public ValueHolderV14 exportSendRuleWarehouseRank(HttpServletRequest request,@RequestBody JSONObject obj){
//        User user = Security4Utils.getUser("root");
        UserImpl user = (UserImpl)r3PrimWebAuthService.getLoginPrimWebUser(request);
        return exportCmd.exportSendRuleWarehouseRank(obj, user);
    }

    @ApiOperation(value = "仓库优先级明细下载导入模板")
    @RequestMapping(path = "/api/cs/st/downloadSendRuleWarehouseRank", method = {RequestMethod.POST})
    public ValueHolderV14 downloadSendRuleWarehouseRank(HttpServletRequest request,@RequestBody JSONObject obj) {
//        User user = Security4Utils.getUser("root");
        UserImpl user = (UserImpl)r3PrimWebAuthService.getLoginPrimWebUser(request);
        return importCmd.downloadRankTemp(obj, user);
    }

    @ApiOperation(value = "仓库优先级明细导入接口")
    @RequestMapping(path = "/api/cs/st/importSendRuleWarehouseRank", method = RequestMethod.POST)
    public ValueHolderV14 importSendRuleWarehouseRank(HttpServletRequest request,
                                                       @RequestParam(value = "file", required = true) MultipartFile file,
                                                       @RequestParam(value = "objid", required = true) Long objid) {
//        User user = Security4Utils.getUser("root");
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //1.传入数据校验
        ValueHolderV14 vh = new ValueHolderV14();
        if (file == null || objid == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不能为空!");
            return vh;
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            log.error(LogUtil.format("导入文件装换成流失败！错误信息为：{}") , Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件转换成流失败!");
            return vh;
        }
        //2.解析Excel
        Workbook workbook = null;
        try {
            workbook = getWorkbookForImportFile(inputStream,file);
        } catch (IOException e) {
            log.error(LogUtil.format("解析Excel失败！错误信息为：{}") , Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件解析Excel失败!");
            return vh;
        }

        List<Map<String, String>> execlList = null;
        try{
            execlList = readExcel(0, workbook);
            if(execlList == null){
                throw new NDSException("读取Eexel文件失败");
            }
        }catch(Exception e){
            log.error(LogUtil.format("读取Excel文件失败！错误信息为：{}") , Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
        //3.生成信息集合
        JSONObject obj = new JSONObject();
        obj.put("objid", objid);
        obj.put("warehousenfo", "NaN");
        ValueHolderV14 warehouseVh = queryCmd.queryWarehouseInfo(obj);
        List<StCSendRuleAddressRentDO> rentList = Lists.newArrayList();
        if (!warehouseVh.isOK()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("获取仓库信息失败，请重试！");
            return vh;
        } else {
            SendRuleWarehouseInfoResult warehouseResult = (SendRuleWarehouseInfoResult) warehouseVh.getData();
            rentList = warehouseResult.getSendRuleAddressRents();
            if (CollectionUtils.isEmpty(rentList)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("仓库为空，请先设置仓库信息！");
                return vh;
            }
        }

        int index = 0;
        int columnSize = rentList.size() + 1;
        List<SendRuleAddressRankResult> rankResultList = Lists.newArrayList();
        for (Map<String, String> columnMap : execlList) {
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != columnSize
                        || !"省".equals(columnMap.get(ROW_STR  + index + CELL_STR + 0))) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("导入文件与模板不符,请重新下载模板后导入");
                    return vh;
                }
                int i = 1;
                for (StCSendRuleAddressRentDO rend : rentList) {
                    if (!rend.getCpCPhyWarehouseEname().equals(columnMap.get(ROW_STR  + index + CELL_STR + i))) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage("导入文件与模板不符,请重新下载模板后导入");
                        return vh;
                    }
                    i++;
                }
            } else {
                // 组装model
                SendRuleAddressRankResult rankResult = getImportRankResultModel(index, columnMap, rentList);
                rankResultList.add(rankResult);
            }
            index++;
        }
        return importCmd.importSendRuleWarehouseRank(objid, rankResultList, user);
    }

    private SendRuleAddressRankResult getImportRankResultModel(int index, Map<String, String> columnMap,
                                                                  List<StCSendRuleAddressRentDO> rentList){
        SendRuleAddressRankResult rankResult = new SendRuleAddressRankResult();
        rankResult.setCpCRegionProvinceEname(columnMap.get(ROW_STR + index + CELL_STR + 0));
        int i = 1;
        for (StCSendRuleAddressRentDO rent : rentList) {
            try {
                Method method = SendRuleAddressRankResult.class.getMethod("setRank" + i, new Class[]{String.class});
                if (columnMap.get(ROW_STR + index + CELL_STR + i) != null) {
                    method.invoke(rankResult, columnMap.get(ROW_STR + index + CELL_STR + i));
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("仓库优先级设置失败：{}") , Throwables.getStackTraceAsString(ex));
            }
            i++;
        }
        return rankResult;
    }

    @ApiOperation(value = "仓库比例列表导出")
    @RequestMapping(path = "/api/cs/st/exportSendRuleWarehouseRate", method = RequestMethod.POST)
    public ValueHolderV14 exportSendRuleWarehouseRate(HttpServletRequest request,@RequestBody JSONObject obj){
//        User user = Security4Utils.getUser("root");
        UserImpl user = (UserImpl)r3PrimWebAuthService.getLoginPrimWebUser(request);
        return exportCmd.exportSendRuleWarehouseRate(obj, user);
    }

    @ApiOperation(value = "仓库比例明细下载导入模板")
    @RequestMapping(path = "/api/cs/st/downloadSendRuleWarehouseRate", method = {RequestMethod.POST})
    public ValueHolderV14 downloadSendRuleWarehouseRate(HttpServletRequest request,@RequestBody JSONObject obj) {
//        User user = Security4Utils.getUser("root");
        UserImpl user = (UserImpl)r3PrimWebAuthService.getLoginPrimWebUser(request);
        return importCmd.downloadRateTemp(obj, user);
    }

    @ApiOperation(value = "仓库比例明细导入接口")
    @RequestMapping(path = "/api/cs/st/importSendRuleWarehouseRate", method = RequestMethod.POST)
    public ValueHolderV14 importSendRuleWarehouseRate(HttpServletRequest request,
                                                      @RequestParam(value = "file", required = true) MultipartFile file,
                                                      @RequestParam(value = "objid", required = true) Long objid) {
//        User user = Security4Utils.getUser("root");
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //1.传入数据校验
        ValueHolderV14 vh = new ValueHolderV14();
        if (file == null || objid == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不能为空!");
            return vh;
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            log.error(LogUtil.format("导入文件装换成流失败！错误信息为：{}") , Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件转换成流失败!");
            return vh;
        }
        //2.解析Excel
        Workbook workbook = null;
        try {
            workbook = getWorkbookForImportFile(inputStream,file);
        } catch (IOException e) {
            log.error(LogUtil.format("解析Excel失败！错误信息为：{}") , Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件解析Excel失败!");
            return vh;
        }

        List<Map<String, String>> execlList = null;
        try{
            execlList = readExcel(0, workbook);
            if(execlList == null){
                throw new NDSException("读取Eexel文件失败");
            }
        }catch(Exception e){
            log.error(LogUtil.format("读取Excel文件失败！错误信息为：{}") , Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
        //3.生成信息集合
        int index = 0;
        int columnSize = 3;
        List<StCSendRuleWarehouseRateDO> rateResultList = Lists.newArrayList();
        for (Map<String, String> columnMap : execlList) {
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != columnSize
                        || !"仓库".equals(columnMap.get(ROW_STR  + index + CELL_STR + 0))
                        || !"仓库优先级".equals(columnMap.get(ROW_STR  + index + CELL_STR + 1))
                        || !"发货比例".equals(columnMap.get(ROW_STR  + index + CELL_STR + 2))) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("导入文件与模板不符,请重新下载模板后导入");
                    return vh;
                }
            } else {
                // 组装model
                StCSendRuleWarehouseRateDO rateResult = getImportRateResultModel(index, columnMap);
                rateResultList.add(rateResult);
            }
            index++;
        }
        return importCmd.importSendRuleWarehouseRate(objid, rateResultList, user);
    }

    private StCSendRuleWarehouseRateDO getImportRateResultModel(int index, Map<String, String> columnMap){
        StCSendRuleWarehouseRateDO rateResult = new StCSendRuleWarehouseRateDO();
        rateResult.setCpCPhyWarehouseEname(columnMap.get(ROW_STR + index + CELL_STR + 0));
        try {
            rateResult.setRank(columnMap.get(ROW_STR + index + CELL_STR + 1) != null ?
                    Long.valueOf(columnMap.get(ROW_STR + index + CELL_STR + 1)) : null);
            rateResult.setSendRate(columnMap.get(ROW_STR + index + CELL_STR + 2) != null ?
                    NumberUtils.createBigDecimal(columnMap.get(ROW_STR + index + CELL_STR + 2)) : null);
        } catch (Exception e) {
            rateResult.setRank(null);
            rateResult.setSendRate(null);
        }
        return rateResult;
    }
}
