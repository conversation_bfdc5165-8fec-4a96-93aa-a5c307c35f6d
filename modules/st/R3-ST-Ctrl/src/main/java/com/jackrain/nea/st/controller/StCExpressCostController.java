package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.ac.model.request.AcLogisticsFeeReCaculationRequest;
import com.jackrain.nea.ac.model.result.AcLogisticsFeeCaculationResult;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCExpressCostImportAndExportCmd;
import com.jackrain.nea.st.api.StCExpressPriceImportAndExportCmd;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.ExpressCostTypeEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.request.StCExpressCostPoi;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyPoi;
import com.jackrain.nea.st.model.result.StImportErrorMsgResult;
import com.jackrain.nea.st.model.table.StCExpressCost;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.request.ReCaculateLogisticsFeeRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTask;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/8/8 18:21
 * @Description
 */
@RestController
@Slf4j
@Api(value = "StCExpressCostController", description = "快运报价设置")
public class StCExpressCostController {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();


    @Autowired
    private StCExpressCostImportAndExportCmd stCExpressCostImportAndExportCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 测试用户
     */
    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("测试用户");
        user.setEname("test");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }


    @ApiOperation(value = "快运报价设置-批量导入")
    @RequestMapping(path = "/api/cs/st/importExpressCost", method = RequestMethod.POST)
    public ValueHolderV14 importSyncExpressCost(HttpServletRequest request,
                                                  @RequestParam(value = "file", required = true) MultipartFile file)
            throws IOException {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {

            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//            User user = getRootUser();

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" importExpressCost fileName：") + file.getOriginalFilename());
            }
            //1.传入数据校验
            if (file == null) {
                throw new NDSException(Resources.getMessage("请求参数不能为空!"));
            }
            InputStream inputStream = null;
            Map<String, InputStream> inputStreamMap = new HashMap<>();
            try {
                inputStream = file.getInputStream();
                inputStreamMap.put("inputStream", inputStream);
                log.info(LogUtil.format(" File Conversion InputStream：") + inputStream);
            } catch (Exception e) {
                log.error(LogUtil.format("File Conversion InputStream Fail：{}"), Throwables.getStackTraceAsString(e));
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("文件转换成流失败!");
                return holderV14;
            }
            //2.解析Excel
            Workbook hssfWorkbook = null;
            try {
                hssfWorkbook = getWorkbookForImportFile(inputStream, file);
            } catch (IOException e) {
                log.error(LogUtil.format("快运报价设置-导入解析Excel失败！错误信息为：{}"), Throwables.getStackTraceAsString(e));
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("文件解析Excel失败!");
                return holderV14;
            } finally {
                inputStream.close();
            }
            //获取sheet页数
            log.info(LogUtil.format(" =========>>>>>>[llf]获取sheet页数！"));
            int sheetNum = hssfWorkbook.getNumberOfSheets();
            if (sheetNum != 1) {
                throw new NDSException(Resources.getMessage("快运保价设置-模板不正确!"));
            }
            ValueHolderV14<List<StImportErrorMsgResult>> importV14 = getSyncExpressCostList(hssfWorkbook, holderV14, user);
            holderV14.setCode(importV14.getCode());
            holderV14.setMessage(importV14.getMessage());
            if (!importV14.isOK()) {
                List<StImportErrorMsgResult> errorMsgList = importV14.getData();
                if (CollectionUtils.isNotEmpty(errorMsgList)) {
                    String errorFilePath = stCExpressCostImportAndExportCmd.downloadImportErrMsg(user, errorMsgList);
                    holderV14.setData(errorFilePath);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("快运保价设置-导入Excel失败！错误信息：{}"), Throwables.getStackTraceAsString(e));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("快运保价设置-导入Excel失败！错误信息：" + e.getMessage());
            return holderV14;
        }
        return holderV14;
    }


    @ApiOperation(value = "快运保价设置-批量导出")
    @RequestMapping(path = "/api/cs/st/exportExpressCost", method = RequestMethod.POST)
    public ValueHolderV14 exportSyncExpressCost(HttpServletRequest request,
                                                  @RequestParam(value = "ids", required = true) String ids)
            throws IOException {
        if (StringUtils.isBlank(ids)) {
            ValueHolderV14<Object> holderV14 = new ValueHolderV14<>();
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请选择需要导出的数据");
            return holderV14;
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = getRootUser();

        return this.export(ids,user);
    }

    private ValueHolderV14 export(String ids,User user) {
        return stCExpressCostImportAndExportCmd.exportExpressCost(ids,user);
    }


    private ValueHolderV14<List<StImportErrorMsgResult>> importExecute(List<StCExpressCostPoi> importRequests, User user) {
        return stCExpressCostImportAndExportCmd.importExpressCost(importRequests,user);
    }


    /**
     * 获得workbook
     * @param inputStream
     * @param file
     * @return
     * @throws IOException
     */
    private Workbook getWorkbookForImportFile(InputStream inputStream, MultipartFile file) throws IOException {
        Workbook workbook = null;
        String fileName = file.getOriginalFilename();
        if (fileName.toLowerCase().endsWith("xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    /**
     * 解析获取
     * @param hssfWorkbook
     * @param vh
     * @param user
     * @return
     */
    private ValueHolderV14<List<StImportErrorMsgResult>> getSyncExpressCostList(Workbook hssfWorkbook, ValueHolderV14 vh, User user) {
        List<StCExpressCostPoi> importRequests = Lists.newArrayList();
        List<Map<String, String>> execlList;
        try {
            execlList = readExcel(0, hssfWorkbook);
            if (execlList == null) {
                throw new NDSException("读取Eexel文件失败");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("快运报价设置导入Excel文件失败！错误信息为：{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            if (index == 1) {
                // 校验excel字段
                if (columnMap.size() != 10
                        || !"仓库".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"物流公司".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"开始日期".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"结束日期".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"目的省份".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"起始重量（KG/不包含）".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"结束重量（KG/包含）".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"快递费用（元/KG）".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"起步费（元）".equals(columnMap.get(rowStr + index + cellStr + 9))
                ) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("导入文件与模板不符,请重新下载模板后导入");
                    return vh;
                }
            }
            if (index > 1) {
                StCExpressCostPoi importRequest = getImportModel(index, columnMap);
                importRequests.add(importRequest);
            }
            index++;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" excel.data：") + importRequests);
        }
        return this.importExecute(importRequests, user);
    }




    /**
     * 导入方法
     * @param sheetIndex
     * @param hssfWorkbook
     * @return
     */
    private List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;
            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }

    /**
     * 转成对象
     * @param index
     * @param columnMap
     * @return
     */
    private StCExpressCostPoi getImportModel(int index, Map<String, String> columnMap) {
        StCExpressCostPoi importRequest = new StCExpressCostPoi();
        importRequest.setWarehouseName(columnMap.get(rowStr + index + cellStr + 0));
        importRequest.setLogisticsName(columnMap.get(rowStr + index + cellStr + 1));
        importRequest.setStartDate(columnMap.get(rowStr + index + cellStr + 2));
        importRequest.setEndDate(columnMap.get(rowStr + index + cellStr + 3));
        importRequest.setRemark(columnMap.get(rowStr + index + cellStr + 4));
        importRequest.setProvinceName(columnMap.get(rowStr + index + cellStr + 5));
        importRequest.setStartWeight(columnMap.get(rowStr + index + cellStr + 6));
        importRequest.setEndWeight(columnMap.get(rowStr + index + cellStr + 7));
        importRequest.setPriceExpress(columnMap.get(rowStr + index + cellStr + 8));
        importRequest.setPriceFirstWeight(columnMap.get(rowStr + index + cellStr + 9));
        return importRequest;
    }

    @ApiOperation(value = "物流费用对账单-重新计算")
    @RequestMapping(path = "/api/cs/st/reCaculateLogisticsFee", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 reCaculateLogisticsFee(HttpServletRequest request ,@RequestBody ReCaculateLogisticsFeeRequest logisticsFeeRequest) {

        log.info(LogUtil.format("LogisticsFeeController.reCaculateLogisticsFee,param:{}", "LogisticsFeeController.reCaculateLogisticsFee"), JSON.toJSON(logisticsFeeRequest));

        ValueHolderV14<Object> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "正在进行对账单费用重算，请稍后到物流对账单查看重算结果");
        if (logisticsFeeRequest.getId() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请勾选一条报价！");
            return v14;
        }
        if (logisticsFeeRequest.getType() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("报价设置类型不能为空！");
            return v14;
        }
        if (logisticsFeeRequest.getBeginConfirmDate() == null || logisticsFeeRequest.getEndConfirmDate() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择对账日期！");
            return v14;
        }

        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = getRootUser();
        try {
            ValueHolderV14<AcLogisticsFeeReCaculationRequest> holderV14 = stCExpressCostImportAndExportCmd.queryFeeChecklistCount(logisticsFeeRequest, user);
            if (holderV14.isOK()) {

                QuerySessionImpl querySession = new QuerySessionImpl(user);

                JSONObject result = new JSONObject();
                result.put("objid", logisticsFeeRequest.getId());
                if (ExpressCostTypeEnum.UNFULLCAR.getval().equals(logisticsFeeRequest.getType())) {
                    result.put("table", "ST_C_UNFULLCAR_COST");
                }else if (ExpressCostTypeEnum.EXPRESS_PRICE.getval().equals(logisticsFeeRequest.getType())) {
                    result.put("table", "ST_C_EXPRESS_PRICE_STRATEGY");
                }else if (ExpressCostTypeEnum.EXPRESS_COST.getval().equals(logisticsFeeRequest.getType())) {
                    result.put("table", "ST_C_EXPRESS_COST");
                }else {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("报价设置类型错误");
                    return v14;
                }
                result.put("request",holderV14.getData());


                DefaultWebEvent event = new DefaultWebEvent("reCaculateLogisticsFee", request, false);
                event.put("menu", "物流费用重新计算");
                event.put("param", result);
                querySession.setEvent(event);

                StCExpressCostController bean = ApplicationContextHandle.getBean(StCExpressCostController.class);
                bean.reCaculateLogisticsFee(querySession);
            }else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(holderV14.getMessage());
            }
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请求异常：" + Throwables.getStackTraceAsString(e));
        }

        return v14;
    }

    @AsyncTask(value = "物流费用重新计算")
    public JSONObject reCaculateLogisticsFee(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        AcLogisticsFeeReCaculationRequest request = JSON.toJavaObject(param.getJSONObject("request"), AcLogisticsFeeReCaculationRequest.class);
        request.setLoginUser(user);
        return stCExpressCostImportAndExportCmd.reCaculateLogisticsFee(request).toJSONObject();
    }

}
