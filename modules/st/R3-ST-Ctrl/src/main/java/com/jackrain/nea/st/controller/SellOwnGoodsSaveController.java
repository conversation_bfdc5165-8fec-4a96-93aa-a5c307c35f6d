package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.*;
import com.jackrain.nea.st.model.table.StCSellOwngoodsDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@Slf4j
@Api(value = "StCSellOwnGoodsSaveController", description = "经销商自有商品controller")
public class SellOwnGoodsSaveController {

    @Autowired
    SellOwnGoodsSaveCmd sellOwnGoodsSaveCmd;

    @Autowired
    SellOwnGoodsCopyCmd sellOwnGoodsCopyCmd;

    @Autowired
    private SellOwnGoodsDeleteCmd sellOwnGoodsDeleteCmd;

    @Autowired
    private SellOwnGoodsAuditCmd sellOwnGoodsAuditCmd;

    @Autowired
    private SellOwnGoodsReverseAuditCmd sellOwnGoodsReverseAuditCmd;

    @Autowired
    private SellOwnGoodsVoidCmd sellOwnGoodsVoidCmd;

    @ApiOperation(value = "自有商品主表保存")
    @RequestMapping(path = "/saveSellOwnGoods", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveSellOwnGoods(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = sellOwnGoodsSaveCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "自有商品主表删除")
    @RequestMapping(path = "/deleteSellOwnGoods", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder deleteSellOwnGoods(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = sellOwnGoodsDeleteCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "自有商品主表审核")
    @RequestMapping(path = "/auditSellOwnGoods", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder aduitSellOwnGoods(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = sellOwnGoodsAuditCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "自有商品主表结案")
    @RequestMapping(path = "/closeSellOwnGoods", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder closeSellOwnGoods(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = sellOwnGoodsAuditCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "自有商品主表反审核")
    @RequestMapping(path = "/unAuditSellOwnGoods", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder unAuditSellOwnGoods(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = sellOwnGoodsReverseAuditCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "自有商品主表作废")
    @RequestMapping(path = "/voidSellOwnGoods", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidSellOwnGoods(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = sellOwnGoodsVoidCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "分销代销方案复制")
    @RequestMapping(path = "/api/cs/st/sellOwnGoodsCopy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14<List<StCSellOwngoodsDO>> sellOwnGoodsCopy(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        return  sellOwnGoodsCopyCmd.execute(querySession);
    }
}
