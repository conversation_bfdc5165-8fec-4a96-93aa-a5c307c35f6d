package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.ShopStrategyDelCmd;
import com.jackrain.nea.st.api.ShopStrategySaveCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2019/3/8 16:25
 */
@RestController
@Slf4j
@Api(value = "ShopStrategyController", description = "店铺策略")
public class ShopStrategyController {

    @Autowired
    private ShopStrategySaveCmd shopStrategySaveCmd;

    @Autowired
    private ShopStrategyDelCmd shopStrategyDelCmd;

    @ApiOperation(value = "店铺策略添加保存")
    @RequestMapping(path = "/api/cs/st/saveShopStrategy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveResolveRule(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = shopStrategySaveCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "删除店铺策略")
    @RequestMapping(path = "/api/cs/st/delShopStragety", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder delShopStragety(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = shopStrategyDelCmd.execute(querySession);
        return result;
    }
}
