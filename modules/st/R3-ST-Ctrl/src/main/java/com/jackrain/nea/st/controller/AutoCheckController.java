package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.model.enums.AutoCheckProLevelEnum;
import com.jackrain.nea.st.model.table.StCAutoCheckAutoTimeDO;
import com.jackrain.nea.st.model.vo.StCAutocheckExcludeProductImpVo;
import com.jackrain.nea.st.services.AutocheckDelCmdImpl;
import com.jackrain.nea.st.services.AutocheckQueryCmdImpl;
import com.jackrain.nea.st.services.AutocheckSaveCmdImpl;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;

/**
 * @Descroption 订单自动审核策略
 * <AUTHOR>
 * @Date 2019/3/12 18:50
 */
@RestController
@Slf4j
@Api(value = "AutoCheckController", description = "订单自动审核策略")
public class AutoCheckController {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();

    @Autowired
    private AutocheckSaveCmdImpl autoCheckSaveCmd;

    @Autowired
    private AutocheckDelCmdImpl autoCheckDelCmd;

    @Autowired
    private AutocheckQueryCmdImpl autocheckQueryLogCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;

    @ApiOperation(value = "新增接口-订单自动审核策略")
    @PostMapping(path = "/api/cs/st/v1/addAutoCheck")
    public ValueHolder addAutoCheck(HttpServletRequest request, @RequestBody JSONObject param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", param);
        querySession.setEvent(event);
        return autoCheckSaveCmd.execute(querySession);
    }

    @ApiOperation(value = "删除接口-订单自动审核策略")
    @PostMapping(path = "/api/cs/st/v1/delAutoCheck")
    public ValueHolder delAutoCheck(HttpServletRequest request, @RequestBody JSONObject param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", param);
        querySession.setEvent(event);
        return autoCheckSaveCmd.execute(querySession);
    }

    @ApiOperation(value = "作废接口-订单自动审核策略")
    @PostMapping(path = "/api/cs/st/v1/voidAutoCheck")
    public ValueHolder voidAutoCheck(HttpServletRequest request, @RequestBody JSONObject param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", param);
        querySession.setEvent(event);
        return autoCheckSaveCmd.execute(querySession);
    }

    @ApiOperation(value = "查询接口-订单自动审核策略")
    @PostMapping(path = "/api/cs/st/v1/getAutoCheck")
    public ValueHolder getAutoCheck(@RequestParam(value = "id") Long id) {
        if (log.isDebugEnabled()) {
            log.info(LogUtil.format("getAutoCheck.inputParam>>>{}", id));
        }
        ValueHolder result = autoCheckSaveCmd.getAutoCheck(id);

        if (log.isDebugEnabled()) {
            log.info(LogUtil.format("getAutoCheck.outputParam>>>{}"), JSON.toJSONString(result));
        }
        return result;
    }

    @ApiOperation(value = "保存接口-批量保存自动审核策略")
    @PostMapping(path = "/api/cs/st/v1/batchUpdateAutoCheck")
    public ValueHolderV14<JSONArray> batchUpdateAutoCheck(HttpServletRequest request, @RequestBody JSONObject param) {
        ValueHolderV14<JSONArray> v14 = new ValueHolderV14<>();
        try {
            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
            if (log.isDebugEnabled()) {
                log.info(LogUtil.format("batchUpdateAutoCheck.inputParam#{}"), param);
            }
            String ids = param.getString("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (StringUtils.isBlank(ids)) {
                throw new NDSException("至少勾选一条需要修改的策略");
            }
            if (fixColumn == null || fixColumn.isEmpty()) {
                throw new NDSException("请填写需要修改的字段");
            }
            String[] idArray = ids.split(",");
            List<Long> idList = new ArrayList<>();
            for (String id : idArray) {
                idList.add(Long.valueOf(id));
            }
            v14 = autoCheckSaveCmd.batchUpdateAutoCheck(fixColumn, idList, user);
        } catch (Exception e) {
            log.error(LogUtil.format("批量修改自动审核策略异常：{}"), Throwables.getStackTraceAsString(e));
            v14.setMessage(e.getMessage());
            v14.setCode(ResultCode.FAIL);
        }
        return v14;
    }

    @ApiOperation(value = "保存接口-批量保存自动审核策略")
    @PostMapping(path = "/api/cs/st/v1/batchUpdateAutoCheckTime")
    public ValueHolderV14<JSONArray> batchUpdateAutoCheckTime(HttpServletRequest request, @RequestBody JSONObject param) {
        ValueHolderV14<JSONArray> v14 = new ValueHolderV14<>();
        try {
            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
            if (log.isDebugEnabled()) {
                log.info(LogUtil.format("batchUpdateAutoCheckTime.inputParam#{}"), param);
            }
            String ids = param.getString("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (StringUtils.isBlank(ids)) {
                throw new NDSException("至少勾选一条需要修改的策略");
            }
            if (fixColumn == null || fixColumn.isEmpty()) {
                throw new NDSException("请填写需要修改的字段");
            }
            String[] idArray = ids.split(",");
            List<Long> idList = new ArrayList<>();
            for (String id : idArray) {
                idList.add(Long.valueOf(id));
            }
            v14 = autoCheckSaveCmd.batchUpdateAutoCheckTime(fixColumn, idList, user);
        } catch (Exception e) {
            log.error(LogUtil.format("批量修改自动审核策略时间异常：{}"), Throwables.getStackTraceAsString(e));
            v14.setMessage(e.getMessage());
            v14.setCode(ResultCode.FAIL);
        }
        return v14;
    }

    @ApiOperation(value = "订单自动审核策略操作日志查询")
    @PostMapping(path = "/api/cs/st/v1/queryAutoCheckLog")
    public ValueHolderV14 queryAutoCheckLog(HttpServletRequest request, @RequestBody JSONObject param) {
        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            v14 = autocheckQueryLogCmd.getAutoCheckLog(param);
        } catch (Exception e) {
            log.error(LogUtil.format("订单自动审核策略查询日志异常：{}"), Throwables.getStackTraceAsString(e));
            v14.setMessage(e.getMessage());
            v14.setCode(ResultCode.FAIL);
        }
        return v14;
    }

    @ApiOperation(value = "订单自动审核策略批量删除排除商品")
    @PostMapping(path = "/api/cs/st/v1/deleteExcludePro")
    public ValueHolderV14 deleteExcludePro(@RequestBody JSONObject param) {
        ValueHolderV14 v14 = autoCheckDelCmd.deleteExcludePro(param);
        return v14;
    }

    @ApiOperation(value = "订单自动审核策略保存时间")
    @PostMapping(path = "/api/cs/st/v1/saveAutoCheckTime")
    public ValueHolderV14 saveAutoCheckTime(HttpServletRequest request, @RequestBody StCAutoCheckAutoTimeDO param){
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 v14 = autoCheckSaveCmd.saveAutoCheckTime(param,user);
        return v14;
    }

    @ApiOperation(value = "订单自动审核策略审核时间删除")
    @PostMapping(path = "/api/cs/st/v1/deleteAutoCheckTime")
    public ValueHolderV14 deleteAutoCheckTime(@RequestBody JSONObject param) {
        ValueHolderV14 v14 = autoCheckDelCmd.deleteAutoCheckTime(param);
        return v14;
    }

    @ApiOperation(value = "订单自动审核策略查询审核时间列表")
    @PostMapping(path = "/api/cs/st/v1/queryAutoCheckTimeList")
    public ValueHolderV14 queryAutoCheckTimeList(@RequestBody JSONObject param){
        ValueHolderV14 v14 = autocheckQueryLogCmd.getAutoCheckTime(param);
        return v14;
    }

    @ApiOperation(value = "订单自动审核策略排除商品导入模板")
    @GetMapping(path = "/api/cs/st/v1/excludeProductTemplate/url")
    public ValueHolderV14 getExcludeProductTemplate(HttpServletRequest request){
        UserImpl user = (UserImpl)r3PrimWebAuthService.getLoginPrimWebUser(request);
        return autocheckQueryLogCmd.downloadTemp(user);
    }
    @ApiOperation(value = "订单自动审核策略排除商品导入")
    @RequestMapping(path = "/api/cs/st/autoCkeckExcludeProduct/import", method = RequestMethod.POST)
    public ValueHolderV14 importWarehouseLogisticsRank(HttpServletRequest request,
                                                       @RequestParam(value = "file", required = true) MultipartFile file,
                                                       @RequestParam(value = "objid") String objid){
        ValueHolderV14 holderV14 = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody =new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("店铺物流设置导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        Map<Object, Object> retMap = new LinkedHashMap<>();
        try {
            if (file == null) {
                throw new NDSException("导入文件不能为空!");
            }
            InputStream inputStream = file.getInputStream();
            Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
            if (hssfWorkbook.getNumberOfSheets() != 1) {
                throw new NDSException("导入模板不正确");
            }
            List<StCAutocheckExcludeProductImpVo> dataImpVos = getImportDataList(hssfWorkbook);
            if (CollectionUtils.isEmpty(dataImpVos)) {
                throw new NDSException("导入数据不能为空!");
            }
            if (dataImpVos.size() > 2000) {
                throw new NDSException("导入条数请勿超过2000！");
            }
            if(StringUtils.isBlank(objid)){
                throw new NDSException("选择策略数据");
            }
            ValueHolderV14 valueHolder =  autoCheckSaveCmd.importDataList(objid,dataImpVos, user);
            retMap.put("code", valueHolder.getCode());
            retMap.put("data", valueHolder.getData());
            retMap.put("message", valueHolder.getMessage());
            //任务完成
            if(valueHolder.getCode() == ResultCode.FAIL){
                asyncTaskBody.setUrl(autoCheckSaveCmd.exportResut(dataImpVos, user));
            }
            asyncTaskBody.setTaskType("导入");
            asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            return valueHolder;
        }catch (Exception ex) {
            retMap.put("code", ResultCode.FAIL);
            retMap.put("message", "导入异常：" + ex.getMessage());
            asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage(Resources.getMessage("自动审核策略排除商品导入异常"));
            return holderV14;
        }
    }



    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public List<StCAutocheckExcludeProductImpVo> getImportDataList(Workbook hssfWorkbook) {
        List<StCAutocheckExcludeProductImpVo> ipBVoicesImpVoList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();

        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;

        for (Map<String, String> columnMap : execlList) {
            StCAutocheckExcludeProductImpVo ipBVoicesImpVo = new StCAutocheckExcludeProductImpVo();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 7
                        || !"商品级别".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"品类".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"商品编码".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"商品条码".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"平台商品ID".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"平台条码ID".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 6))
                ) {

                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))) {
                    StCAutocheckExcludeProductImpVo ipBInvoiceImpVo = StCAutocheckExcludeProductImpVo.importCreate(index, ipBVoicesImpVo, columnMap);
                    //转换枚举值
                    if(StringUtils.isNotBlank(ipBInvoiceImpVo.getProLevelName())){
                        ipBInvoiceImpVo.setProLevel(AutoCheckProLevelEnum.getByDesc(ipBInvoiceImpVo.getProLevelName()).getKey());
                    }
                    ipBVoicesImpVoList.add(ipBInvoiceImpVo);
                }
            }
            index++;
        }
        return ipBVoicesImpVoList;
    }



    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }
}
