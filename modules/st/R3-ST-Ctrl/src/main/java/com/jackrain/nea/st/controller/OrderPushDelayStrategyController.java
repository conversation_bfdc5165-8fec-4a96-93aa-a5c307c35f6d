package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.StCOrderPushDelayStrategyDelCmd;
import com.jackrain.nea.st.api.StCOrderPushDelayStrategySaveCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Descroption 订单推单延时策略
 * <AUTHOR>
 * @Date 2020/07/04 20:17
 */
@RestController
@Slf4j
@Api(value = "OrderPushDelayStrategyController", description = "订单推单延时策略")
public class OrderPushDelayStrategyController {
    @Autowired
    private StCOrderPushDelayStrategySaveCmd orderPushDelayStrategySaveCmd;

    @Autowired
    private StCOrderPushDelayStrategyDelCmd orderPushDelayStrategyDelCmd;

    @ApiOperation(value = "添加订单推单延时策略")
    @RequestMapping(path = "/api/cs/st/orderPushDelayStrategySaveCmd", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder orderPushDelayStrategySaveCmd(HttpServletRequest request, @RequestBody JSONObject param) {
        log.debug(LogUtil.format("Start OrderPushDelayStrategyController.orderPushDelayStrategySaveCmd. " +
                "ReceiveParams: {}"), param);
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param",param);
        querySession.setEvent(event);
        ValueHolder result = orderPushDelayStrategySaveCmd.execute(querySession);
        log.debug(LogUtil.format("Finish OrderPushDelayStrategyController.orderPushDelayStrategySaveCmd. " +
                "ReceiveParams: {}"), result);
        return result;
    }

    @ApiOperation(value = "删除订单推单延时策略")
    @RequestMapping(path = "/api/cs/st/orderPushDelayStrategyDelCmd", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder orderPushDelayStrategyDelCmd(HttpServletRequest request, @RequestBody JSONObject param) {
        log.debug(LogUtil.format("Start OrderPushDelayStrategyController.orderPushDelayStrategyDelCmd. ReceiveParams:" +
                " {}"), param);
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param",param);
        querySession.setEvent(event);
        ValueHolder result = orderPushDelayStrategyDelCmd.execute(querySession);
        log.debug(LogUtil.format("Finish OrderPushDelayStrategyController.orderPushDelayStrategyDelCmd. " +
                "ReceiveParams: {}"), result);
        return result;
    }
}
