package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2020/06/10
 */
@RestController
@Slf4j
@Api(value = "LiveCastStrategyController", description = "直播解析策略")
//@RequestMapping(path = "/api/cs/st/liveCastStrategy/v1")
@RequestMapping(path = "/api/cs/st/liveCastStrategy/v1") // 暂时使用老规范
public class LiveCastStrategyController {


    @Autowired
    private LiveCastStrategyQueryListCmd liveCastStrategyQueryListCmd;

    @Autowired
    private LiveCastStrategyDeleteCmd liveCastStrategyDeleteCmd;

    @Autowired
    private LiveCastStrategyVoidCmd liveCastStrategyVoidCmd;

    @Autowired
    private LiveCastStrategyApproveCmd liveCastStrategyApproveCmd;

    @Autowired
    private LiveCastStrategyFinishCmd liveCastStrategyFinishCmd;

    @Autowired
    private LiveCastStrategyUpdateEndTimeCmd liveCastStrategyUpdateEndTimeCmd;

    @Autowired
    private LiveCastStrategySaveCmd liveCastStrategySaveCmd;

    /**
     * QuerySession
     * @param request
     * @param param
     * @return
     */
    private QuerySession buildSession(HttpServletRequest request, String param) {
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);

        return querySession;
    }

    /**
     * 测试用
     * @param request
     * @param param
     * @return
     */
//    private QuerySession buildSession(HttpServletRequest request, String param) {
//        UserImpl user = new UserImpl();
//        user.setPhone("113080433327");
//        user.setClientName("local");
//        user.setCpCstoreId(1);
//        user.setStoreId(1);
//        user.setStoreName("cang");
//        user.setSupClientId(11);
//        user.setName("debug001");
//        user.setEname("debuge001");
//        user.setId(998);
//        user.setActive(true);
//        user.setDescription("no description for debug no more.");
//
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
//        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
//        event.put("param", JSON.parseObject(param));
//        querySession.setEvent(event);
//
//        return querySession;
//    }


    /**
     * 保存直播解析策略
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "直播解析策略保存")
    @RequestMapping(path = "/save", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        if (log.isInfoEnabled()) {
            log.info(LogUtil.format("saveStrategy.param:{}"), param);
        }

        QuerySession session = buildSession(request, param);
        ValueHolder vh = liveCastStrategySaveCmd.execute(session);

        return vh;
    }

    /**
     * 删除策略
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "直播解析策略删除")
    @RequestMapping(path = "/delete", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder deleteStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        if (log.isDebugEnabled()) {
            log.info(LogUtil.format("deleteStrategy.param:{}"), param);
        }

        QuerySession session = buildSession(request, param);
        ValueHolder vh = liveCastStrategyDeleteCmd.execute(session);

        return vh;
    }

    /**
     * 作废策略
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "直播解析策略作废")
    @RequestMapping(path = "/void", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        if (log.isDebugEnabled()) {
            log.info(LogUtil.format("voidStrategy.param:{}"), param);
        }

        QuerySession session = buildSession(request, param);
        ValueHolder vh = liveCastStrategyVoidCmd.execute(session);

        return vh;
    }

    /**
     * 审核策略
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "直播解析策略审核")
    @RequestMapping(path = "/approve", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder approveStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        if (log.isDebugEnabled()) {
            log.info(LogUtil.format("approveStrategy.param:{}"), param);
        }

        QuerySession session = buildSession(request, param);
        ValueHolder vh = liveCastStrategyApproveCmd.execute(session);

        return vh;
    }

    /**
     * 策略结案
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "直播解析策略结案")
    @RequestMapping(path = "/finish", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder finishStrategy(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        if (log.isDebugEnabled()) {
            log.info(LogUtil.format("finishStrategy.param:{}"), param);
        }

        QuerySession session = buildSession(request, param);
        ValueHolder vh = liveCastStrategyFinishCmd.execute(session);

        return vh;
    }

    @ApiOperation(value = "强制更新时间")
    @RequestMapping(path = "/updateEndTime", method = {RequestMethod.POST})
    public ValueHolder updateEndTime(HttpServletRequest request, @RequestBody Object param) {
        if (log.isDebugEnabled()) {
            log.info(LogUtil.format("updateEndTime.param:{}"), param);
        }

        QuerySession session = buildSession(request, JSON.toJSONString(param));
        ValueHolder vh = liveCastStrategyUpdateEndTimeCmd.execute(session);

        return vh;
    }
    
    @ApiOperation(value = "编辑页面查询策略")
    @RequestMapping(path = "/liveCastStrategyQueryList",method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 LiveCastStrategyQueryList(@RequestParam(value = "objid", required = true) Long objid) {
        if (log.isDebugEnabled()) {
            log.info(LogUtil.format("LiveCastStrategyQueryList.param:{}"), objid);
        }
        return liveCastStrategyQueryListCmd.queryListById(objid);
    }
}
