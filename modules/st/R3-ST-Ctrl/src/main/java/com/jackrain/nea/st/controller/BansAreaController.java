package com.jackrain.nea.st.controller;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.BansAreaStrategyCmd;
import com.jackrain.nea.st.controller.utils.CtrlExportUtil;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ming.fz
 * @since : 2022-06-10
 * create at : 2022-06-10 18:46
 */

@RestController
@Slf4j
@Api(value = "BansAreaController", description = "物流禁发区域")
@RequestMapping("/api/cs/st/bansArea")
public class BansAreaController {

    @Autowired
    private BansAreaStrategyCmd bansAreaStrategyCmd;

    @Autowired
    private CtrlExportUtil exportUtil;

    @ApiOperation(value = "导入模板下载")
    @RequestMapping(path = "/download", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14<String> download(HttpServletRequest request) {
        User user = (User) Security4Utils.getUser("root");
        return bansAreaStrategyCmd.downLoadImprotTemp(user);
    }


    @ApiOperation(value = "导入")
    @RequestMapping(path = "/import", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 importDate(HttpServletRequest request,
                                     @RequestParam(value = "file") MultipartFile file,
                                     @RequestParam(value = "logisticsId") Long logisticsId) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.FAIL, "");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("BansAreaController.importDate start",
                    "BansAreaController.importDate start"));
        }
        try {
            List<Map<String, String>> excelList = new ArrayList();
            try {
                if (ObjectUtils.isEmpty(file)) {
                    vh.setMessage("文件为空");
                }
                // 1、读取excel文件
                exportUtil.readFile(file, excelList);
            } catch (Exception e) {
                throw new NDSException(e);
            }
            User user = (User) Security4Utils.getUser("root");
            vh = bansAreaStrategyCmd.importBansArea(user, excelList, logisticsId);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("BansAreaController.importDate",
                    "BansAreaController.importDate error"));
            vh.setMessage(e.getMessage());
        }
        return vh;
    }
}
