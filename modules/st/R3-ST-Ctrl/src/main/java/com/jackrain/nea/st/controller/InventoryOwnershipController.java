package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.InventoryOwnershipSaveCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2019/8/6 17:43
 */
@RestController
@Slf4j
@Api(value = "InventoryOwnershipController", description = "库存归属策略")
public class InventoryOwnershipController {
    @Autowired
    private InventoryOwnershipSaveCmd saveCmd;

    @ApiOperation(value = "库存归属策略保存")
    @RequestMapping(path = "/api/cs/st/saveInventoryOwnership", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveInventoryOwnership(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = saveCmd.execute(querySession);
        return result;
    }
}
