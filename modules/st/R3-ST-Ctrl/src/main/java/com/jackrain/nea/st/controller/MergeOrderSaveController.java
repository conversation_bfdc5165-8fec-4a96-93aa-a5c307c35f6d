package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.api.MergeOrderSaveCmd;
import com.jackrain.nea.st.api.MergeOrderVoidCmd;
import com.jackrain.nea.st.services.MergeOrderSaveCmdImpl;
import com.jackrain.nea.st.services.MergeOrderVoidCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@Slf4j
@Api(value = "MergeOrderSaveController", description = "订单合并策略controller")
public class MergeOrderSaveController {
    @Autowired
    private MergeOrderSaveCmdImpl mergeOrderSaveCmd;

    @Autowired
    private MergeOrderVoidCmdImpl mergeOrderVoidCmd;

    @ApiOperation(value = "订单合并策略保存")
    @RequestMapping(path = "/api/cs/st/saveMergeOrder", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveSellOwnGoods(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = mergeOrderSaveCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "订单合并策略作废")
    @RequestMapping(path = "/api/cs/st/voidMergeOrder", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidMergeOrder(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = mergeOrderVoidCmd.execute(querySession);
        return result;
    }
}
