package com.jackrain.nea.st.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.st.api.GetResolveRuleDetailCmd;
import com.jackrain.nea.st.api.ResolveRuleDelCmd;
import com.jackrain.nea.st.api.ResolveRuleSaveCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2019/3/7 10:55
 */

@RestController
@Slf4j
@Api(value = "ResolveRuleController", description = "解析规则")
public class ResolveRuleController {

    @Autowired
    private ResolveRuleSaveCmd resolveRuleSaveCmd;

    @Autowired
    private ResolveRuleDelCmd resolveRuleDelCmd;

    @Autowired
    private GetResolveRuleDetailCmd getResolveRuleDetailCmd;

    @ApiOperation(value = "解析规则添加保存")
    @RequestMapping(path = "/api/cs/st/saveResolveRule", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveResolveRule(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = resolveRuleSaveCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "解析规则删除")
    @RequestMapping(path = "/delResolveRule", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder delResolveRule(HttpServletRequest request, String param) {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = resolveRuleDelCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "解析规则双击列表查看信息接口")
    @RequestMapping(path = "/api/cs/st/getResolveRuleDetail", method = {RequestMethod.POST})
    public JSONObject getResolveRuleDetail(HttpServletRequest request,
                                           @RequestParam(value = "param", required = true)
                                                   String param) {
//        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(); //new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder vh = getResolveRuleDetailCmd.execute(querySession);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("finish getResolveRuleDetail. Return Result=") + result);
        }
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }
}
