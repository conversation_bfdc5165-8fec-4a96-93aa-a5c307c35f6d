package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPreOccupyProvincePriority;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreOccupyProvincePriorityMapper
 * @Description 预寻源-省优先
 * <AUTHOR>
 * @Date 2025/2/26 09:26
 * @Version 1.0
 */
@Component
@Mapper
public interface StCPreOccupyProvincePriorityMapper extends ExtentionMapper<StCPreOccupyProvincePriority> {

    @Select("select * from st_c_pre_occupy_province_priority where cp_c_province_id = #{cpCProvinceId} and ISACTIVE = 'Y'")
    StCPreOccupyProvincePriority selectByCpCProvinceId(Long cpCProvinceId);

    @Select("select * from st_c_pre_occupy_province_priority where cp_c_province_code = #{cpCProvinceEcode} and ISACTIVE = 'Y'")
    StCPreOccupyProvincePriority selectByCpCProvinceEcode(String cpCProvinceEcode);
}
