package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCOrderUrgentStrategyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface StCOrderUrgentStrategyMapper extends ExtentionMapper<StCOrderUrgentStrategyDO> {

    @Select("SELECT * FROM st_c_order_urgent_strategy WHERE FIND_IN_SET(#{shopId}, cp_c_shop_id) AND vp_c_viptype_id=#{vipLevel} ORDER BY ID DESC LIMIT 1")
    StCOrderUrgentStrategyDO selectOrderUrgencyStrategy(@Param("shopId") Long shopId, @Param("vipLevel") Integer vipLevel);

}