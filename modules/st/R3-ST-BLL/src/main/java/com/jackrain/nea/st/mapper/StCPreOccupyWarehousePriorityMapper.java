package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPreOccupyWarehousePriority;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreOccupyWarehousePriorityMapper
 * @Description 订单预寻源-仓优先
 * <AUTHOR>
 * @Date 2025/2/27 09:14
 * @Version 1.0
 */
@Component
@Mapper
public interface StCPreOccupyWarehousePriorityMapper extends ExtentionMapper<StCPreOccupyWarehousePriority> {

    @Select("select * from st_c_pre_occupy_warehouse_priority where cp_c_phy_warehouse_id = #{cpCPhyWarehouseId} and ISACTIVE = 'Y'")
    StCPreOccupyWarehousePriority selectByCpCWarehouseId(Long cpCPhyWarehouseId);

    @Select("select * from st_c_pre_occupy_warehouse_priority where cp_c_phy_warehouse_ecode = #{cpCPhyWarehouseEcode} and ISACTIVE = 'Y'")
    StCPreOccupyWarehousePriority selectByCpCWarehouseEcode(String cpCPhyWarehouseEcode);

}
