package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.request.SendRuleRequest;
import com.jackrain.nea.st.model.result.SendRuleAddressRankResult;
import com.jackrain.nea.st.model.result.SendRuleAddressVipResult;
import com.jackrain.nea.st.model.result.WarehouseRankResult;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.*;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Bll层-派单规则新增保存业务逻辑
 *
 * <AUTHOR> 陈俊明
 * @since : 2019-03-11
 * create at : 2019-03-11 17:47
 */
@Component
@Slf4j
@Transactional
public class SendRuleSaveService extends CommandAdapter {
    @Autowired
    private StCSendRuleMapper stCSendRuleMapper;

    @Autowired
    private StCSendRuleAddressRentMapper rentMapper;

    @Autowired
    private StCSendRuleAddressRankMapper rankMapper;

    @Autowired
    private StCSendRuleWarehouseRateMapper rateMapper;

    @Autowired
    private StCSendRuleAddressVipMapper stCSendRuleAddressVipMapper;

    @Autowired
    private RpcCpService rpcCpService;

    @Autowired
    private StCSendPlanItemMapper stCItemMapper;

    private String strTableMain = "ST_C_SEND_RULE";
    private String strTableList = "ST_C_SEND_RULE_ADDRESS_RENT";
    private String strTableListRank = "ST_C_SEND_RULE_ADDRESS_RANK";
    private String strTableListVip = "ST_C_SEND_RULE_ADDRESS_VIP";
    private String strTableListRate = "ST_C_SEND_RULE_WAREHOUSE_RATE";

    @Autowired
    private RedisOpsUtil redisUtil;

    /**
     * 新增和修改
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                SendRuleRequest request = JsonUtils.jsonParseClass(fixColumn, SendRuleRequest.class);

                if (request == null) {
                    throw new NDSException("数据异常！");
                }
                if (id != null && id < 0) {
                    valueHolder = saveSendRule(request, valueHolder, querySession, id);
                } else {
                    valueHolder = updateSendRule(request, valueHolder, querySession, id);
                    if (valueHolder != null && valueHolder.isOK()) {
                        redisUtil.objRedisTemplate.delete(OmsRedisKeyResources.bulidLockStSendRuleKey(id));
                    }
                }
            }
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    /**
     * 新增操作
     *
     * @param request      数据
     * @param holder       响应数据
     * @param querySession 封装数据
     * @param objid        id
     * @return 返回状态
     */
    private ValueHolder saveSendRule(SendRuleRequest request,
                                     ValueHolder holder,
                                     QuerySession querySession, Long objid) {
        //objid就是主表的id
        StCSendRuleDO stCSendRuleDO = request.getSendRule();
        if (stCSendRuleDO != null) {
            //状态检查
            if (!checkStatus(stCSendRuleDO, objid, holder)) {
                return holder;
            }

            stCSendRuleDO.setId(ModelUtil.getSequence(strTableMain));
            StBeanUtils.makeCreateField(stCSendRuleDO, querySession.getUser());

            if (stCSendRuleMapper.insert(stCSendRuleDO) <= 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
            RedisCacheUtil.hashDelete(RedisConstant.ST_SEND_RULE_KEY, stCSendRuleDO.getId());
        } else {
            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
            return holder;
        }
        //子表操作
        if (saveChildTable(request, holder, querySession, objid)) {
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(stCSendRuleDO.getId(), strTableMain);
        return holder;
    }

    /**
     * 更新操作
     *
     * @param request      数据
     * @param holder       响应数据
     * @param querySession 封装数据
     * @param objid        派单规则ID
     * @return 返回状态
     */
    private ValueHolder updateSendRule(SendRuleRequest request,
                                       ValueHolder holder,
                                       QuerySession querySession, Long objid) {
        //主表更新，objid就是主表ID
        StCSendRuleDO stCSendRuleDO = request.getSendRule();
        if (stCSendRuleDO != null) {
            if (!checkStatus(stCSendRuleDO, objid, holder)) {
                return holder;
            }
            stCSendRuleDO.setId(objid);
            StBeanUtils.makeModifierField(stCSendRuleDO, querySession.getUser());

            if (stCSendRuleMapper.updateById(stCSendRuleDO) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
            RedisCacheUtil.hashDelete(RedisConstant.ST_SEND_RULE_KEY, stCSendRuleDO.getId());
        }

        //子表操作
        if (saveChildTable(request, holder, querySession, objid)) {
            try {
                Long shopId = stCItemMapper.selectShopIdByRuleId(objid);
                if (shopId != null){
                    String redisPlanKey = "st:shop:send:plan:and:rule:shopid:" + shopId;
                    if (redisUtil.strRedisTemplate.hasKey(redisPlanKey)) {
                        redisUtil.strRedisTemplate.delete(redisPlanKey);
                    }
                }
            } catch (Exception e) {
                log.error(LogUtil.format("清除缓存异常{}"), Throwables.getStackTraceAsString(e));
            }
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(objid, strTableMain);
        return holder;
    }


    /**
     * @param request       请求参数
     * @param holder        返回参数
     * @param querySession  封装数据
     * @param stCSendRuleId 派单规则ID
     * <AUTHOR>
     * @create 2020-06-16 09:59
     * @desc 订单派单规则-明细、仓库优先级明细、分仓比例明细、唯品会明细保存
     **/
    private boolean saveChildTable(SendRuleRequest request, ValueHolder holder, QuerySession querySession, Long stCSendRuleId) {
        //订单派单规则明细
        List<StCSendRuleAddressRentDO> stCSendRuleAddressRentDOList = request.getRentList();
        if (stCSendRuleAddressRentDOList != null) {
            if (!saveRentList(querySession, stCSendRuleId, stCSendRuleAddressRentDOList, holder)) {
                return true;
            }
        }
        //订单派单规则仓库优先级明细
        List<SendRuleAddressRankResult> stCSendRuleAddressRankDOList = request.getRankList();
        if (stCSendRuleAddressRankDOList != null) {
            if (!saveRankList(querySession, stCSendRuleId, stCSendRuleAddressRankDOList, holder)) {
                return true;
            }
        }
        //订单派单规则分仓比例明细
        List<StCSendRuleWarehouseRateDO> stCSendRuleWarehouseRateDOList = request.getRateList();
        if (stCSendRuleWarehouseRateDOList != null) {
            //检查行明细
            if (!checkRateListStatus(stCSendRuleWarehouseRateDOList, stCSendRuleId, holder)) {
                return true;
            }
            if (!saveRateList(querySession, stCSendRuleId, stCSendRuleWarehouseRateDOList, holder)) {
                return true;
            }
        }
        //派单规则唯品会明细添加
        List<SendRuleAddressVipResult> sendRuleAddressVipResultList = request.getSendRuleAddressVipResultList();
        if (CollectionUtils.isNotEmpty(sendRuleAddressVipResultList)) {
            if (!saveVipList(querySession, stCSendRuleId, sendRuleAddressVipResultList, holder)) {
                return true;
            }
        }
        return false;
    }

    private boolean saveRentList(QuerySession querySession, Long objid,
                                 List<StCSendRuleAddressRentDO> stCSendRuleAddressRentDOList, ValueHolder valueHolder) {
        for (StCSendRuleAddressRentDO stCSendRuleAddressRentDO : stCSendRuleAddressRentDOList) {
            Long id = stCSendRuleAddressRentDO.getId();
            // 取得仓库信息
            if (stCSendRuleAddressRentDO.getCpCPhyWarehouseId() != null) {
                CpCPhyWarehouse cpCPhyWarehouse = rpcCpService.getCpCPhyWahouseDoById(stCSendRuleAddressRentDO.getCpCPhyWarehouseId());
                if (cpCPhyWarehouse != null) {
                    stCSendRuleAddressRentDO.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                    stCSendRuleAddressRentDO.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                }
            }
            if (id < 0) {
                stCSendRuleAddressRentDO.setId(ModelUtil.getSequence(strTableList));
                stCSendRuleAddressRentDO.setStCSendRuleId(objid);
                StBeanUtils.makeCreateField(stCSendRuleAddressRentDO, querySession.getUser());

                if (rentMapper.insert(stCSendRuleAddressRentDO) <= 0) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "保存失败！");
                    return false;
                }
            } else {
                //修改原有的行信息 id>0
                if (rentMapper.selectById(id) != null) {
                    StBeanUtils.makeModifierField(stCSendRuleAddressRentDO, querySession.getUser());
                    if (rentMapper.updateById(stCSendRuleAddressRentDO) < 0) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "保存失败！");
                        return false;
                    }
                } else {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "修改的行明细已被删除！");
                    return false;
                }
            }
        }
        return true;
    }

    private boolean saveRankList(QuerySession querySession, Long objid,
                                 List<SendRuleAddressRankResult> sendRuleAddressRankResultList, ValueHolder valueHolder) {
        //获取仓库信息
        List<StCSendRuleAddressRentDO> rentList = getStCSendRuleWareHouse(objid);
        if (CollectionUtils.isNotEmpty(rentList)) {
            rentList = rentList.stream().sorted(Comparator.comparing(StCSendRuleAddressRentDO::getId))
                    .collect(Collectors.toList());
        }
        for (SendRuleAddressRankResult rankResult : sendRuleAddressRankResultList) {

            Long id = rankResult.getId();
            StCSendRuleAddressRankDO rank = new StCSendRuleAddressRankDO();
            BeanUtils.copyProperties(rankResult, rank);

            int i = 1;
            List<WarehouseRankResult> warehouseRankNewList = Lists.newArrayList();
            List<String> rankAllList = Lists.newArrayList();
            for (StCSendRuleAddressRentDO rent : rentList) {
                WarehouseRankResult warehouseRankNew = new WarehouseRankResult();
                warehouseRankNew.setWarehouseId(rent.getCpCPhyWarehouseId());
                try {
                    Method method = SendRuleAddressRankResult.class.getMethod("getRank" + i);
                    warehouseRankNew.setRank((String) method.invoke(rankResult));
                } catch (Exception ex) {
                    warehouseRankNew.setRank("");
                    log.debug(LogUtil.format("仓库优先级获取失败：{}"), Throwables.getStackTraceAsString(ex));
                }
                i++;
                if (warehouseRankNew.getRank() != null && !"".equals(warehouseRankNew.getRank())) {
                    if (rankAllList.contains(warehouseRankNew.getRank())) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", rank.getCpCRegionProvinceEname() +
                                rank.getCpCRegionProvinceEname() + "仓库优先级重复！");
                        return false;
                    } else {
                        rankAllList.add(warehouseRankNew.getRank());
                    }
                    warehouseRankNewList.add(warehouseRankNew);
                }
            }

            boolean saveFlg = false;
            for (WarehouseRankResult warehouseRankResult : warehouseRankNewList) {
                if (StringUtils.isNotBlank(warehouseRankResult.getRank())) {
                    saveFlg = true;
                }
            }

            rank.setWarehouseRank(JSONArray.toJSONString(warehouseRankNewList));
            if (id < 0) {
                if (saveFlg) {
                    rank.setId(ModelUtil.getSequence(strTableListRank));
                    rank.setStCSendRuleId(objid);

                    StBeanUtils.makeCreateField(rank, querySession.getUser());
                    if (rankMapper.insert(rank) < 0) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", rank.getCpCRegionProvinceEname() +
                                rank.getCpCRegionProvinceEname() + "保存失败！");
                        return false;
                    }
                }
            } else {
                //清除rediskey
                String redisKey = RedisConstant.bulidLockStCSendRuleAddressRankKey(objid, rank.getCpCRegionProvinceId());
                if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
                    redisUtil.objRedisTemplate.delete(redisKey);
                }
                if (saveFlg) {
                    //修改原有的行信息 id>0
                    if (rankMapper.selectById(id) != null) {
                        StBeanUtils.makeModifierField(rank, querySession.getUser());
                        if (rankMapper.updateById(rank) < 0) {
                            valueHolder.put("code", -1);
                            valueHolder.put("message", "保存失败！");
                            return false;
                        }
                    } else {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "修改的行明细已被删除！");
                        return false;
                    }
                } else {
                    if (rankMapper.deleteById(rank) < 0) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "保存失败！");
                        return false;
                    }
                }
            }
            String redisKey2 = OmsRedisKeyResources.buildLockStCSendRuleAddressRankKey(rank.getStCSendRuleId(),
                    rank.getCpCRegionProvinceId());
            RedisCacheUtil.deleteAll(redisKey2);
        }
        return true;
    }

    private boolean saveRateList(QuerySession querySession, Long objid,
                                 List<StCSendRuleWarehouseRateDO> stCSendRuleWarehouseRateDOList, ValueHolder valueHolder) {
        for (StCSendRuleWarehouseRateDO stCSendRuleWarehouseRateDO : stCSendRuleWarehouseRateDOList) {
            Long id = stCSendRuleWarehouseRateDO.getId();
            // 取得仓库信息
            if (stCSendRuleWarehouseRateDO.getCpCPhyWarehouseId() != null) {
                CpCPhyWarehouse cpCPhyWarehouse = rpcCpService.getCpCPhyWahouseDoById(stCSendRuleWarehouseRateDO.getCpCPhyWarehouseId());
                if (cpCPhyWarehouse != null) {
                    stCSendRuleWarehouseRateDO.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                    stCSendRuleWarehouseRateDO.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                }
            }
            if (id < 0) {
                if (stCSendRuleWarehouseRateDO.getQtySend() == null) {
                    stCSendRuleWarehouseRateDO.setQtySend(BigDecimal.ZERO);
                }
                if (stCSendRuleWarehouseRateDO.getSendRate().doubleValue() < 0.0) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "发货比例，不允许录入负数！");
                    return false;
                }
                if (stCSendRuleWarehouseRateDO.getSendRate().doubleValue() > 100.0) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "发货比例，不允许超过100！");
                    return false;
                }
                stCSendRuleWarehouseRateDO.setId(ModelUtil.getSequence(strTableListRate));
                stCSendRuleWarehouseRateDO.setStCSendRuleId(objid);
                StBeanUtils.makeCreateField(stCSendRuleWarehouseRateDO, querySession.getUser());

                if (rateMapper.insert(stCSendRuleWarehouseRateDO) < 0) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "保存失败！");
                    return false;
                }
            } else {
                //修改原有的行信息 id>0
                if (rateMapper.selectById(id) != null) {
                    StBeanUtils.makeModifierField(stCSendRuleWarehouseRateDO, querySession.getUser());
                    if (rateMapper.updateById(stCSendRuleWarehouseRateDO) < 0) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "保存失败！");
                        return false;
                    }
                } else {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "修改的行明细已被删除！");
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @param querySession                 查询session
     * @param stCSendRuleId                派单规则ID
     * @param sendRuleAddressVipResultList 明细数据
     * @param valueHolder                  返回参数
     * @return
     * <AUTHOR>
     * @create 2020-06-15 17:23
     * @desc 派单规则-唯品会明细添加
     **/
    private boolean saveVipList(QuerySession querySession, Long stCSendRuleId,
                                List<SendRuleAddressVipResult> sendRuleAddressVipResultList, ValueHolder valueHolder) {
        //获取仓库信息
        List<StCSendRuleAddressRentDO> rentList = getStCSendRuleWareHouse(stCSendRuleId);
        if (CollectionUtils.isEmpty(rentList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "请先设置仓库");
            return false;
        }
        //排序
        rentList = rentList.stream().sorted(Comparator.comparing(StCSendRuleAddressRentDO::getId)).collect(Collectors.toList());
        for (SendRuleAddressVipResult sendRuleAddressVipResult : sendRuleAddressVipResultList) {
            //订单派单规则-唯品会明细
            StCSendRuleAddressVipDo stCSendRuleAddressVipDo = new StCSendRuleAddressVipDo();
            BeanUtils.copyProperties(sendRuleAddressVipResult, stCSendRuleAddressVipDo);
            //用于字段取值
            int i = 1;
            //实体仓和优先级的集合
            List<WarehouseRankResult> warehouseRankNewList = Lists.newArrayList();
            //实体仓的优先级集合
//            List<String> rankAllList = Lists.newArrayList();
            boolean saveFlg = false;
            //已设置实体仓列表
            for (StCSendRuleAddressRentDO stCSendRuleAddressRentDO : rentList) {
                //实体仓优先级关系类
                WarehouseRankResult warehouseRankResult = new WarehouseRankResult();
                warehouseRankResult.setWarehouseId(stCSendRuleAddressRentDO.getCpCPhyWarehouseId());
                try {
                    //获取对应优先级
                    Method method = SendRuleAddressVipResult.class.getMethod("getRank" + i);
                    warehouseRankResult.setRank((String) method.invoke(sendRuleAddressVipResult));
                } catch (Exception ex) {
                    warehouseRankResult.setRank("");
                }
                i++;
                if (StringUtils.isNotEmpty(warehouseRankResult.getRank())) {
//                    if (rankAllList.contains(warehouseRankResult.getRank())) {
//                        valueHolder.put("code", ResultCode.FAIL);
//                        valueHolder.put("message", stCSendRuleAddressVipDo.getCpCVipcomWahouseWarehouseCode() +
//                                stCSendRuleAddressVipDo.getCpCVipcomWahouseWarehouseName() + "仓库优先级重复！");
//                        return false;
//                    } else {
//                        rankAllList.add(warehouseRankResult.getRank());
//                    }
                    warehouseRankNewList.add(warehouseRankResult);
                }
                if (StringUtils.isNotBlank(warehouseRankResult.getRank())) {
                    saveFlg = true;
                }
            }
            stCSendRuleAddressVipDo.setWarehouseRank(JSONArray.toJSONString(warehouseRankNewList));
            //保存
            if (saveVip(querySession.getUser(), stCSendRuleId, stCSendRuleAddressVipDo, saveFlg, valueHolder)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param user                    用户信息
     * @param stCSendRuleId           派单规则ID
     * @param stCSendRuleAddressVipDo 派单规则-唯品会明细
     * @param saveFlg                 是否保存
     * @param valueHolder             返回参数
     * @return
     * <AUTHOR>
     * @create 2020-06-15 21:37
     * @desc 派单规则-唯品会明细保存
     **/
    private boolean saveVip(User user, Long stCSendRuleId, StCSendRuleAddressVipDo stCSendRuleAddressVipDo, boolean saveFlg, ValueHolder valueHolder) {
        if (stCSendRuleAddressVipDo.getId() == null || stCSendRuleAddressVipDo.getId() < 0) {
            if (saveFlg) {
                stCSendRuleAddressVipDo.setId(ModelUtil.getSequence(strTableListVip));
                stCSendRuleAddressVipDo.setStCSendRuleId(stCSendRuleId);
                StBeanUtils.makeCreateField(stCSendRuleAddressVipDo, user);
                if (stCSendRuleAddressVipMapper.insert(stCSendRuleAddressVipDo) < 0) {
                    valueHolder.put("code", ResultCode.FAIL);
                    valueHolder.put("message", stCSendRuleAddressVipDo.getCpCVipcomWahouseWarehouseCode() +
                            stCSendRuleAddressVipDo.getCpCVipcomWahouseWarehouseCode() + "保存失败！");
                    return true;
                }
            }
        } else {
            //清除rediskey
            String redisKey = OmsRedisKeyResources.bulidLockStCSendRuleAddressVipKey(stCSendRuleId, stCSendRuleAddressVipDo.getCpCVipcomWahouseId());
            if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
                redisUtil.objRedisTemplate.delete(redisKey);
            }
            if (saveFlg) {
                //修改原有的行信息 id>0
                if (stCSendRuleAddressVipMapper.selectById(stCSendRuleAddressVipDo.getId()) != null) {
                    StBeanUtils.makeModifierField(stCSendRuleAddressVipDo, user);
                    if (stCSendRuleAddressVipMapper.updateById(stCSendRuleAddressVipDo) < 0) {
                        valueHolder.put("code", ResultCode.FAIL);
                        valueHolder.put("message", "保存失败！");
                        return true;
                    }
                } else {
                    valueHolder.put("code", ResultCode.FAIL);
                    valueHolder.put("message", "修改的行明细已被删除！");
                    return true;
                }
            } else {
                if (stCSendRuleAddressVipMapper.deleteById(stCSendRuleAddressVipDo) < 0) {
                    valueHolder.put("code", ResultCode.FAIL);
                    valueHolder.put("message", "保存失败！");
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @param stCSendRuleId 派单规则ID
     * <AUTHOR>
     * @create 2020-06-15 17:36
     * @desc 获取派单规则的所有仓库信息
     **/
    private List<StCSendRuleAddressRentDO> getStCSendRuleWareHouse(Long stCSendRuleId) {
        return rentMapper.selectList(new QueryWrapper<StCSendRuleAddressRentDO>().lambda().eq(StCSendRuleAddressRentDO::getStCSendRuleId, stCSendRuleId));
    }

    /**
     * @param stCSendRuleWarehouseRateDOList 按分仓比例
     * @param objid                          主表主键
     * @param valueHolder                    封装返回
     * @return 返回
     */
    private boolean checkRateListStatus(List<StCSendRuleWarehouseRateDO> stCSendRuleWarehouseRateDOList,
                                        Long objid, ValueHolder valueHolder) {
        //新增和修改的内容全部防止在Map类型内，区别判断后面的Value 如果是修改的值存到是id，新增的行id则是-1
        Map<String, String> modifyMap = new HashMap();
        Map<String, String> oldMap = new HashMap<String, String>();

        String strKey = "";
        String strValue = "";
        BigDecimal bigDecimalSum = new BigDecimal("0");
        BigDecimal bigDecimal = new BigDecimal("100");

        //【发货比例】： 手动维护，总和小于等于1。
        if (objid > 0) {
            StCSendRuleWarehouseRateDO rateDO = rateMapper.selectByIdAndSendRate(objid);
            if (rateDO != null) {
                bigDecimalSum = rateDO.getSendRate();
            }
        }

        //按分仓比例
        //判断修改的内容是否涉及到 仓库(cp_c_phy_warehouse_id)
        for (StCSendRuleWarehouseRateDO stCSendRuleWarehouseRateDO : stCSendRuleWarehouseRateDOList) {
            if (stCSendRuleWarehouseRateDO.getCpCPhyWarehouseId() != null) {
                strKey = stCSendRuleWarehouseRateDO.getCpCPhyWarehouseId().toString();
                strValue = stCSendRuleWarehouseRateDO.getId().toString();

                if (modifyMap.isEmpty()) {
                    modifyMap.put(strKey, strValue);
                } else {
                    if (modifyMap.containsKey(strKey)) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "仓库不能重复！");
                        return false;
                    } else {
                        modifyMap.put(strKey, strValue);
                    }
                }
            }
            bigDecimalSum = bigDecimalSum.add(stCSendRuleWarehouseRateDO.getSendRate());
        }
        if (objid > 0) {
            List<StCSendRuleWarehouseRateDO> rateOldList = rateMapper.listBySendRuleId(objid);
            if (rateOldList != null) {
                for (StCSendRuleWarehouseRateDO stCDO : rateOldList) {
                    if (stCDO.getId() != null && stCDO.getCpCPhyWarehouseId() != null) {
                        strKey = stCDO.getCpCPhyWarehouseId().toString();
                        strValue = stCDO.getId().toString();
                        //判断如果是修改仓库的话，则从oldMap去除这行
                        if (!modifyMap.containsValue(strValue)) {
                            oldMap.put(strKey, strValue);
                        }
                    }
                    if (modifyMap.containsValue(stCDO.getId().toString())) {
                        bigDecimalSum = bigDecimalSum.subtract(stCDO.getSendRate());
                    }
                }
            }
        }

        if (!oldMap.isEmpty() && !modifyMap.isEmpty()) {
            //判断modifyMap的key值 是否存在于oldMap的key值
            for (String key : modifyMap.keySet()) {
                if (oldMap.containsKey(key)) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "仓库不能重复！");
                    return false;
                }
            }
        }
        if (bigDecimalSum.compareTo(bigDecimal) >= 1) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "发货比例，总和不能大于100！");
            return false;
        }
        return true;
    }

    private boolean checkStatus(StCSendRuleDO stCSendRuleDO, Long objId, ValueHolder valueHolder) {
        if (stCSendRuleDO.getEname() != null) {
            List<StCSendRuleDO> listSendRules = stCSendRuleMapper.listByEname(stCSendRuleDO.getEname());
            if (listSendRules != null && listSendRules.size() > 0) {
                for (StCSendRuleDO stCSendRule : listSendRules) {
                    if (!stCSendRule.getId().equals(objId)) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "规则名称不能重复！");
                        return false;
                    }
                }
            }
        }

        if (objId > 0) {
            StCSendRuleDO stCSendRuleDO1 = stCSendRuleMapper.selectById(objId);
            if (stCSendRuleDO1 != null) {
                String strActive = stCSendRuleDO1.getIsactive();
                if ("N".equals(strActive)) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "单据处于作废状态不能编辑！");
                    return false;
                }
            } else {
                valueHolder.put("code", -1);
                valueHolder.put("message", "数据已不存在！");
                return false;
            }
        }
        return true;
    }
}
