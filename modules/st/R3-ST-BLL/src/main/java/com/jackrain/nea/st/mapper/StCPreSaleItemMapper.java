package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPreSaleDO;
import com.jackrain.nea.st.model.table.StCPreSaleItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCPreSaleItemMapper extends ExtentionMapper<StCPreSaleItemDO> {
    @Select("select * from st_c_pre_sale_item where st_c_pre_sale_id = #{preSaleId} and pre_sale_way = #{preSaleWay}")
    List<StCPreSaleItemDO> selectByPreSaleIdAndPreSaleWay(@Param("preSaleId") Long preSaleId, @Param("preSaleWay") Integer preSaleWay);
}