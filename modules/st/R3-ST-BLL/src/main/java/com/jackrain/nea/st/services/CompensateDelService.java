package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCCompensateLogisticsMapper;
import com.jackrain.nea.st.mapper.StCCompensateMapper;
import com.jackrain.nea.st.mapper.StCCompensateWarehouseMapper;
import com.jackrain.nea.st.model.table.StCCompensateDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2019/3/12 16:56
 */
@Component
@Slf4j
@Transactional
public class CompensateDelService extends CommandAdapter {

    @Autowired
    private StCCompensateMapper stCCompensateMapper;

    @Autowired
    private StCCompensateLogisticsMapper stCCompensateLogisticsMapper;

    @Autowired
    private StCCompensateWarehouseMapper stCCompensateWarehouseMapper;

    /**
     * @param querySession
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/12
     */

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder result = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");

        //判断主表是否存在
        if (stCCompensateMapper.selectById(objid) == null) {
            result = ValueHolderUtils.getFailValueHolder("当前记录已不存在!");
            return result;
        }
        //判断是删除主表还是明细表单独删除
        if (StConstant.FALSE_STR.equals(isDel)) {
            //单独删除明细
            JSONObject tabitem = param.getJSONObject("tabitem");
            JSONArray logisticsArray = tabitem.getJSONArray(StConstant.TAB_ST_C_COMPENSATE_LOGISTICS);
            if (logisticsArray != null && logisticsArray.size() > 0) {
                result = delLogistics(logisticsArray, result);
            }
            JSONArray warehouseArray = tabitem.getJSONArray(StConstant.TAB_ST_C_COMPENSATE_WAREHOUSE);
            if (warehouseArray != null && warehouseArray.size() > 0) {
                result = delWarehouse(warehouseArray, result);
            }
            updateModifyInfo(querySession, objid);
        } else {
            //删除主表跟明细
            result = delCompensate(objid);
        }
        return result;
    }

    private void updateModifyInfo(QuerySession session, Long id) {
        StCCompensateDO compensate = new StCCompensateDO();
        compensate.setId(id);
        StBeanUtils.makeModifierField(compensate, session.getUser());
        if ((stCCompensateMapper.updateById(compensate)) <= 0) {
            log.error(LogUtil.format("ExpressDelService.updateModifyInfo Error", "删除明细，主表修改字段信息更新出错id=", id));
        }
    }

    /**
     * @param logisticsArray
     * @param result
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/12
     */
    private ValueHolder delLogistics(JSONArray logisticsArray, ValueHolder result) {
        for (int i = 0; i < logisticsArray.size(); i++) {
            Long slaverId = logisticsArray.getLong(i);
            int deleteCount = stCCompensateLogisticsMapper.deleteById(slaverId);
            result = ValueHolderUtils.getDeleteSuccessValueHolder();
            //小于0表示数据已经被删除
            if (deleteCount < 0) {
                result = ValueHolderUtils.getFailValueHolder("快递公司明细已不存在！");
                return result;
            }
        }
        return result;
    }

    private ValueHolder delWarehouse(JSONArray warehouseArray, ValueHolder result) {
        for (int i = 0; i < warehouseArray.size(); i++) {
            Long slaverId = warehouseArray.getLong(i);
            int deleteCount = stCCompensateWarehouseMapper.deleteById(slaverId);
            result = ValueHolderUtils.getDeleteSuccessValueHolder();
            //小于0表示数据已经被删除
            if (deleteCount < 0) {
                result = ValueHolderUtils.getFailValueHolder("实物仓明细已不存在！");
                return result;
            }
        }
        return result;
    }

    /**
     * @param masterId
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/12
     */
    private ValueHolder delCompensate(Long masterId) {
        //删除明细
        if(stCCompensateLogisticsMapper.deleteById(masterId) < 0){
            return ValueHolderUtils.getFailValueHolder("快递公司明细已不存在！");
        }
        if(stCCompensateWarehouseMapper.deleteById(masterId) < 0){
            return ValueHolderUtils.getFailValueHolder("实物仓明细已不存在！");
        }
        //删除主表数据
        if (stCCompensateMapper.deleteById(masterId) < 0) {
            return ValueHolderUtils.getFailValueHolder("快递赔付已不存在！");
        }
        return ValueHolderUtils.getSuccessValueHolder("删除成功");
    }
}
