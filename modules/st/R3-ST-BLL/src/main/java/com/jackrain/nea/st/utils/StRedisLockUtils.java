package com.jackrain.nea.st.utils;

import com.google.common.base.Throwables;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/6 11:21
 */
@Slf4j
public class StRedisLockUtils {

    public static boolean lock(String lockKey) {
        //打印当前redis lockKey 和线程名
        if (log.isDebugEnabled()) {
            log.debug(" StRedisLockUtils.lock.lockKey={},ThreadName={}", lockKey, Thread.currentThread().getName());
        }
        CusRedisTemplate<String, String> redisMasterTemplate = StRedisMasterUtils.getObjRedisTemplate();
        Boolean blnCanInit = redisMasterTemplate.opsForValue().setIfAbsent(lockKey, "ok");
        if (blnCanInit != null && blnCanInit) {
            redisMasterTemplate.expire(lockKey, 5, TimeUnit.SECONDS);
        } else {
            AssertUtils.logAndThrow("当前单据正在操作中。。。");
        }
        return true;
    }

    public static void unlock(String lockKey) {
        CusRedisTemplate<String, String> redisMasterTemplate = StRedisMasterUtils.getObjRedisTemplate();
        redisMasterTemplate.delete(lockKey);
    }

    public static void unlock(String lockKey, Logger logger, String className) {
        try {
            unlock(lockKey);
        } catch (Exception e) {
            logger.error("{}.voidInResult exception has occured! lockKey:{} 详情:{} 释放锁失败，请联系管理员！",
                    className, lockKey, Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("释放锁失败，请联系管理员！");
        }
    }
}
