package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCInventorySkuOwnershipDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

@Mapper
public interface StCInventorySkuOwnershipMapper extends ExtentionMapper<StCInventorySkuOwnershipDO> {

    @UpdateProvider(type = StCInventorySkuOwnershipMapper.StCInventorySkuOwnershipAttribute.class, method = "updateSql")
    int updateAtrributes(JSONObject entity);

    class StCInventorySkuOwnershipAttribute {
        public String updateSql(JSONObject entity) {
            return new SQL() {
                {
                    UPDATE("st_c_inventory_sku_ownership");
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }
}