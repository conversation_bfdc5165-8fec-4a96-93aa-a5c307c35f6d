package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCVipcomCooperationNo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

@Mapper
@Component
public interface StCVipcomCooperationNoMapper extends ExtentionMapper<StCVipcomCooperationNo> {

    /**
     * 根据常态合作编码查询常态合作编码信息
     * @param ecode
     * @return StCVipcomCooperationNo
     */
    @Select("SELECT * from st_c_vipcom_cooperation_no s \n" +
            " where s.cooperation_no=#{ecode} and s.ISACTIVE='Y'")
    StCVipcomCooperationNo queryCooperationNoInfoByNo(@Param("ecode") String ecode);

}