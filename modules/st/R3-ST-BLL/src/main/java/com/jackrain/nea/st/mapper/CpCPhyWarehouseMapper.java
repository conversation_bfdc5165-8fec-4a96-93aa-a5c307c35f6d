package com.jackrain.nea.st.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.CpCPhyWarehouseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

@Mapper
public interface CpCPhyWarehouseMapper extends ExtentionMapper<CpCPhyWarehouseDO> {
    @Select("Select group_concat(wms_warehouse_code) FROM CP_C_PHY_WAREHOUSE WHERE id in (#{ids})")
    String selectPhyWareHouseCodesByIds(String ids);
}