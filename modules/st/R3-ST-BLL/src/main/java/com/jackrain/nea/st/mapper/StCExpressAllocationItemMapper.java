package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.request.ExpressAllocationRequest;
import com.jackrain.nea.st.model.table.StCExpressAllocationItemDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

@Mapper
@Component
public interface StCExpressAllocationItemMapper extends ExtentionMapper<StCExpressAllocationItemDO> {

    @Select("select * from st_c_express_allocation_item where st_c_express_allocation_id = #{mainid}")
    List<StCExpressAllocationItemDO> listByMainid(@Param("mainid") Long mainid);

    @Select("SELECT cp_c_logistics_id FROM st_c_express_allocation_item a INNER JOIN st_c_express_allocation  b ON  b.id=a.st_c_express_allocation_id WHERE b.cp_c_phy_warehouse_id=#{warehouseId} AND a.isactive='Y' AND b.isactive='Y'")
    List<Long> getLogisticInfoByWarehouseId(@Param("warehouseId") Long warehouseId);

    @Select("select b.* from st_c_express_allocation a INNER JOIN st_c_express_allocation_item b on a.id=b.st_c_express_allocation_id where a.isactive='Y' and b.isactive='Y' and a.cp_c_phy_warehouse_id= #{wareid} and b.cp_c_logistics_id = #{logisticsid}")
    List<StCExpressAllocationItemDO> listByWareidAndLogisticsid(@Param("wareid") Long wareid, @Param("logisticsid") Long logisticsid);

    /**
     * 根据ID 获取当前数据库 发货比例的总和
     *
     * @param id
     * @return
     */
    @Select("SELECT SUM(SCALE) as SCALE FROM ST_C_EXPRESS_ALLOCATION_ITEM WHERE ST_C_EXPRESS_ALLOCATION_ID = #{id} ")
    StCExpressAllocationItemDO selectByIdAndScale(@Param("id") Long id);

    /**
     * 更新物流公司发货数量
     *
     * @param phyWarehouseId 仓库公司Id
     * @param logisticsId    物流公司Id
     * @return
     */
    @Update("UPDATE st_c_express_allocation_item item INNER JOIN st_c_express_allocation express ON express.id = item.st_c_express_allocation_id" +
            " SET item.send_num = IFNULL(item.send_num,0) +1" +
            " WHERE express.cp_c_phy_warehouse_id=#{phyWarehouseId} AND item.cp_c_logistics_id=#{logisticsId}")
    Integer updateLogisticsNum(@Param("phyWarehouseId") Long phyWarehouseId, @Param("logisticsId") Long logisticsId);

    /**
     * 根据订单"发货仓库"查询【物流分配规则明细表】的“物流公司”信息
     *
     * @param qtyAll 商品数量
     * @return List<StCExpressAllocationItem>
     */
    @Select("SELECT B.* FROM ST_C_EXPRESS_ALLOCATION A  INNER JOIN  ST_C_EXPRESS_ALLOCATION_ITEM B " +
            "WHERE A.ID=B.ST_C_EXPRESS_ALLOCATION_ID AND A.CP_C_PHY_WAREHOUSE_ID = #{cpCPhyWarehouseId} AND A.ISACTIVE='Y' AND B.ISACTIVE='Y' ")
    List<StCExpressAllocationItemDO> selectLogisticsIdBycpCPhyWarehouseId(@Param("cpCPhyWarehouseId") Long cpCPhyWarehouseId, @Param("qtyAll") BigDecimal qtyAll);

    @Select("SELECT B.CP_C_LOGISTICS_ID FROM ST_C_EXPRESS_ALLOCATION A  INNER JOIN  ST_C_EXPRESS_ALLOCATION_ITEM B " +
            "WHERE A.ID=B.ST_C_EXPRESS_ALLOCATION_ID AND A.CP_C_PHY_WAREHOUSE_ID = #{cpCPhyWarehouseId} AND A.ISACTIVE='Y' AND B.ISACTIVE='Y' ")
    List<Long> selectLogisticsIdByWarehouseId(@Param("cpCPhyWarehouseId") Long cpCPhyWarehouseId);

    @Select("<script>"
            + "SELECT * FROM ST_C_EXPRESS_ALLOCATION_ITEM  WHERE CP_C_LOGISTICS_ID IN "
            + "<foreach item='item' index='index' collection='list'      open='(' separator=',' close=')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<StCExpressAllocationItemDO> selectStCExpressAllocationItemByLogisticsId(@Param("list") List<Long> list);

    /**
     * 查询物流公司发货总数量
     *
     * @param cpCPhyWarehouseId
     * @return BigDecimal
     */
    @Select("SELECT SUM(A.SEND_NUM) FROM ST_C_EXPRESS_ALLOCATION_ITEM  A ,ST_C_EXPRESS_ALLOCATION B WHERE B.ID = A.ST_C_EXPRESS_ALLOCATION_ID AND B.CP_C_PHY_WAREHOUSE_ID = #{cpCPhyWarehouseId} ")
    BigDecimal countSendsum(@Param("cpCPhyWarehouseId") Long cpCPhyWarehouseId);

    @SelectProvider(type = ExpressByPhyWarehouseSqlProvider.class, method = "select")
    List<HashMap> selectExpressByPhyWarehouse(ExpressAllocationRequest queryRequest);

    @Select("SELECT COUNT(1) from st_c_express_allocation_item WHERE  st_c_express_allocation_id = #{objid} AND CP_C_SHOP_ID = #{cpCShopId} AND ISACTIVE ='Y' \n")
    int isExistExpressAllocationItem(@Param("objid") Long objid, @Param("cpCShopId") String cpCShopId);

    @Select("SELECT\n" +
            "\tcp_c_logistics_id \n" +
            "FROM\n" +
            "\tst_c_express_allocation a\n" +
            "\tJOIN st_c_express_allocation_item b ON a.id = b.st_c_express_allocation_id \n" +
            "WHERE\n" +
            "\ta.cp_c_phy_warehouse_id = #{warehouseId}  AND b.cp_c_shop_id = #{cpShopId}\n")
    List<Long> selectExpressAllocation(@Param("warehouseId") Long warehouseId, @Param("cpShopId") Long cpShopId);

    class ExpressByPhyWarehouseSqlProvider {

        public String select(ExpressAllocationRequest queryRequest) {
            StringBuilder sql = new StringBuilder("SELECT\n" +
                    " a.cp_c_logistics_id ,l.ename\n" +
                    "FROM\n" +
                    " st_c_express_allocation_item a\n" +
                    " INNER JOIN st_c_express_allocation b ON b.id = a.st_c_express_allocation_id \n" +
                    " INNER JOIN CP_C_LOGISTICS l on l.id = a.cp_c_logistics_id \n" +
                    "WHERE\n" +
                    "  a.isactive='Y' AND b.isactive='Y' ");

            sql.append(" and b.cp_c_phy_warehouse_id =  " + queryRequest.getCpCPhyWarehouseId());
            if (StringUtils.isNotEmpty(queryRequest.getKeyword())) {
                sql.append(" and l.ename  like '" + queryRequest.getKeyword() + "%'");
            }
            String excSql = sql.toString();
            return excSql;
        }

    }
}