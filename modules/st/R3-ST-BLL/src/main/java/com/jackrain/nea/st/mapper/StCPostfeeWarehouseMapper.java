package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPostfeeWarehouseDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCPostfeeWarehouseMapper extends ExtentionMapper<StCPostfeeWarehouseDO> {
    @Select("select * from st_c_postfee_warehouse where st_c_postfee_id = #{id}")
    List<StCPostfeeWarehouseDO> selectWarehouseByPostfeeId(@Param("id") Long id);

    @Select("select * from st_c_postfee_warehouse where cp_c_phy_warehouse_id = #{warehouseid} and st_c_postfee_id = #{postfeeid}")
    List<StCPostfeeWarehouseDO> selectWarehouseByWarehouseIdAndPostfeeId(@Param("warehouseid") Long warehouseid, @Param("postfeeid") Long postfeeid);

    @Delete("delete from st_c_postfee_warehouse where st_c_postfee_id = #{postfeeid}")
    int deletePostfeeWarehouseByPostfeeId(@Param("postfeeid") Long postfeeid);

    /**
     *  根据运费方案id查询运费方案仓库明细
     * @param stCPostfeeId 运费方案id
     * @return List<StCPostfeeItem>
     */
    @Select("SELECT * FROM ST_C_POSTFEE_WAREHOUSE WHERE st_c_postfee_id = #{stCPostfeeId} AND ISACTIVE='Y'")
    List<StCPostfeeWarehouseDO> selectStCPostfeeWarehouse(@Param("stCPostfeeId") Long stCPostfeeId);
}