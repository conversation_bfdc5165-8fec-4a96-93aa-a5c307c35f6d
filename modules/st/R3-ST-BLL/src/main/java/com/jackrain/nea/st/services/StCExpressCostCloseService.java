package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.mapper.StCExpressCostItemMapper;
import com.jackrain.nea.st.mapper.StCExpressCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.table.StCExpressCost;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/8/8 16:19
 * @Description
 */
@Component
@Slf4j
public class StCExpressCostCloseService extends CommandAdapter {

    @Autowired
    private StCExpressCostMapper stCExpressCostMapper;

    @Autowired
    private StCExpressCostItemMapper stCExpressCostItemMapper;

    @StOperationLog(operationType = "FINISH", mainTableName = "ST_C_EXPRESS_COST", itemsTableName = "ST_C_EXPRESS_COST_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray voidArray = StBeanUtils.makeUnAuditJsonArray(param);
        if (voidArray.size() == 1) {
            voidAction(voidArray.getLong(0),querySession);
        }else {
            int success = 0;
            int fail = 0;
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = voidArray.getLong(i);
                try {
                    voidAction(id,querySession);
                    success++;
                } catch (Exception e) {
                    fail++;
                }
            }
            if (success != voidArray.size()) {
                return ValueHolderUtils.getFailValueHolder("快运报价设置结案成功" + success + "条,失败" + fail + "条");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("结案成功");
    }

    private void voidAction(Long id, QuerySession querySession) {
        User user = querySession.getUser();
        StCExpressCost expressCost = stCExpressCostMapper.selectById(id);
        //判断主表是否存在
        if (expressCost == null){
            throw new NDSException("当前记录不存在！");
        }
        if (!SubmitStatusEnum.SUBMIT.getKey().equals(expressCost.getStatus())){
            throw new NDSException("当前单据状态，不允许结案！");
        }
        if (!CloseStatusEnum.NO_CLOSE.getKey().equals(expressCost.getCloseStatus())){
            throw new NDSException("当前单据状态，不允许结案！");
        }
        StCExpressCost newCost = new StCExpressCost();
        newCost.setId(expressCost.getId());
        newCost.setCloseStatus(CloseStatusEnum.CLOSE.getKey());
        newCost.setCloserid(Long.valueOf(user.getId()));
        newCost.setCloseTime(new Date());
        newCost.setIsactive(YesNoEnum.N.getKey());
        StBeanUtils.makeModifierField(newCost, querySession.getUser());
        int update = stCExpressCostMapper.updateById(newCost);
        if (update <= 0) {
            throw new NDSException("结案失败！");
        }
    }
}
