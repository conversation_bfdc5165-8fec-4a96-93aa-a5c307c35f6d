package com.jackrain.nea.st.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPriceMapper;
import com.jackrain.nea.st.model.table.StCPriceDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Descroption 商品价格策略-作废逻辑
 * <AUTHOR>
 * @Date 2019/3/11
 */
@Component
@Slf4j
@Transactional
public class PriceVoidService extends CommandAdapter {
    @Autowired
    private StCPriceMapper stCPriceMapper;

    @Override
    @StOperationLog(operationType = "VOID", mainTableName = "ST_C_PRICE", itemsTableName = "ST_C_PRICE_EXCLUDE_ITEM")
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        JSONArray itemArray = StBeanUtils.makeVoidJsonArray(param);
        JSONArray errorArray = new JSONArray();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                savePriceByID(querySession, itemid, errorArray);
            }
        }
        return StBeanUtils.getProcessValueHolder(itemArray, errorArray, StConstant.CON_BILL_ACTION_VOID);
    }

    private void savePriceByID(QuerySession session, Long id, JSONArray errorArray) {
        StCPriceDO stCPriceDO = stCPriceMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkPriceStatus(stCPriceDO, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }
        StBeanUtils.makeModifierField(stCPriceDO, session.getUser());//修改信息
        stCPriceDO.setIsactive(StConstant.ISACTIVE_N);//作废状态
        stCPriceDO.setEstatus(StConstant.CON_BILL_STATUS_03);
        stCPriceDO.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        stCPriceDO.setDelTime(new Date());//作废时间
        stCPriceDO.setDelname(session.getUser().getName());//作废人姓名
        stCPriceDO.setDelename(session.getUser().getEname());//作废人账号
        stCPriceDO.setModifierename(session.getUser().getEname());//修改人账号
        if ((stCPriceMapper.updateById(stCPriceDO)) <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "方案名称:" + stCPriceDO.getEname() + ",作废失败！"));
        } else {
            Long shopId = StringUtils.isBlank(stCPriceDO.getCpCShopId()) ? null : Long.valueOf(stCPriceDO.getCpCShopId());
            RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY);
            RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY_INFO);

        }
    }

    private void checkPriceStatus(StCPriceDO stCPriceDO, Long id, JSONArray errorArray) {
        if (stCPriceDO == null) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已不存在！"));
            return;
        }
        //不是未作废，不允许作废
        if (stCPriceDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已作废，不允许作废！"));
            return;
        }

        //不是未审核，不允许作废
        if (stCPriceDO.getEstatus() == null
                || !StConstant.CON_BILL_STATUS_01.equals(stCPriceDO.getEstatus())) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录不是未审核，不允许作废！"));
            return;
        }
    }
}
