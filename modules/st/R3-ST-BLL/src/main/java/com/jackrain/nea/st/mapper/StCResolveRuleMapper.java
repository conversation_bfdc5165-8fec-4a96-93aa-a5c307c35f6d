package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCResolveRuleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper
public interface StCResolveRuleMapper extends ExtentionMapper<StCResolveRuleDO> {
    @Select("SELECT * from st_c_resolve_rule where cp_c_platform_id=#{platformId} ORDER BY rank asc,creationdate desc")
    List<StCResolveRuleDO> selectByPlatformId(@Param("platformId") Long platformId);

    @Select("SELECT * from st_c_resolve_rule where cp_c_platform_id=#{platformId} and id in(${ids}) ORDER BY rank asc,creationdate desc")
    List<StCResolveRuleDO> selectAnalysisDataByPlatformId(@Param("platformId")Long platformId,@Param("ids") String ids);

    @Select("select cp_c_platform_id  from st_c_resolve_rule where id=#{id}")
    Long selectPlatformIdById(@Param("id")Long id);
}