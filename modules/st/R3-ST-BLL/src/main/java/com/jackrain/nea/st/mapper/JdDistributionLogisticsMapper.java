package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.JdDistributionLogistics;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName JdDistributionLogisticsMapper
 * @Description 京东分销商物流
 * <AUTHOR>
 * @Date 2022/12/9 13:46
 * @Version 1.0
 */
@Mapper
public interface JdDistributionLogisticsMapper extends ExtentionMapper<JdDistributionLogistics> {

    @Delete("delete from jd_distribution_logistics where jd_distribution_id = #{jdDistributionId}")
    int deleteByDistributionId(@Param("jdDistributionId") Long jdDistributionId);

    @Select("select * from jd_distribution_logistics where jd_distribution_id = #{distributionId}")
    List<JdDistributionLogistics> getByDistributionId(@Param("distributionId") Long jdDistributionId);
}
