package com.jackrain.nea.st.mapper;




import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.AcFManageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AcFManageMapper extends ExtentionMapper<AcFManageDO> {
    @Select("SELECT * FROM AC_F_MANAGE WHERE ID=#{managerId} AND ISACTIVE='Y'")
    List<AcFManageDO> selectUsefulManagerById(@Param("managerId") Long managerId);

    /**
     * 根据ecode查询对应的经营主体
     *
     * @param ecode 编码
     * @return  返回经营主体实体
     */
    @Select("SELECT * FROM AC_F_MANAGE WHERE ECODE=#{ecode} AND ISACTIVE='Y' LIMIT 1")
    AcFManageDO selectUsefulManagerByEcode(@Param("ecode") String ecode);

    /**
     * 根据店仓eCode查询对应的经营主体
     *
     * @param storeECode 店铺eCode
     * @return 经营主体
     */
    @Select("SELECT " +
            " M .* " +
            "FROM " +
            " ac_f_manage_storage S " +
            "INNER JOIN ac_f_manage M ON S.ac_f_manage_id = M .\"id\" " +
            "WHERE " +
            " S.cp_c_store_ecode = #{storeECode} " +
            "AND M .isactive = 'Y' " +
            "AND S.isactive = 'Y';")
    AcFManageDO selectByStoreECode(@Param("storeECode") String storeECode);



    /**
     * 根据店仓id查询对应的经营主体
     *
     * @param storeId 店铺id
     * @return 经营主体
     */
    @Select("SELECT " +
            " M .* " +
            "FROM " +
            " ac_f_manage_storage S " +
            "INNER JOIN ac_f_manage M ON S.ac_f_manage_id = M .\"id\" " +
            "WHERE " +
            " S.cp_c_store_id = #{storeId} " +
            "AND M .isactive = 'Y' " +
            "AND S.isactive = 'Y' ")
    AcFManageDO selectByStoreId(@Param("storeId") Long storeId);



    /**
     * 根据店仓id查询对应的经营主体
     *
     * @param shopId 店铺id
     * @return 经营主体
     */
    @Select("SELECT " +
            " M .* " +
            "FROM " +
            " ac_f_manage_shop S " +
            "INNER JOIN ac_f_manage M ON S.ac_f_manage_id = M .\"id\" " +
            "WHERE " +
            " S.cp_c_shop_id = #{shopId} " +
            "AND M .isactive = 'Y' " +
            "AND S.isactive = 'Y' ")
    AcFManageDO selectByShopId(@Param("shopId") Long shopId);
}