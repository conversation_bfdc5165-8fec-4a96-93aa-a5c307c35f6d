package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCExpressProItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface StCExpressProItemMapper extends ExtentionMapper<StCExpressProItemDO> {

    /**
     * 根据物流公司id，查询物流公司商品明细
     * @param stCExpressId
     * @return List<StCExpressProItem>
     */
    @Select("SELECT * FROM ST_C_EXPRESS_PRO_ITEM WHERE ST_C_EXPRESS_ID=#{stCExpressId} AND ISACTIVE='Y'")
    List<StCExpressProItemDO> selectStCExpressProItem(@Param("stCExpressId") Long stCExpressId);

    @UpdateProvider(type = StCExpressProItemMapper.StCExpressProItemAttribute.class, method = "updateSql")
    int updateAtrributes(JSONObject entity);

    class  StCExpressProItemAttribute {

        public String updateSql(JSONObject entity) {
            return new SQL() {
                {
                    UPDATE("ST_C_EXPRESS_PRO_ITEM");
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }
}