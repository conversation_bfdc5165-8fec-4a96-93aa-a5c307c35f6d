package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCEwaybillLogisticsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface StCEwaybillLogisticsMapper extends ExtentionMapper<StCEwaybillLogisticsDO> {

    @Select("SELECT * FROM ST_C_EWAYBILL_LOGISTICS WHERE ST_C_EWAYBILL_ID = #{ewaybillId}")
    List<StCEwaybillLogisticsDO> selectItemByMainId(@Param("ewaybillId") Long ewaybillId);

    @SelectProvider(type = EwaybillLogisticsSql.class,method = "queryEwaybillLogisticsByIds")
    List<StCEwaybillLogisticsDO> queryEwaybillLogisticsByIds(@Param("ewaybillId")Long ewaybillId, @Param("logisticsIds")String logisticsIds);
    class EwaybillLogisticsSql {
        public String queryEwaybillLogisticsByIds(@Param("ewaybillId") Long ewaybillId,@Param("logisticsIds") String logisticsIds){
            StringBuffer sb = new StringBuffer();
            sb.append("SELECT                                                               \n");
            sb.append("    *                                                                \n");
            sb.append("FROM                                                                 \n");
            sb.append("     ST_C_EWAYBILL_LOGISTICS                                         \n");
            sb.append("WHERE                                                                \n");
            sb.append("    ST_C_EWAYBILL_ID = "+ewaybillId+" AND                            \n");
            sb.append("    ID NOT IN ("+logisticsIds+")                                     \n");
            return sb.toString();
        }
    }

    @Select("SELECT c.cp_c_logistics_id FROM st_c_ewaybill a inner join st_c_ewaybill_shop b on \n" +
            "b.st_c_ewaybill_id = a.id inner join st_c_ewaybill_logistics c on \n" +
            "c.st_c_ewaybill_id = a.id WHERE b.cp_c_shop_id = #{cpCShopId} and c.isactive='Y' and a.isactive='Y' and b.isactive='Y'")
    List<Long> selectLogisticsIdsByCpCShopId(@Param("cpCShopId") Long cpCShopId);
}