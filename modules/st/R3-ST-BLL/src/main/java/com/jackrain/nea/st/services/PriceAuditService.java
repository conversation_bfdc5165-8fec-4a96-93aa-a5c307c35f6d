package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPriceItemMapper;
import com.jackrain.nea.st.mapper.StCPriceMapper;
import com.jackrain.nea.st.model.common.StCConstants;
import com.jackrain.nea.st.model.table.StCPriceDO;
import com.jackrain.nea.st.model.table.StCPriceItemDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Descroption 商品价格策略-审核
 * <AUTHOR>
 * @Date 2019/3/21 15:43
 */
@Component
@Slf4j
@Transactional
public class PriceAuditService extends CommandAdapter {
    @Autowired
    private StCPriceMapper stCPriceMapper;
    @Autowired
    private StCPriceItemMapper stCPriceItemMapper;

    @Override
    @StOperationLog(operationType = "AUDIT", mainTableName = "ST_C_PRICE", itemsTableName = "ST_C_PRICE_EXCLUDE_ITEM")
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("PriceAuditService.param=") + param.toJSONString());
        }
        JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
        JSONArray errorArray = new JSONArray();
        HashMap<Long, Object> errMap = new HashMap();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                try {
                    //4.遍历结案方法
                    savePriceByID(querySession, itemid, errorArray);
                } catch (Exception e) {
                    errMap.put(itemid, e.getMessage());
                }
            }
        }
        return StBeanUtils.getExcuteValueHolder(itemArray.size(), errMap);
    }

    private void savePriceByID(QuerySession session, Long id, JSONArray errorArray) {
        StCPriceDO stCPriceDO = stCPriceMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkPriceStatus(stCPriceDO, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }
        StBeanUtils.makeModifierField(stCPriceDO, session.getUser());//修改信息
        stCPriceDO.setModifierename(session.getUser().getEname());//修改人账号
        stCPriceDO.setEstatus(StConstant.CON_BILL_STATUS_02);
        stCPriceDO.setCheckid(Long.valueOf(session.getUser().getId()));//审核人
        stCPriceDO.setChecktime(new Date());//审核时间
        stCPriceDO.setCheckname(session.getUser().getName());//审核人姓名
        stCPriceDO.setCheckename(session.getUser().getEname());//审核人账号
        if ((stCPriceMapper.updateById(stCPriceDO)) <= 0) {
            throw new NDSException("活动名称:" + stCPriceDO.getEname() + ",审核失败！");
        }else{
            Long shopId = StringUtils.isBlank(stCPriceDO.getCpCShopId()) ? null : Long.valueOf(stCPriceDO.getCpCShopId());
            RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY);
            RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY_INFO);
        }
    }

    private void checkPriceStatus(StCPriceDO stCPriceDO, Long id, JSONArray errorArray) {
        if (stCPriceDO == null) {
            throw new NDSException("当前记录已不存在！");
        }
        //不是未审核，不允许审核
        if (stCPriceDO.getEstatus() == null
                || !StConstant.CON_BILL_STATUS_01.equals(stCPriceDO.getEstatus())) {
            throw new NDSException("当前记录不是未审核，不允许审核！");
        }


        //商品范围
        Integer proRange = stCPriceDO.getProRange();
        if(StCConstants.ST_C_PRICE_PRO_RANGE_2.equals(proRange)){
            //商品价格策略-明细
            HashMap<String, Object> map = new HashMap<>();
            map.put("st_c_price_id", id);
            List<StCPriceItemDO> stCPriceItemDOList = stCPriceItemMapper.selectByMap(map);
            if (stCPriceItemDOList.isEmpty() || stCPriceItemDOList.size() <= 0) {
                throw new NDSException("当前商品价格策略-明细表没有记录，不允许审核！");
            }
        }
    }
}
