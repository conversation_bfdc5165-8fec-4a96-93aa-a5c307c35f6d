package com.jackrain.nea.st.services.message;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.encrypt.Md5Encrypt;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpCPlatformQueryCmd;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCUnfullcarCostItemMapper;
import com.jackrain.nea.st.mapper.message.StCSendMessageMapper;
import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import com.jackrain.nea.st.model.table.message.StCSendMessage;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.jackrain.nea.st.common.StConstant.TEMPLATE_2303010652690653;
import static com.jackrain.nea.st.common.StConstant.TEMPLATE_2304141895892956;

/**
 * @Auther: chenhao
 * @Date: 2022-09-12 16:38
 * @Description:
 */

@Slf4j
@Component
public class OcBSendMessageService {

    @Autowired
    private StCSendMessageMapper messageMapper;
    @Autowired
    private StCUnfullcarCostItemMapper unfullcarCostItemMapper;

    @Reference(group = "cp-ext", version = "1.0")
    private CpCPlatformQueryCmd cpCPlatformQueryCmd;

    @NacosValue(value = "${r3.st.send.message.developerKey:223d8188402beeec9bdb4eba926a4ba6}", autoRefreshed = true)
    private String developerKey;

    @NacosValue(value = "${r3.st.send.message.url:http://gateway-pro.sms.burgeonyun.com/v1/sms/sendSms}", autoRefreshed = true)
    private String url;

    /**
     * 系统参数 redis key
     */
    private final static String R3_ST_C_SEND_MESSAGE_MERCHANTS_BILL_NO = "business_system:r3_st_c_send_message_merchants_bill_no";

    private final static String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private final static String MD5 = "MD5";

    public ValueHolderV14 sendMessage() {

        log.info(LogUtil.format("OcBSendMessageService.sendMessage start", "OcBSendMessageService.sendMessage"));

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "周期购短信发送定时任务执行成功！");

        List<StCSendMessage> ocSendMessages = messageMapper.selectSendMessage();

        if (CollectionUtils.isNotEmpty(ocSendMessages)) {
            for (StCSendMessage message : ocSendMessages) {
                JSONObject returnStr = new JSONObject();
                try {
                    returnStr = buildMessage(message);
                    log.info(LogUtil.format("returnStr:{}", "buildMessage.requset"), returnStr.toJSONString());
                    String post = post(returnStr, message);
                    log.info(LogUtil.format("post:{}", "buildMessage.response"), post);
                    if (StringUtils.isNotEmpty(post)) {
                        success(message.getId(), JSONObject.toJSONString(returnStr));
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("exception_has_occured:{}", "sendMessage.error"), Throwables.getStackTraceAsString(e));
                    fail(message, e.getMessage(), JSONObject.toJSONString(returnStr));
                }
            }
        }


        return v14;
    }

    /**
     * 短信参数设置
     *
     * @param message message
     * @return JSONObject
     */
    private JSONObject buildMessage(StCSendMessage message) {
        JSONObject returnStr = new JSONObject();
        returnStr.put("cus_no", getSendMessageMerchantsBillNo());
        returnStr.put("timestamp", getNow());
        returnStr.put("phone_numbers", message.getMobile());
        returnStr.put("platform_template_code", message.getBurgeonMessageTemplateNumber());
        returnStr.put("template_param", getTemplateParam(message).toJSONString());
        returnStr.put("platform_sign_code", message.getBurgeonPlatformSignatureNumber());

        Long cpPlatformId = message.getCpCPlatformId();
        if (cpPlatformId != null) {
            if (PlatFormEnum.TAOBAO.getCode().equals(cpPlatformId.intValue()) ||
                    PlatFormEnum.DOU_YIN.getCode().equals(cpPlatformId.intValue())) {
                returnStr.put("sellernick", message.getSellerNick());
                returnStr.put("tid", message.getTid());
                returnStr.put("oaid", message.getOaid());
            }
        }

        log.info(LogUtil.format("cpPlatformId:{}", "buildMessage.cpPlatformId"), cpPlatformId);

        String sign = getSign(returnStr);
        log.info(LogUtil.format("sign:{}", "buildMessage.sign"), sign);

        returnStr.put("sign", sign);

        return returnStr;
    }

    /**
     * sign 签名规则 加密
     *
     * @param returnStr returnStr
     * @return String
     */
    private String getSign(JSONObject returnStr) {

        List<String> keySet = new ArrayList<>(returnStr.keySet());
        Collections.sort(keySet);

        StringBuilder builder = new StringBuilder();
        for (String key : keySet) {
            String value = returnStr.getString(key);
            if (StringUtils.isNotEmpty(value)) {
                builder.append(key).append("=").append(value).append("&");
            }
        }

        String str = builder.toString();
        String substring = str.substring(0, str.length() - 1);
        log.info(LogUtil.format("substring:{},developerKey:{}", "buildMessage.getSign"), substring, developerKey);

        return Md5Encrypt.encrypt(substring + "223d8188402beeec9bdb4eba926a4ba6");
    }

    /**
     * 获取系统参数
     * r3_st_c_send_message_merchants_bill_no
     *
     * @return 短信发送商家编号
     */
    private String getSendMessageMerchantsBillNo() {
        try {
            String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(R3_ST_C_SEND_MESSAGE_MERCHANTS_BILL_NO);
            log.info("短信发送商家编号 value：{}", value);
            return value;
        } catch (Exception e) {
            log.error("获取短信发送商家编号异常：{}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取当前时间
     * ps：返回String类型 并且 yyyy-MM-dd HH:mm:ss 格式
     *
     * @return 当前时间
     */
    private String getNow() {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        return sdf.format(new Date());
    }

    /**
     * 您好，牛主人，您购买的冷藏娟姗牛乳已发货！快递单号：${ expresscode } 请你注意查收。
     * 温馨提示：冷藏奶需在2℃-6℃条件下冷藏储存，保质期为18天，请尽快品尝，留住美味。
     *
     * @param message 入参
     * @return JSONObject
     */
    private JSONObject getTemplateParam(StCSendMessage message) {
        JSONObject param = new JSONObject();
        // 根据不同的模板设置不同的参数
        if (ObjectUtil.equal(TEMPLATE_2303010652690653, message.getBurgeonMessageTemplateNumber())) {
            param.put("expresscode", message.getLogisticsNumber());
        } else if (ObjectUtil.equal(TEMPLATE_2304141895892956, message.getBurgeonMessageTemplateNumber())) {
            if (ObjectUtil.isNull(message.getScanTime())) {
                message.setScanTime(new Date());
            }
            Integer arrivalDays = 5;
            if (ObjectUtil.isAllNotEmpty(message.getCpCLogisticsId(), message.getCpCPhyWarehouseId(), message.getProvinceId())) {
                List<StCUnfullcarCostItem> unfullcarCostItemList = unfullcarCostItemMapper.listUnfullcardItem(message.getCpCLogisticsId(), message.getCpCPhyWarehouseId(), message.getProvinceId());
                if (CollectionUtils.isNotEmpty(unfullcarCostItemList)) {
                    StCUnfullcarCostItem unfullcarCostItem = unfullcarCostItemList.get(0);
                    arrivalDays = unfullcarCostItem.getArrivalDays();
                }
            }
            Date arrivalDate = DateUtil.offsetDay(message.getScanTime(), arrivalDays);
            param.put("orderConfirmTime", (DateUtil.month(message.getScanTime()) + 1) + "月" + DateUtil.dayOfMonth(message.getScanTime()) + "日");
            param.put("warehouse", message.getCpCPhyWarehouseEname());
            param.put("arrivaltime", (DateUtil.month(arrivalDate) + 1) + "月" + DateUtil.dayOfMonth(arrivalDate) + "日");
            Map<String, Object> jsonMap = new LinkedHashMap<>();
            jsonMap.put("orderConfirmTime", param.get("orderConfirmTime"));
            jsonMap.put("warehouse", param.get("warehouse"));
            jsonMap.put("arrivaltime", param.get("arrivaltime"));
            param = new JSONObject(jsonMap);
        }
        return param;
    }

    /**
     * 发送成功
     *
     * @param id  中间表id
     * @param str 报文
     */
    private void success(Long id, String str) {
        StCSendMessage message = new StCSendMessage();
        message.setInterfaceStatus(StConstant.INTERFACE_STATUS_SUCCESS);
        message.setId(id);
        messageMapper.update(message, new LambdaUpdateWrapper<StCSendMessage>()
                .set(StCSendMessage::getFailReason, null)
                .set(StCSendMessage::getInterfaceStatus, StConstant.INTERFACE_STATUS_SUCCESS)
                .set(StCSendMessage::getSendTime, new Date())
                .set(StCSendMessage::getMessage, str)
                .eq(StCSendMessage::getId, id));
    }

    /**
     * 发送失败
     *
     * @param message 中间表
     * @param fail    失败原因
     * @param str     报文
     */
    private void fail(StCSendMessage message, String fail, String str) {
        StCSendMessage update = new StCSendMessage();
        update.setInterfaceStatus(StConstant.INTERFACE_STATUS_FAIL);
        update.setId(message.getId());
        if (StringUtils.isNotEmpty(fail) && fail.length() > 500) {
            update.setFailReason(fail.substring(0, 500));
        } else {
            update.setFailReason(fail);
        }

        Integer count = Optional.ofNullable(message.getFailCount()).orElse(0);
        update.setFailCount(count + 1);
        update.setSendTime(new Date());
        update.setMessage(str);
        messageMapper.updateById(update);
    }

    /**
     * http请求
     *
     * @param jsonBody 请求实体
     * @param message  中间表
     * @return 请求返回值
     */
    public String post(JSONObject jsonBody, StCSendMessage message) {

        String body = jsonBody.toJSONString();
        log.info(LogUtil.format("post：url:{},jsonBody:{}", "OcBSendMessageService.post"), url, body);

//        String requestUrl = url + "?appkey=" + skxWingConfig.getAppKey() + "&requesttime="
//                + requestTime + "&sign=" + getSign(jsonBody, methodName, requestTime) + "&version=" +
//                SkxWingConfig.VERSION_VAL + "&method=" + methodName;

        String requestUrl = url + "?sign=" + jsonBody.getString("sign");

        RequestConfig.Builder builder = RequestConfig.custom().setConnectTimeout(2000).setSocketTimeout(3000000);
        HttpPost httpPost = new HttpPost(requestUrl);
        StringEntity entity = new StringEntity(body, "utf-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        httpPost.setConfig(builder.build());
        String result = "";

        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(httpPost)) {
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                log.info("HTTP请求成功!");
                // 从响应模型中获取响应实体
                org.apache.http.HttpEntity responseEntity = response.getEntity();
                if (responseEntity != null) {
                    String res = EntityUtils.toString(responseEntity);
                    JSONObject jsonObject = JSONObject.parseObject(res);
                    String statusCode = jsonObject.getString("status_code");
                    if (StringUtils.isNotEmpty(statusCode) && "FAIL".equals(statusCode)) {
                        fail(message, "HTTP请求成功!第三方返回请求失败：" + jsonObject.getString("err_msg"), JSONObject.toJSONString(jsonBody));
                    } else {
                        result = res;
                    }
                }

            } else {
                log.info("HTTP请求失败!");
                fail(message, "HTTP请求失败!", JSONObject.toJSONString(jsonBody));
                return null;
            }
            log.info("返回结果:{}", JSONObject.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("HTTP请求出现异常：", e);
            fail(message, e.getMessage(), JSONObject.toJSONString(jsonBody));
            return null;
        }
    }

}
