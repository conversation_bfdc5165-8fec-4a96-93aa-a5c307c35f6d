package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPostfeeItemDO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;

@Mapper
public interface StCPostfeeItemMapper extends ExtentionMapper<StCPostfeeItemDO> {

    @Select("select * from st_c_postfee_item where st_c_postfee_id = #{id}")
    List<StCPostfeeItemDO> selectPostfeeById(@Param("id") Long id);

    @Select("select * from st_c_postfee_item where cp_c_logistics_id = #{logisticsid} and st_c_postfee_id = #{postfeeid} and area_rank = #{arearank}")
    List<StCPostfeeItemDO> selectPostfeeByLogisticsIdAndIdAndArearank(@Param("logisticsid") Long logisticsid, @Param("postfeeid") Long postfeeid, @Param("arearank") Long arearank);

    @Delete("delete from st_c_postfee_item where st_c_postfee_id = #{postfeeid}")
    int deletePostfeeItemByPostfeeId(@Param("postfeeid") Long postfeeid);

    @UpdateProvider(type = StCPostfeeItemMapper.StCPostfeeItemAttribute.class, method = "updateSql")
    int updateAtrributes(JSONObject entity);

    class StCPostfeeItemAttribute {

        public String insertSql(JSONObject entity) {
            return new SQL() {
                {
                    INSERT_INTO("st_c_postfee_item");
                    for (String key : entity.keySet()) {
                        if (entity.get(key) != null) {
                            VALUES(key, "#{" + key + "}");
                        }
                    }
                }
            }.toString();
        }

        public String updateSql(JSONObject entity) {
            return new SQL() {
                {
                    UPDATE("st_c_postfee_item");
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }

    /**
     *  根据运费方案id查询运费方案明细
     * @param stCPostfeeId 运费方案id
     * @param logisticsId 物流公司
     * @param provinceId 省份id
     * @param cityId 城市id
     * @param areaId 地区id
     * @return List<StCPostfeeItem>
     */
    @Select("<script> SELECT * FROM ST_C_POSTFEE_ITEM WHERE "
            + "st_c_postfee_id = #{stCPostfeeId} AND cp_c_logistics_id = #{logisticsId} "
            + " AND cp_c_region_province_id = #{provinceId} "
            + "<if test=\"cityId != null\"> AND cp_c_region_city_id = #{cityId} </if>"
            + " <if test=\"areaId != null\"> AND cp_c_region_area_id = #{areaId}  </if> AND ISACTIVE='Y' </script> ")
    List<StCPostfeeItemDO> selectStCPostfeeItem(@Param("stCPostfeeId") Long stCPostfeeId, @Param("logisticsId") Long logisticsId,
                                              @Param("provinceId") Long provinceId, @Param("cityId") Long cityId, @Param("areaId") Long areaId);
}