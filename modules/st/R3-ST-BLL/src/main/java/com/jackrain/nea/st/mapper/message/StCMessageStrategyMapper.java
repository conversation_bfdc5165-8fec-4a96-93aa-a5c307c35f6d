package com.jackrain.nea.st.mapper.message;


import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.st.model.table.message.StCMessageStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface StCMessageStrategyMapper extends ExtentionMapper<StCMessageStrategy> {

    /**
     * 短信发送查询adb 订单sql
     */
    @Select("<script>" +
            " select count(1) from ${rptOrder}.oc_b_order " +
            " where is_history = 'N' " +
            " and WAREHOUSE_DELIVERY_TIME &lt;= #{now} " +
            " <if test = 'finalDate != null'>" +
            " and WAREHOUSE_DELIVERY_TIME &gt;= #{finalDate} " +
            " </if>" +
            " and cp_c_shop_id in " +
            " <foreach collection= 'shopIdList'  item= 'item' separator= ',' open= '(' close= ')'> #{item} </foreach> " +
            " and business_type_code in " +
            " <foreach collection= 'orderBusinessTypeCode'  item= 'item' separator= ',' open= '(' close= ')'> #{item} </foreach> " +
            " and order_status in " +
            " <foreach collection= 'orderStatus'  item= 'item' separator= ',' open= '(' close= ')'> ${item} </foreach> " +
            "</script>")
    Integer queryOrderCount(@Param("rptOrder") String rptOrder,
                            @Param("shopIdList") List<Long> shopIdList,
                            @Param("orderBusinessTypeCode") List<String> orderBusinessTypeCode,
                            @Param("orderStatus") List<String> orderStatus,
                            @Param("now") Date now,
                            @Param("finalDate") Date finalDate);

    /**
     * 短信发送查询adb 订单sql
     */
    @Select("<script>" +
            " select * from ${rptOrder}.oc_b_order " +
            " where is_history = 'N' " +
            " and WAREHOUSE_DELIVERY_TIME &lt;= #{now} " +
            " <if test = 'finalDate != null'>" +
            " and WAREHOUSE_DELIVERY_TIME &gt;= #{finalDate} " +
            " </if>" +
            " and cp_c_shop_id in " +
            " <foreach collection= 'shopIdList'  item= 'item' separator= ',' open= '(' close= ')'> #{item} </foreach> " +
            " and business_type_code in " +
            " <foreach collection= 'orderBusinessTypeCode'  item= 'item' separator= ',' open= '(' close= ')'> #{item} </foreach> " +
            " and order_status in " +
            " <foreach collection= 'orderStatus'  item= 'item' separator= ',' open= '(' close= ')'> ${item} </foreach> " +
            " limit #{limitStart},#{limitEnd}" +
            "</script>")
    List<OcBOrder> queryOrder(@Param("rptOrder") String rptOrder,
                              @Param("shopIdList") List<Long> shopIdList,
                              @Param("orderBusinessTypeCode") List<String> orderBusinessTypeCode,
                              @Param("orderStatus") List<String> orderStatusCode,
                              @Param("now") Date now,
                              @Param("finalDate") Date finalDate,
                              @Param("limitStart") Integer limitStart,
                              @Param("limitEnd") Integer limitEnd);
}