package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCAutoCheckDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Mapper
@Component
public interface StCAutoCheckMapper extends ExtentionMapper<StCAutoCheckDO> {

    /**
     * 根据店铺编码 查询 订单自动审核策略的 数据集合
     *
     * @param cpCShopId 店铺编码
     * @return 返回订单合并策略的实体集合
     */
    @Select("SELECT * FROM ST_C_AUTOCHECK WHERE CP_C_SHOP_ID = #{cpCShopId} ")
    List<StCAutoCheckDO> selectBycpCShopId(@Param("cpCShopId") Long cpCShopId);


    /**
     * 查询所有开启自动审核店铺的策略
     *
     * @return
     */
    @Select("SELECT * FROM ST_C_AUTOCHECK WHERE ISACTIVE = 'Y' and IS_AUTOCHECK_ORDER='Y' ")
    List<StCAutoCheckDO> selectEnableAutoAuditList();

    /**
     * 更新订单自动审核策略的店铺名称
     *
     * @param cpCShopTitle
     * @param cpCShopId
     * @return
     */
    @Update("UPDATE ST_C_AUTOCHECK\n" +
            "SET CP_C_SHOP_TITLE = #{cpCShopTitle}, MODIFIERID = #{modifierid}," +
            "MODIFIERENAME = #{modifierename}, MODIFIEDDATE = #{modifieddate}\n" +
            "WHERE CP_C_SHOP_ID = #{cpCShopId}")
    int updateShopTitleByShopId(@Param("cpCShopTitle") String cpCShopTitle,
                                @Param("modifierid") Long modifierid,
                                @Param("modifierename") String modifierename,
                                @Param("modifieddate") Date modifieddate,
                                @Param("cpCShopId") Long cpCShopId);

    /**
     * 更新订单自动审核策略的店铺名称
     *
     * @param cpCShopTitle
     * @param cpCShopId
     * @return
     */
    /*@Update("UPDATE ST_C_AUTOCHECK SET CP_C_SHOP_TITLE = #{cpCShopTitle} WHERE CP_C_SHOP_ID = #{cpCShopId} ")
    int updateShopTitleByShopId(@Param("cpCShopTitle") String cpCShopTitle,
                                @Param("cpCShopId") Long cpCShopId);*/


    /**
     * 查询店铺自动审核策略
     *
     * @param shopId 店铺Id
     * @return OcStCAutocheck
     */
    @Select("SELECT * FROM St_C_Autocheck WHERE cp_c_shop_id=#{shopId} AND isactive='Y'")
    StCAutoCheckDO queryOcStCAutocheck(Long shopId);

    @UpdateProvider(type = StCAutoCheckMapper.StCAutocheckAttribute.class, method = "updateSql")
    int updateAtrributes(JSONObject entity);

    class StCAutocheckAttribute {

        public String updateSql(JSONObject entity) {
            entity.remove("tableName");
            return new SQL() {
                {
                    UPDATE("st_c_autocheck");
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            if (StringUtils.isEmpty(entity.getString(key))) {
                                SET(key + "=" + "NULL");
                            } else {
                                SET(key + "=" + "#{" + key + "}");
                            }
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }
}