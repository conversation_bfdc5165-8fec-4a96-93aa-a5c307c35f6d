package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPickUpTimeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCPickUpTimeMapper extends ExtentionMapper<StCPickUpTimeDO> {

    @Select("SELECT t1.* FROM st_c_pick_up_time t1 " +
            "INNER JOIN st_c_pick_up_time_item t2 ON t1.id = t2.st_c_pick_up_time_id " +
            "WHERE t1.cp_c_logistics_id = #{cpCLogisticsId} and t2.cp_c_phy_warehouse_id = #{cpCPhyWarehouseId} and t1.isactive = 'Y' and t2.isactive = 'Y'")
    List<StCPickUpTimeDO> selectTimeByWarehouseLogistics(@Param("cpCLogisticsId") Long cpCLogisticsId, @Param("cpCPhyWarehouseId") Long cpCPhyWarehouseId);

    @Select("SELECT t1.* FROM st_c_pick_up_time t1 " +
            "INNER JOIN st_c_pick_up_time_item t2 ON t1.id = t2.st_c_pick_up_time_id " +
            "WHERE t1.cp_c_logistics_id = #{cpCLogisticsId} and t2.cp_c_phy_warehouse_id = #{cpCPhyWarehouseId} and t1.isactive = 'Y'")
    List<StCPickUpTimeDO> selectCheckByWarehouseLogistics(@Param("cpCLogisticsId") Long cpCLogisticsId, @Param("cpCPhyWarehouseId") Long cpCPhyWarehouseId);
}