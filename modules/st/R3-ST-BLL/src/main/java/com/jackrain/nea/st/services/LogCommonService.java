package com.jackrain.nea.st.services;

import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOperationLogMapper;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.st.model.table.StCOperationLogDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description 日志操作基类
 * @author:洪艺安
 * @since: 2020/2/18
 * @create at : 2020/2/18 14:58
 */
@Component
public class LogCommonService {

    @Resource
    private StCOperationLogMapper stCOperationLogMapper;

    /**
     * @param joinPoint
     * @param operationLog
     * @param returnValue
     * @return void
     * @Description 生成日志
     * <AUTHOR>
     * @date 2020/2/19 16:15
     */
    void generateStLog(JoinPoint joinPoint, StOperationLog operationLog, Object returnValue) {

    }

    /**
     * @Descroption 生成日志对象
     * @param tableName
     * @param operationType
     * @param updateId
     * @param tableDescription
     * @param columnName
     * @param columnBeforeValue
     * @param columnAfterValue
     * @param user
     * @return
     */
    public StCOperationLogDO getOperationLog(String tableName, String operationType, Long updateId, String tableDescription, String columnName, String columnBeforeValue, String columnAfterValue, User user) {
        StCOperationLogDO operationLog = new StCOperationLogDO();
        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_OPERATION_LOG));
        operationLog.setTableName(tableName);
        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
        operationLog.setUpdateId(updateId);
        operationLog.setUpdateModelName(tableDescription);
        operationLog.setModContent(columnName);
        operationLog.setBeforeData(columnBeforeValue);
        operationLog.setAfterData(columnAfterValue);
        StBeanUtils.makeCreateField(operationLog, user);
        operationLog.setOwnerename(user.getEname());
        return operationLog;
    }

    /**
     * 保存日志
     *
     * @param operationLogDO
     */
    public void insertLog(StCOperationLogDO operationLogDO) {
        stCOperationLogMapper.insert(operationLogDO);
    }

    /**
     * 批量保存日志
     *
     * @param operationLogList
     */
    public void batchInsertLog(List<StCOperationLogDO> operationLogList) {
        if (CollectionUtils.isNotEmpty(operationLogList)) {
            stCOperationLogMapper.batchInsert(operationLogList);
        }
    }
}
