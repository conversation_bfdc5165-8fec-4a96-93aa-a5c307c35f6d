package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;


@Mapper
public interface StCDistributionMapper extends ExtentionMapper<StCDistributionDO> {
    @Update("UPDATE ST_C_DISTRIBUTION SET BILL_NO=#{sequence} WHERE id=#{id}")
    void updateDistributionSequence(@Param("sequence") String sequence, @Param("id") Long id);

    /**
     *  根据经营主体查询分销代销
     * @param manageIdSeller 销货方
     * @param manageId 购货方
     * @return List<StCDistributionDO>
     */
    @Select("SELECT * FROM ST_C_DISTRIBUTION WHERE ac_f_manage_id = #{manageId} " +
            "AND ac_f_manage_id_seller = #{manageIdSeller} AND ISACTIVE='Y' AND BILL_STATUS =2 " +
            "AND BEGIN_TIME < #{currentDate} AND END_TIME > #{currentDate} ORDER BY creationdate desc")
    List<StCDistributionDO> selectDistributionByManage(@Param("manageIdSeller") Long manageIdSeller,
                                                       @Param("manageId") Long manageId,
                                                       @Param("currentDate") Date currentDate);
}