package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.request.StCShopStrategyLogisticsSaveRequest;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Mapper
@Component
public interface StCShopStrategyMapper extends ExtentionMapper<StCShopStrategyDO> {

    @Update("<script>" +
            "update st_c_shop_strategy set logistics_type = #{request.logisticsType}" +
            "where id in " +
            "<foreach collection='request.getObjids' item='item' open='(' separator=',' close=')'> #{item} </foreach>" +
            " and isactive = 'Y'" +
            "</script>")
    Integer batchUpdateByIds(@Param("request")StCShopStrategyLogisticsSaveRequest request);

    @Select("<script>" +
            "select * from cp_c_logistics where id in " +
            "<foreach collection='request.getLogisticsList' item='item' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<CpCLogistics> queryLogistics(@Param("request") StCShopStrategyLogisticsSaveRequest request);

    @Select("<script>" +
            "select id,ename,isactive from cp_c_logistics" +
            "</script>")
    List<CpCLogistics> queryAllLogistics();

    /**
     * 查询可用的店铺以及财务核销标识为是(1)。
     *
     * @param isactive
     * @param isWriteoff
     * @return
     */
    @Select("SELECT cp_c_shop_id FROM st_c_shop_strategy\n" +
            "WHERE isactive = #{isactive} AND is_writeoff = #{isWriteoff}")
    List<Long> SelectShopIdsByIsWriteoff(@Param("isactive") String isactive,
                                         @Param("isWriteoff") int isWriteoff);

    /**
     * 查询店铺策略默认仓库
     *
     * @param shopId
     * @return
     */
    @Select("  select cp_c_shop_id,default_store_id from st_c_shop_strategy " +
            "where isactive = 'Y' and cp_c_shop_id = #{shopId} ")
    List<StCShopStrategyDO> selectDefaultStore(@Param("shopId") Long shopId);

    /**
     * 根据店铺编码 查询 店铺策略的 数据集合
     *
     * @param cpCShopId 店铺编码
     * @return 返回店铺策略的实体集合
     */
    @Select("SELECT * FROM ST_C_SHOP_STRATEGY WHERE CP_C_SHOP_ID = #{cpCShopId} ")
    List<StCShopStrategyDO> selectBycpCShopId(@Param("cpCShopId") Long cpCShopId);


    /**
     * 更新店铺策略的店铺名称
     *
     * @param cpCShopTitle 店铺名称
     * @param cpCShopId 店铺编码
     * @return
     */
    @Update("UPDATE ST_C_SHOP_STRATEGY\n" +
            "SET CP_C_SHOP_TITLE = #{cpCShopTitle}, MODIFIERID = #{modifierid}," +
            "MODIFIERENAME = #{modifierename}, MODIFIEDDATE = #{modifieddate}\n" +
            "WHERE CP_C_SHOP_ID = #{cpCShopId}")
    int updateShopTitleByShopId(@Param("cpCShopTitle") String cpCShopTitle,
                                @Param("modifierid") Long modifierid,
                                @Param("modifierename") String modifierename,
                                @Param("modifieddate") Date modifieddate,
                                @Param("cpCShopId") Long cpCShopId);


    /**
     * 查找补差价条码
     *
     * @param shopId       店铺id
     * @param skuEcodeList 明细条码ID
     * @return List<OcStCShopStrategyItem>
     */
    @Select("<script> SELECT b.diffpricesku as diffpricesku FROM st_c_shop_strategy a INNER JOIN "
            + "st_c_shop_strategy_item b ON a.id "
            + "= b.st_c_shop_strategy_id WHERE a.cp_c_shop_id = #{shopId} AND b.diffpricesku in <foreach item='item'"
            + " index='index' collection='skuEcodeList' open='(' separator=',' close=')'> #{item} </foreach> "
            + " AND a.isactive='Y' AND b.isactive='Y' </script>")
    List<String> queryDiffenPriceSku(@Param("shopId") Long shopId,
                                     @Param("skuEcodeList") List<String> skuEcodeList);

    /**
     * 查找店铺退货自动审核策略
     *
     * @param shopId 店铺id
     * @return int
     */
    @Select("SELECT count(*) FROM  st_c_shop_strategy "
            + "WHERE cp_c_shop_id=#{shopId} AND is_auto_audit='Y' AND isactive='Y'  limit 1")
    int queryStCShopStrategyAutoAudit(Long shopId);

    /**
     * 传入店铺和sku,判断该sku是否为补差价条码
     *
     * @param shopId   店铺Id
     * @param skuEcode skuID
     * @return Long
     */
    @Select("SELECT COUNT(1) FROM st_c_shop_strategy a INNER JOIN st_c_shop_strategy_item b ON a.id "
            + " = b.st_c_shop_strategy_id WHERE a.cp_c_shop_id = #{shopId} AND b.diffpricesku=#{skuEcode} "
            + " AND a.isactive='Y' AND b.isactive='Y'")
    Long differPriceSku(@Param("shopId") Long shopId, @Param("skuEcode") String skuEcode);

    /**
     * 查找店铺策略
     *
     * @param shopId 店铺id
     * @return Long
     */
    @Select("SELECT * FROM  st_c_shop_strategy "
            + "WHERE cp_c_shop_id=#{shopId} AND isactive='Y'  limit 1")
    StCShopStrategyDO selectOcStCShopStrategy(Long shopId);

    @Update("<script> UPDATE ST_C_SHOP_STRATEGY\n" +
            "SET is_auto_intercept = '0', MODIFIERID = #{modifierid}," +
            "MODIFIERENAME = #{modifierename}, MODIFIEDDATE = now()\n" +
            "WHERE id in" +
            " <foreach item='item' collection='ids' separator=',' open='(' close=')' > #{item} </foreach> </script>")
    int batchCancelAutoInterceptById(@Param("modifierid") Long modifierid,
                                     @Param("modifierename") String modifierename,
                                     @Param("ids") List<Long> ids);

    @UpdateProvider(type = StCShopStrategyMapper.StCShopStrategyAttribute.class, method = "updateSql")
    int updateAtrributes(JSONObject entity);

    class StCShopStrategyAttribute {

        public String updateSql(JSONObject entity) {
            return new SQL() {
                {
                    UPDATE("st_c_shop_strategy");
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }
}