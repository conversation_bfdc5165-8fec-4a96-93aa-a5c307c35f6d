package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCSendPlanItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface StCSendPlanItemMapper extends ExtentionMapper<StCSendPlanItemDO> {
    @Select("SELECT COUNT(1)\n" +
            "FROM ST_C_SEND_PLAN_ITEM spi \n" +
            "INNER JOIN ST_C_SEND_PLAN sp on (st_c_send_plan_id = sp.id)\n" +
            "WHERE sp.isactive = 'Y' AND ST_C_SEND_RULE_ID = #{sendRuleId}")
    int selectSendPlanItemBySendRuleId(Long sendRuleId);

    @Select("SELECT COUNT(1) FROM ST_C_SEND_PLAN_ITEM spi \n" +
            "WHERE spi.st_c_send_plan_id = #{mainId}")
    int selectItemCountByMainId(Long mainId);

    //根据主表的ID， 查询出所有的按收货地址明细行
    @Select("select * from st_c_send_plan_item where st_c_send_plan_id = #{sendPlanId} ")
    List<StCSendPlanItemDO> listBySendPlanId(@Param("sendPlanId") Long st_c_send_plan_id);

    /**
     * 根据派单规则ID查询店铺信息
     *
     * @param ruleId 派单规则ID
     * @return 店铺ID
     */
    @Select("select a.cp_c_shop_id from st_c_send_plan a inner join st_c_send_plan_item b on a.id = b.st_c_send_plan_id where st_c_send_rule_id = #{ruleId} and a.ISACTIVE = 'Y' and b.ISACTIVE = 'Y'")
    Long selectShopIdByRuleId(@Param("ruleId") Long ruleId);
}