package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCShopStrategyLogisticsItem;
import com.jackrain.nea.st.request.StCShopStrategyLogisticsSaveRequest;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StCShopStrategyLogisticsItemMapper extends ExtentionMapper<StCShopStrategyLogisticsItem> {

    @Delete("<script>" +
            "delete from st_c_shop_strategy_logistics_item " +
            "where st_c_shop_strategy_id in " +
            "<foreach collection='request.getObjids' item='item' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    Integer batchDelete(@Param("request")StCShopStrategyLogisticsSaveRequest request);
}
