package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPreorderFieldStrategyDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName StPreorderFieldStrategyMapper
 * @Description 订单预导入模板明细
 * <AUTHOR>
 * @Date 2022/12/22 11:54
 * @Version 1.0
 */
@Mapper
public interface StPreorderFieldStrategyMapper extends ExtentionMapper<StCPreorderFieldStrategyDO> {

    @Select("select * from st_c_preorder_field_strategy where preorder_model_strategy_id = #{modelId} and isactive = 'Y'")
    List<StCPreorderFieldStrategyDO> getFieldStrategyDOListByModelId(@Param("modelId") Long modelId);

    @Select("select * from st_c_preorder_field_strategy where customize_field = #{customizeField} and isactive = 'Y' and preorder_model_strategy_id = #{modelId}")
    StCPreorderFieldStrategyDO getFieldStrategyDOListByCustomizeField(@Param("customizeField") String customizeField, @Param("modelId") Long modelId);

    @Delete("delete from st_c_preorder_field_strategy where preorder_model_strategy_id = #{modelId}")
    void delByModelId(@Param("modelId") Long modelId);
}
