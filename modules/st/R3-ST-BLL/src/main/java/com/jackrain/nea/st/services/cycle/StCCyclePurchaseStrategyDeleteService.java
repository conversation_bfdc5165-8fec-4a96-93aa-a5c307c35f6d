package com.jackrain.nea.st.services.cycle;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.cycle.StCCyclePurchaseStrategyItemMapper;
import com.jackrain.nea.st.mapper.cycle.StCCyclePurchaseStrategyMapper;
import com.jackrain.nea.st.model.request.cycle.StCCyclePurchaseStrategyDeleteRequest;
import com.jackrain.nea.st.model.table.cycle.StCCyclePurchaseStrategy;
import com.jackrain.nea.st.model.table.cycle.StCCyclePurchaseStrategyItem;
import com.jackrain.nea.st.utils.StR3ParamUtils;
import com.jackrain.nea.st.utils.StRedisLockUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import utils.AssertUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Auther: chenhao
 * @Date: 2022-09-05 10:18
 * @Description:
 */

@Slf4j
@Component
public class StCCyclePurchaseStrategyDeleteService {

    @Autowired
    private StCCyclePurchaseStrategyMapper mainMapper;
    @Autowired
    private StCCyclePurchaseStrategyItemMapper itemMapper;

    /**
     * 周期购删除（页面入库入口）
     *
     * @param session session
     * @return return
     */
    public ValueHolder delete(QuerySession session) {
        StCCyclePurchaseStrategyDeleteRequest request = StR3ParamUtils.parseSaveObject(session, StCCyclePurchaseStrategyDeleteRequest.class);
        request.setR3(true);
        StCCyclePurchaseStrategyDeleteService service = ApplicationContextHandle.getBean(StCCyclePurchaseStrategyDeleteService.class);
        return StR3ParamUtils.convertV14WithResult(service.delete(request, session));
    }


    /**
     * 周期购删除
     *
     * @param request 删除入参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> delete(StCCyclePurchaseStrategyDeleteRequest request, QuerySession session) {

        log.info(LogUtil.format("Start StCCyclePurchaseStrategyDeleteService.delete:param={}",
                "StCCyclePurchaseStrategyDeleteService"), JSONObject.toJSONString(request));

        String lockKey = StConstant.ST_C_CYCLE_PURCHASE_STRATEGY + ":" + request.getObjId();
        StRedisLockUtils.lock(lockKey);
        try {
            checkParams(request);

            StCCyclePurchaseStrategy stCyclePurchaseStrategy = mainMapper.selectById(request.getObjId());
            String redisKey = "st:cyclePurchaseStrategy:proCode:" + stCyclePurchaseStrategy.getPsCProEcode();

            if (Boolean.TRUE.equals(RedisOpsUtil.getStrRedisTemplate().hasKey(redisKey))) {
                RedisOpsUtil.getStrRedisTemplate().delete(redisKey);
            }
            String redisKeyById = "st:cyclePurchaseStrategy:id:" + stCyclePurchaseStrategy.getId();

            if (Boolean.TRUE.equals(RedisOpsUtil.getStrRedisTemplate().hasKey(redisKeyById))) {
                RedisOpsUtil.getStrRedisTemplate().delete(redisKeyById);
            }

            //明细删除
            if (CollectionUtils.isNotEmpty(request.getItemIds())) {
                List<StCCyclePurchaseStrategyItem> items = itemMapper.selectList(new QueryWrapper<StCCyclePurchaseStrategyItem>()
                        .lambda().eq(StCCyclePurchaseStrategyItem::getStCCyclePurchaseStrategyId, request.getObjId()));
                if (CollectionUtils.isNotEmpty(items)) {
                    Map<Long, String> beforeDelObjMap = new HashMap<>();
                    for (StCCyclePurchaseStrategyItem item : items) {
                        beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                    }
                    session.setAttribute("beforeDelObjMap", beforeDelObjMap);
                }
                itemMapper.delete(new QueryWrapper<StCCyclePurchaseStrategyItem>()
                        .lambda().eq(StCCyclePurchaseStrategyItem::getStCCyclePurchaseStrategyId, request.getObjId()));
            } else {
                itemMapper.delete(new QueryWrapper<StCCyclePurchaseStrategyItem>()
                        .lambda().eq(StCCyclePurchaseStrategyItem::getStCCyclePurchaseStrategyId, request.getObjId()));
                mainMapper.delete(new QueryWrapper<StCCyclePurchaseStrategy>()
                        .lambda().eq(StCCyclePurchaseStrategy::getId, request.getObjId()));
            }

        } catch (Exception e) {
            log.error(LogUtil.format("exception_has_occured:{}", "exception_has_occured"), Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrowException("周期购删除删除异常", e, request.getLoginUser().getLocale());
        } finally {
            StRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "删除成功!");
    }


    /**
     * 校验策略状态
     *
     * @param request 策略状态
     */
    private void checkParams(StCCyclePurchaseStrategyDeleteRequest request) {
        StCCyclePurchaseStrategy stCyclePurchaseStrategy = mainMapper.selectById(request.getObjId());
        AssertUtils.cannot(Objects.isNull(stCyclePurchaseStrategy), "当前记录已不存在！");
        Integer status = stCyclePurchaseStrategy.getStatus();
        AssertUtils.notNull(status, "策略状态值异常：null");
        if (StConstant.CYCLE_PURCHASE_STRATEGY_STATUS_SUBMIT.equals(status)) {
            AssertUtils.logAndThrow("当前策略已审核，不允许编辑！");
        } else if (StConstant.CYCLE_PURCHASE_STRATEGY_STATUS_VOID.equals(status)) {
            AssertUtils.logAndThrow("当前策略已作废，不允许编辑！");
        } else if (StConstant.CYCLE_PURCHASE_STRATEGY_STATUS_CLOSER.equals(status)) {
            AssertUtils.logAndThrow("当前策略已结案，不允许编辑！");
        }
    }
}
