package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCEwaybillMapper;
import com.jackrain.nea.st.model.table.StCEwaybillDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Date;
import java.util.HashMap;

/**
 * @Descroption 电子面单策略作废
 * <AUTHOR>
 * @Date 2019/3/21 15:55
 */
@Component
@Slf4j
public class EwaybillVoidService extends CommandAdapter {
    @Autowired
    private StCEwaybillMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start EwaybillVoidService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                //4.遍历作废方法
                voidEwaybill(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * @param id
     * @param querySession
     * @return void
     * @Descroption 检查并更新作废状态
     * @Author: 洪艺安
     * @Date 2019/3/12
     */
    public void voidEwaybill(Long id, QuerySession querySession) {
        StCEwaybillDO ewaybill = mapper.selectById(id);
        checkDistribution(ewaybill);
        //更新作废状态
        StBeanUtils.makeModifierField(ewaybill, querySession.getUser());
        ewaybill.setIsactive(StConstant.ISACTIVE_N);//作废
        setVoidCommonField(ewaybill, querySession.getUser());
        int updateNum = mapper.updateById(ewaybill);
        if (updateNum < 0) {
            throw new NDSException("作废失败！");
        }
    }

    private void checkDistribution(StCEwaybillDO ewaybill) {
        if (ewaybill == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.ISACTIVE_N.equals(ewaybill.getIsactive())) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }

    /**
     * @param ewaybill
     * @param user
     * @return void
     * @Descroption 设置作废公共字段
     * @Author: 洪艺安
     * @Date 2019/3/12
     */
    private void setVoidCommonField(StCEwaybillDO ewaybill, User user) {
        ewaybill.setDelid(Long.valueOf(user.getId()));
        ewaybill.setDelename(user.getEname());
        ewaybill.setDelname(user.getName());
        ewaybill.setDelTime(new Date());
    }
}
