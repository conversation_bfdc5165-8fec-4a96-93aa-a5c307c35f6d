package com.jackrain.nea.st.services.message;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.message.StCMessageStrategyItemMapper;
import com.jackrain.nea.st.mapper.message.StCMessageStrategyMapper;
import com.jackrain.nea.st.model.request.message.StCMessageStrategyBillSaveRequest;
import com.jackrain.nea.st.model.request.message.StCMessageStrategySaveItemRequest;
import com.jackrain.nea.st.model.request.message.StCMessageStrategySaveRequest;
import com.jackrain.nea.st.model.table.message.StCMessageStrategy;
import com.jackrain.nea.st.model.table.message.StCMessageStrategyItem;
import com.jackrain.nea.st.utils.StR3ParamUtils;
import com.jackrain.nea.st.utils.StRedisLockUtils;
import com.jackrain.nea.st.utils.UserUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import utils.AssertUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Auther: chenhao
 * @Date: 2022-09-09 17:47
 * @Description: 短信策略新增/保存
 */

@Slf4j
@Component
public class StCMessageStrategySaveService {

    @Autowired
    private StCMessageStrategyMapper mainMapper;
    @Autowired
    private StCMessageStrategyItemMapper itemMapper;

    /**
     * R3页面新增/保存
     * ps:将页面传入参数转换成StCMessageStrategyBillSaveRequest
     *
     * @param session 入参
     * @return ValueHolder
     */
    public ValueHolder execute(QuerySession session) {
        StCMessageStrategyBillSaveRequest billSaveRequest = StR3ParamUtils.parseSaveObject(session, StCMessageStrategyBillSaveRequest.class);
        log.info(LogUtil.format("StCMessageStrategySaveService.execute:{}",
                "StCMessageStrategySaveService.execute"),
                Objects.nonNull(billSaveRequest) ? JSONObject.toJSONString(billSaveRequest) : null);
        billSaveRequest.setR3(true);
        StCMessageStrategySaveService service = ApplicationContextHandle.getBean(StCMessageStrategySaveService.class);
        return StR3ParamUtils.convertV14WithResult(service.save(billSaveRequest));
    }

    /**
     * 新增/保存
     *
     * @param request 入参
     * @return ValueHolderV14
     */
    public ValueHolderV14<SgR3BaseResult> save(StCMessageStrategyBillSaveRequest request) {

        log.info(LogUtil.format("StCMessageStrategySaveService.execute save:{}",
                "StCMessageStrategySaveService.execute"),
                Objects.nonNull(request) ? JSONObject.toJSONString(request) : null);

        checkPram(request);

        ValueHolderV14<SgR3BaseResult> v14;

        Long objId = request.getObjId();
        if (objId != null && objId > 0) {
            v14 = update(request);
        } else {
            v14 = insert(request);
        }

        log.info(LogUtil.format("StCMessageStrategySaveService.execute save:{}",
                "StCMessageStrategySaveService.ValueHolderV14"),
                JSONObject.toJSONString(v14));

        return v14;
    }

    /**
     * 保存
     *
     * @param request 入参
     * @return ValueHolderV14
     */
    private ValueHolderV14<SgR3BaseResult> update(StCMessageStrategyBillSaveRequest request) {

        StCMessageStrategySaveRequest mainRequest = request.getMainRequest();
        List<StCMessageStrategySaveItemRequest> itemRequestList = request.getItemRequest();
        Long objId = request.getObjId();

        String lockKey = StConstant.ST_C_MESSAGE_STRATEGY + ":" + objId;
        StRedisLockUtils.lock(lockKey);
        try {
            //主表更新
            StCMessageStrategy update = new StCMessageStrategy();
            if (Objects.nonNull(mainRequest)) {
                BeanUtils.copyProperties(mainRequest, update);
                update.setStrategyName(mainRequest.getStrategyName());
                update.setRemark(mainRequest.getRemark());
            }
            update.setId(objId);
            UserUtils.setModelDefalutDataByUpdate(update, request.getLoginUser());
            mainMapper.updateById(update);

            //明细更新
            if (CollectionUtils.isNotEmpty(itemRequestList)) {
                for (StCMessageStrategySaveItemRequest itemRequest : itemRequestList) {
                    Long itemId = itemRequest.getId();
                    if (itemId != null && itemId > 0) {
                        StCMessageStrategyItem updateItem = new StCMessageStrategyItem();
                        BeanUtils.copyProperties(itemRequest, updateItem);
                        updateItem.setId(itemId);
                        UserUtils.setModelDefalutDataByUpdate(updateItem, request.getLoginUser());
                        itemMapper.updateById(updateItem);
                    } else {
                        StCMessageStrategyItem insetItem = new StCMessageStrategyItem();
                        BeanUtils.copyProperties(itemRequest, insetItem);
                        insetItem.setId(ModelUtil.getSequence(StConstant.ST_C_MESSAGE_STRATEGY_ITEM));
                        UserUtils.setModelDefalutData(insetItem, request.getLoginUser());
                        insetItem.setStCMessageStrategyId(objId);
                        itemMapper.insert(insetItem);
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("exception_has_occured:{}", "exception_has_occured"), Throwables.getStackTraceAsString(e));
            com.jackrain.nea.utils.AssertUtils.logAndThrow("短信策略保存异常:" + e.getMessage());
        } finally {
            StRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }

        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(objId, StConstant.ST_C_MESSAGE_STRATEGY);
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, "短信策略保存成功！");
    }

    /**
     * 新增
     *
     * @param request 入参
     * @return ValueHolderV14
     */
    private ValueHolderV14<SgR3BaseResult> insert(StCMessageStrategyBillSaveRequest request) {

        StCMessageStrategySaveRequest mainRequest = request.getMainRequest();
        List<StCMessageStrategySaveItemRequest> itemRequestList = request.getItemRequest();

        StCMessageStrategy insert = new StCMessageStrategy();
        Long objId = ModelUtil.getSequence(StConstant.ST_C_MESSAGE_STRATEGY);
        insert.setId(objId);
        UserUtils.setModelDefalutData(insert, request.getLoginUser());
        insert.setStrategyName(mainRequest.getStrategyName());
        insert.setRemark(mainRequest.getRemark());
        mainMapper.insert(insert);

        if (CollectionUtils.isNotEmpty(itemRequestList)) {

            List<StCMessageStrategyItem> insertItemList = new ArrayList<>();
            for (StCMessageStrategySaveItemRequest itemRequest : itemRequestList) {
                StCMessageStrategyItem insetItem = new StCMessageStrategyItem();
                BeanUtils.copyProperties(itemRequest, insetItem);
                insetItem.setId(ModelUtil.getSequence(StConstant.ST_C_MESSAGE_STRATEGY_ITEM));
                UserUtils.setModelDefalutData(insetItem, request.getLoginUser());
                insetItem.setStCMessageStrategyId(objId);
                insertItemList.add(insetItem);
            }

            if (CollectionUtils.isNotEmpty(itemRequestList)) {
                itemMapper.batchInsert(insertItemList);
            }

        }

        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(objId, StConstant.ST_C_MESSAGE_STRATEGY);
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);

    }

    /**
     * 参数校验
     *
     * @param request 入参
     */
    private void checkPram(StCMessageStrategyBillSaveRequest request) {

        AssertUtils.notNull(request, "短信策略新增/保存失败：入参为空！");

        //保存校验
        Long objId = request.getObjId();
        boolean isSave = objId != null && objId > 0;
        if (isSave) {
            StCMessageStrategy stMessageStrategy = mainMapper.selectById(objId);
            AssertUtils.notNull(stMessageStrategy, "当前记录已不存在！");
            AssertUtils.cannot(SgConstants.IS_ACTIVE_N.equals(stMessageStrategy.getIsactive()), "当前记录已作废，不允许编辑！");
        }

        //主表校验
        StCMessageStrategySaveRequest mainRequest = request.getMainRequest();
        if (Objects.nonNull(mainRequest)) {
            String strategyName = mainRequest.getStrategyName();
            if (StringUtils.isNotEmpty(strategyName)) {
                Integer selectCount = mainMapper.selectCount(new LambdaQueryWrapper<StCMessageStrategy>()
                        .eq(StCMessageStrategy::getStrategyName, strategyName)
                        .eq(StCMessageStrategy::getId, Objects.nonNull(objId) ? objId : -1)
                        .eq(StCMessageStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));
                AssertUtils.cannot(selectCount > 0, "当前策略名称【" + strategyName + "】已存在，请重新设置！");
            }
        }

        //明细校验
        List<StCMessageStrategySaveItemRequest> itemRequestList = request.getItemRequest();
        if (CollectionUtils.isNotEmpty(itemRequestList)) {
            for (StCMessageStrategySaveItemRequest itemRequest : itemRequestList) {
                Long itemId = itemRequest.getId();
                if (itemId != null && itemId > 0) {
                    StCMessageStrategyItem stMessageStrategyItem = itemMapper.selectById(itemId);
                    AssertUtils.notNull(stMessageStrategyItem, "当前明细记录已不存在！");
                }

                Long cpShopId = itemRequest.getCpCShopId();
                if (Objects.nonNull(cpShopId) && isSave) {
                    Integer selectCount = itemMapper.selectCount(new LambdaQueryWrapper<StCMessageStrategyItem>()
                            .eq(StCMessageStrategyItem::getCpCShopId, cpShopId)
                            .eq(StCMessageStrategyItem::getStCMessageStrategyId, objId)
                            .eq(StCMessageStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));
                    AssertUtils.cannot(selectCount > 0, "明细中已存在当前店铺，不允许重复设置");
                }
            }
        }
    }

}
