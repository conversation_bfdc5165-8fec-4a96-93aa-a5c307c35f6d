package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface StCWarehouseLogisticsMapper extends ExtentionMapper<StCWarehouseLogisticsDO> {
    @Select("select count(1) from st_c_warehouse_logistics where id<>#{mid} and isactive='Y' and cp_c_phy_warehouse_id= #{wareid}")
    int listByWareid(@Param("mid") Long mid, @Param("wareid") Long wareid);
}