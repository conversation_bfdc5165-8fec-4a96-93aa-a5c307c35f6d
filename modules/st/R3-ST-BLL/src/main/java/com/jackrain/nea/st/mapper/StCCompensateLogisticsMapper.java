package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCCompensateLogisticsDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Mapper
public interface StCCompensateLogisticsMapper extends ExtentionMapper<StCCompensateLogisticsDO> {
    @Select("SELECT * FROM ST_C_COMPENSATE_LOGISTICS WHERE ST_C_LOGISTICS_COMPENSATE_ID = #{slaverId}")
    List<StCCompensateLogisticsDO> selectLogisticsBySlaverId(Long slaverId);

    @Delete("DELETE FROM ST_C_COMPENSATE_LOGISTICS WHERE ST_C_LOGISTICS_COMPENSATE_ID = #{masterId}")
    int deleteByLogisticsId(Long masterId);

    @Select("SELECT * FROM ST_C_COMPENSATE_LOGISTICS WHERE cp_c_logistics_id = #{cpCLogisticsId} and ST_C_LOGISTICS_COMPENSATE_ID = #{id}")
    List<StCCompensateLogisticsDO> selectStCCompensateLogisticsDOCheckBylogisid(@Param("cpCLogisticsId") Long cpCLogisticsId , @Param("id") Long id);

    @Select("SELECT scl.* " +
            "FROM st_c_compensate_logistics scl " +
            "INNER JOIN st_c_compensate scc on (scc.id = scl.st_c_logistics_compensate_id) " +
            "INNER JOIN st_c_compensate_warehouse scw on (scc.id = scw.st_c_logistics_compensate_id) " +
            "WHERE scc.bill_status = 2 and scl.cp_c_logistics_id = #{logisticsId} " +
            "AND scw.cp_c_phy_warehouse_id = #{phyWarehouseId} " +
            "AND scc.begin_time < #{currentDate} AND scc.end_time > #{currentDate} " +
            "ORDER BY scc.creationdate desc ")
    List<StCCompensateLogisticsDO> selectByAcPayable(@Param("logisticsId") Long logisticsId,
                                                     @Param("phyWarehouseId") Long phyWarehouseId,
                                                     @Param("currentDate") Date currentDate);
}