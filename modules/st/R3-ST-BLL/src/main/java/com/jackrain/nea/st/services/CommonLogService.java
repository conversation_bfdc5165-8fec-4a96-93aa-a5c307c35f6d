package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.st.mapper.CommonLogMapper;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;

/**
 * @ClassName : CommonLogService  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-09-13 18:25  
 */
@Slf4j
@Component
public class CommonLogService {

    /**
     *
     * @param user
     * @param table  日志表
     * @param mainTable 关联表
     * @param id 关联表ID
     * @param modcontent 修改内容
     * @param bData 修改前内容
     * @param aData 修改后内容
     */
    public void addchangeLog(User user, String table, String mainTable, Long id, String modcontent, String bData, String aData) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tableName",table.toUpperCase());
        jsonObject.put("ID", Tools.getSequence(table.toUpperCase()));
        jsonObject.put("AD_CLIENT_ID", user.getClientId());
        jsonObject.put("AD_ORG_ID", user.getOrgId());
        jsonObject.put(mainTable.toUpperCase() + "_ID", id);
        jsonObject.put("MODCONTENT", modcontent);
        jsonObject.put("BMOD", bData);
        jsonObject.put("AMOD", aData);
        jsonObject.put("ISACTIVE", "Y");
        jsonObject.put("OWNERID", user.getId().longValue());
        jsonObject.put("OWNERNAME", user.getName());
        jsonObject.put("OWNERENAME", user.getEname());
        jsonObject.put("CREATIONDATE", timestamp);
        jsonObject.put("MODIFIERID", user.getId().longValue());
        jsonObject.put("MODIFIERNAME", user.getName());
        jsonObject.put("MODIFIERENAME", user.getEname());
        jsonObject.put("MODIFIEDDATE", timestamp);
        CommonLogMapper mapper = ApplicationContextHandle
                .getBean(CommonLogMapper.class);
        mapper.insert(jsonObject);

    }
}
