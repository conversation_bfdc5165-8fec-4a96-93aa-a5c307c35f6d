package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StOperateLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 12:58 2020/4/30
 * description ：
 * @ Modified By：
 */

@Mapper
@Component
public interface StOperateLogMapper extends ExtentionMapper<StOperateLogDO> {

    /**
     * 获取指定的列的值
     *
     * @param tableName      表名
     * @param columns        列名
     * @param operateTableId 表ID
     * @return 返回值
     */
    @Select("select ${columns} from ${tableName} where id = #{operateTableId}")
    JSONObject selectDataById(@Param("tableName") String tableName, @Param("columns") String columns, @Param("operateTableId") long operateTableId);

    @Select("select column_description,original_value,new_value,ownername,creationdate from st_c_operate_log where modify_id=#{operateTableId} and table_name=#{tableName}")
    List<HashMap<String, Object>> selectlog(@Param("tableName") String tableName, @Param("operateTableId") long operateTableId);
}