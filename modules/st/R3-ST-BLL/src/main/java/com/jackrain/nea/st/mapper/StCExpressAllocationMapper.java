package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCExpressAllocationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

@Mapper
@Component
public interface StCExpressAllocationMapper extends ExtentionMapper<StCExpressAllocationDO> {

    @Select("select count(1) from st_c_express_allocation where id<>#{mid} and isactive='Y' and cp_c_phy_warehouse_id= #{wareid}")
    int listByWareid(@Param("mid") Long mid,@Param("wareid") Long wareid);

    /**
     * 判断“物流公司”是否在订单中“发货仓库”对应的【仓库物流规则】中存在且启用
     *
     * @param cpClogisticsId    公司Id
     * @param cpCphyWarehouseId 仓库Id
     * @return
     */
    @Select("SELECT COUNT(*) FROM ST_C_EXPRESS_ALLOCATION a LEFT JOIN st_c_express_allocation_item b ON a.id= b.st_c_express_allocation_id "
            + "WHERE a.cp_c_phy_warehouse_id=#{cpCphyWarehouseId} AND b.cp_c_logistics_id=#{cpClogisticsId} AND a.isactive='Y' AND b.isactive='Y'")
    int queryLogisticsRule(@Param("cpClogisticsId") Long cpClogisticsId, @Param("cpCphyWarehouseId") Long cpCphyWarehouseId);

    /**
     * 仓库限制数量
     *
     * @param cpCphyWarehouseId 仓库ID
     * @param cpCogisticsId     物流公司ID
     * @return countProLimitNumber
     */
    @Select("SELECT IFNULL(SUM(b.limit_num),0) AS limitNum  FROM st_c_express_allocation a LEFT JOIN "
            + "st_c_express_allocation_item b ON a.id = b.st_c_express_allocation_id WHERE  a.ISACTIVE='Y' and b.ISACTIVE='Y'"
            + "and a.cp_c_phy_warehouse_id = #{cpCphyWarehouseId} AND b.cp_c_logistics_id=#{cpCogisticsId}")
    Long countProLimitNumber(@Param("cpCphyWarehouseId") Long cpCphyWarehouseId,
                             @Param("cpCogisticsId") Long cpCogisticsId);

}