package com.jackrain.nea.st.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * OSS配置
 *
 * @author: huang.z<PERSON><PERSON>
 * @since: 2019/8/9
 * create at : 2019/8/9 9:59
 */
@Configuration
@Data
public class R3OssConfig {

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;
}