package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCRouterCodeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCRouterCodeMapper extends ExtentionMapper<StCRouterCodeDO> {

    @Select("SELECT * FROM st_c_router_code WHERE sys_type = #{sysType} AND isactive='Y'")
    List<StCRouterCodeDO> selectBySysType(@Param("sysType") String sysType);

    @Select("SELECT COUNT(*) FROM st_c_router_code WHERE id <> #{id} AND isactive='Y' AND ENAME = #{ename} ")
    int selectCountByEnameAndId(@Param("id") Long id, @Param("ename") String ename);

    @Select("SELECT COUNT(*) FROM st_c_router_code WHERE id <> #{id} AND isactive='Y' AND code = #{code} AND sys_type = #{sysType} ")
    int selectCountByCodeAndSysType(@Param("id") Long id, @Param("code") String code,@Param("sysType") String sysType);
}