package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsRankDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCWarehouseLogisticsRankMapper extends ExtentionMapper<StCWarehouseLogisticsRankDO> {

    @Select("select rank.* from st_c_warehouse_logistics_rank rank " +
            "inner join st_c_warehouse_logistics logistics ON rank.st_c_warehouse_logistics_id = logistics.id " +
            "where logistics.cp_c_phy_warehouse_id = #{cpCPhyWarehouseId} AND logistics.isactive = 'Y' " +
            "AND rank.cp_c_region_province_id = #{cpCRegionProvinceId} AND rank.cp_c_region_city_id = #{cpCRegionCityId}")
    List<StCWarehouseLogisticsRankDO> queryLogisticsRankInfo(@Param("cpCPhyWarehouseId") Long cpCPhyWarehouseId
                                                            , @Param("cpCRegionProvinceId") Long cpCRegionProvinceId
                                                            , @Param("cpCRegionCityId") Long cpCRegionCityId);
}