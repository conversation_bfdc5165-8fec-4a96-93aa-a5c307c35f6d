package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.AdLimitvalueDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AdLimitvalueMapper extends ExtentionMapper<AdLimitvalueDO> {
        @Select("SELECT\n" +
                "\t*\n" +
                "FROM\n" +
                "\tAD_LIMITVALUE aL,\n" +
                "\tAD_LIMITVALUE_GROUP alg\n" +
                "WHERE\n" +
                "\tal.ad_limitvalue_group_id = alg.id\n" +
                "AND alg.`name` =  #{name}\n")
        List<AdLimitvalueDO> selectTypeByName (@Param("name") String name);
        }