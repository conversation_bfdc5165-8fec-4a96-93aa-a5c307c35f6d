package com.jackrain.nea.st.services;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyMapper;
import com.jackrain.nea.st.model.enums.StCExpressPriceStrategyEnum;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyItemDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/9 17:42
 * @Description 快递报价设置 策略 保存的新增修改业务逻辑
 * @Version 1.0
 */
@Component
@Slf4j
public class StCExpressPriceStrategySaveService extends CommandAdapter {

    @Resource
    private StCExpressPriceStrategyMapper stCExpressPriceStrategyMapper;

    @Resource
    private StCExpressPriceStrategyItemMapper stCExpressPriceStrategyItemMapper;


    /**
     * 新增和修改
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @StOperationLog(mainTableName = "ST_C_EXPRESS_PRICE_STRATEGY", itemsTableName = "ST_C_EXPRESS_PRICE_STRATEGY_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        if (log.isDebugEnabled()) {
            log.debug("Start StExpressPriceService execute event:{}", JSONObject.toJSONString(event));
        }
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject mainMap = fixColumn.getJSONObject(StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY);
                JSONArray itemMap = fixColumn.getJSONArray(StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY_ITEM);
                if (id != null && id < 0) {
                    //新增
                    valueHolder = saveFunction(mainMap, itemMap, valueHolder, querySession, id);
                } else {
                    //编辑
                    valueHolder = updateFunction(mainMap, itemMap, valueHolder, querySession, id);
                }
            }
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }


    /**
     * 新增操作
     * @param mainMap
     * @param itemMap
     * @param holder
     * @param querySession
     * @param id
     * @return
     */
    private ValueHolder saveFunction(JSONObject mainMap, JSONArray itemMap, ValueHolder holder, QuerySession querySession, Long id) {
        StCExpressPriceStrategyDO stCExpressPriceStrategyDO = JSON.parseObject(mainMap.toJSONString(),
                new TypeReference<StCExpressPriceStrategyDO>() {
                });
        if (stCExpressPriceStrategyDO != null) {
            stCExpressPriceStrategyDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY));
            StBeanUtils.makeCreateField(stCExpressPriceStrategyDO, querySession.getUser());
            reSetDateTimeRange(stCExpressPriceStrategyDO);
            if (stCExpressPriceStrategyDO.getEndDate().getTime() < stCExpressPriceStrategyDO.getStartDate().getTime()) {
                holder = ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
                return holder;
            }

            if (stCExpressPriceStrategyMapper.insert(stCExpressPriceStrategyDO) <= 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        } else {
            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(stCExpressPriceStrategyDO.getId(), StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY);
        return holder;
    }


    /**
     * 更新操作
     * @param mainMap
     * @param itemMap
     * @param holder
     * @param querySession
     * @param objid
     * @return
     */
    private ValueHolder updateFunction(JSONObject mainMap, JSONArray itemMap, ValueHolder holder, QuerySession querySession, Long objid) {
        StCExpressPriceStrategyDO oldMainData = stCExpressPriceStrategyMapper.selectById(objid);
        if (oldMainData == null) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已存在，不允许编辑！");
            return holder;
        }
        if (StCExpressPriceStrategyEnum.CLOSED.getKey().equals(oldMainData.getCloseStatus())) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已结案，不允许编辑！");
            return holder;
        }
        if (StCExpressPriceStrategyEnum.SUBMITTED.getKey().equals(oldMainData.getStatus())) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已提交，不允许编辑！");
            return holder;
        }

        //主表更新，objid就是主表ID
        if (mainMap != null && !mainMap.isEmpty()) {
            StCExpressPriceStrategyDO stCExpressPriceStrategyDO = JSON.parseObject(mainMap.toJSONString(),
                    new TypeReference<StCExpressPriceStrategyDO>() {
                    });
            //校验日期是否违法
            if (stCExpressPriceStrategyDO.getStartDate() == null) {
                stCExpressPriceStrategyDO.setStartDate(oldMainData.getStartDate());
            }
            if (stCExpressPriceStrategyDO.getEndDate() == null) {
                stCExpressPriceStrategyDO.setEndDate(oldMainData.getEndDate());
            }
            reSetDateTimeRange(stCExpressPriceStrategyDO);
            if (stCExpressPriceStrategyDO.getEndDate().getTime() < stCExpressPriceStrategyDO.getStartDate().getTime()) {
                holder = ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
                return holder;
            }

            stCExpressPriceStrategyDO.setId(objid);
            StBeanUtils.makeModifierField(stCExpressPriceStrategyDO, querySession.getUser());

            if (stCExpressPriceStrategyMapper.updateById(stCExpressPriceStrategyDO) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        }

        //判断子表数据是否存在
        if (itemMap != null && !itemMap.isEmpty()) {
            ArrayList<StCExpressPriceStrategyItemDO> stCExpressPriceStrategyItemDOList = JSON.parseObject(itemMap.toJSONString(),
                    new TypeReference<ArrayList<StCExpressPriceStrategyItemDO>>() {
                    });
            if (CollectionUtils.isNotEmpty(stCExpressPriceStrategyItemDOList)) {

                //检查行明细
                this.checkItem(stCExpressPriceStrategyItemDOList, holder, objid);

                for (StCExpressPriceStrategyItemDO stCExpressPriceStrategyItemDO : stCExpressPriceStrategyItemDOList) {
                    Long id = stCExpressPriceStrategyItemDO.getId();
                    if (id < 0) {
                        stCExpressPriceStrategyItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY_ITEM));
                        stCExpressPriceStrategyItemDO.setStCExpressPriceStrategyId(objid);
                        StBeanUtils.makeCreateField(stCExpressPriceStrategyItemDO, querySession.getUser());

                        if (stCExpressPriceStrategyItemMapper.insert(stCExpressPriceStrategyItemDO) < 0) {
                            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                            return holder;
                        }
                    } else {
                        //修改原有的行信息 id>0
                        if (stCExpressPriceStrategyItemMapper.selectById(id) != null) {
                            StBeanUtils.makeModifierField(stCExpressPriceStrategyItemDO, querySession.getUser());
                            if (stCExpressPriceStrategyItemMapper.updateById(stCExpressPriceStrategyItemDO) < 0) {
                                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                                return holder;
                            }
                        } else {
                            holder = ValueHolderUtils.getFailValueHolder("修改的行明细已被删除！");
                            return holder;
                        }
                    }
                }
                StCExpressPriceStrategyDO stCExpressPriceStrategyDO = new StCExpressPriceStrategyDO();
                stCExpressPriceStrategyDO.setId(objid);
                StBeanUtils.makeModifierField(stCExpressPriceStrategyDO, querySession.getUser());
                stCExpressPriceStrategyMapper.updateById(stCExpressPriceStrategyDO);
            } else {
                holder = ValueHolderUtils.getFailValueHolder("明细JSON转换失败，保存失败！");
                return holder;
            }
        }

        holder = ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY);
        return holder;
    }



    /**
     * 明细前置条件保存校验
     * @param itemDOList
     * @param holder
     * @return
     */
    private void checkItem(List<StCExpressPriceStrategyItemDO> itemDOList, ValueHolder holder, Long objid) {

        //校验有且只有一种计费方式，并补充数据，用于后面校验
        for (StCExpressPriceStrategyItemDO itemDO : itemDOList) {
            //为起始重量和结束重量赋值，保证后续比较不存在空指针
            if (itemDO.getId() != null && itemDO.getId() < 0) {
                //新增的明细
                if (itemDO.getStartWeight() == null) {
                    itemDO.setStartWeight(new BigDecimal(0));
                }
                if (itemDO.getEndWeight() == null) {
                    itemDO.setEndWeight(new BigDecimal(0));
                }
                //校验是否有且只维护了一种计费方式
                checkBillingMethodByInsert(itemDO);
            }else {
                //更新的明细
                if (itemDO.getProvinceId() == null || itemDO.getStartWeight() == null || itemDO.getEndWeight() == null) {
                    StCExpressPriceStrategyItemDO oldItem = stCExpressPriceStrategyItemMapper.selectById(itemDO.getId());
                    if (oldItem == null) {
                        throw new NDSException("修改的明细数据不存在或已删除！");
                    }
                    if (itemDO.getProvinceId() == null) {
                        itemDO.setProvinceId(oldItem.getProvinceId());
                    }
                    if (itemDO.getCityId() == null) {
                        itemDO.setCityId(oldItem.getCityId());
                    }
                    if (itemDO.getStartWeight() == null) {
                        itemDO.setStartWeight(oldItem.getStartWeight());
                    }
                    if (itemDO.getEndWeight() == null) {
                        itemDO.setEndWeight(oldItem.getEndWeight());
                    }
                    //对于计费方式有变化的，需要将另一个计费方式变为空
                    //首先保证前端没变，对象中保留该有的值，因为字段加了@TableField(strategy = FieldStrategy.IGNORED)，在field=null时，也会将控制更新进去

                    //对于前端没有改变快递费用、首重、续重的情况,保留原有的值
                    if (itemDO.getPriceExpress() == null && itemDO.getPriceFirstWeight() == null && itemDO.getPriceContinuedWeight() == null) {
                        if (oldItem.getPriceExpress() != null) {
                            itemDO.setPriceExpress(oldItem.getPriceExpress());
                        }
                        if (oldItem.getPriceFirstWeight() != null) {
                            itemDO.setPriceFirstWeight(oldItem.getPriceFirstWeight());
                        }
                        if (oldItem.getPriceContinuedWeight() != null) {
                            itemDO.setPriceContinuedWeight(oldItem.getPriceContinuedWeight());
                        }
                    }else {
                        //先校验前端传的没问题
                        checkBillingMethodByInsert(itemDO);
                        if (itemDO.getPriceExpress() != null) {
                            itemDO.setPriceFirstWeight(null);
                            itemDO.setPriceContinuedWeight(null);
                        }else {
                            itemDO.setPriceExpress(null);
                        }
                    }
                }
            }

        }

        //将子表按照省份分组，先验证集合内的交叉情况
        Map<Long, List<StCExpressPriceStrategyItemDO>> listMapByProvince = itemDOList.stream().collect(Collectors.groupingBy(StCExpressPriceStrategyItemDO::getProvinceId));
        for (Long provinceId : listMapByProvince.keySet()) {
            List<StCExpressPriceStrategyItemDO> strategyItemDOList = listMapByProvince.get(provinceId);
            if (strategyItemDOList != null && strategyItemDOList.size() > 1) {
                for (int i=0;i<strategyItemDOList.size();i++) {
                    for (int j=i+1;j<strategyItemDOList.size();j++) {
                        StCExpressPriceStrategyItemDO one = strategyItemDOList.get(i);
                        StCExpressPriceStrategyItemDO two = strategyItemDOList.get(j);
                        Boolean flag = checkWeight(one.getStartWeight(), one.getEndWeight(), two.getStartWeight(), two.getEndWeight());
                        if (!flag) {
                            throw new NDSException("录入的明细已存在，不允许重复录入！");
                        }
                    }
                }
            }
        }

        //与库内数据校验
        for (StCExpressPriceStrategyItemDO itemDO : itemDOList) {
            List<StCExpressPriceStrategyItemDO> selectList = stCExpressPriceStrategyItemMapper.selectList(new LambdaQueryWrapper<StCExpressPriceStrategyItemDO>()
                    .eq(StCExpressPriceStrategyItemDO::getStCExpressPriceStrategyId, objid)
                    .eq(StCExpressPriceStrategyItemDO::getProvinceId, itemDO.getProvinceId())
                    .ne(StCExpressPriceStrategyItemDO::getId, itemDO.getId()));
            if (CollectionUtils.isNotEmpty(selectList)) {
                for (StCExpressPriceStrategyItemDO strategyItemDO : selectList) {
                    Boolean flag = checkWeight(itemDO.getStartWeight(), itemDO.getEndWeight(), strategyItemDO.getStartWeight(), strategyItemDO.getEndWeight());
                    if (!flag) {
                        throw new NDSException("录入的明细已存在，不允许重复录入！");
                    }
                }
            }
        }
    }

    /**
     * 确保有且只有一种计费方式
     * @param itemDO
     * @return
     */
    private void checkBillingMethodByInsert(StCExpressPriceStrategyItemDO itemDO) {
        //新增的验证有且只有一种计费方式
        //如果快递费用为空，则首重和续重都不能为空，如果快递费用不为空，则首重和续重都得为空
        if (itemDO.getPriceExpress() == null) {
            if (!(itemDO.getPriceFirstWeight() != null && itemDO.getPriceContinuedWeight() != null)) {
                throw new NDSException("请至少维护一种计费方式！");
            }
        }
//        else {
//            if (!(itemDO.getPriceFirstWeight() == null && itemDO.getPriceContinuedWeight() == null)) {
//                throw new NDSException("【快递费用】和【首重+续重】计费方式只能同时维护一种方式");
//            }
//        }
    }


    /**
     * 验证重量是否存在交叉
     * @param startWeight
     * @param endWeight
     * @param startWeight1
     * @param endWeight1
     * @return
     */
    private Boolean checkWeight(BigDecimal startWeight, BigDecimal endWeight, BigDecimal startWeight1, BigDecimal endWeight1) {
        if (endWeight.compareTo(endWeight1) == 0) {
            return false;
        }else {
            if (endWeight.compareTo(endWeight1) == 1) {
                //endWeight大，当startWeight小于endWeight1时，返回false
                if (startWeight.compareTo(endWeight1) == -1) {
                    return false;
                }
            }else {
                //endWeight1大，当startWeight1小于endWeight时，返回false
                if (startWeight1.compareTo(endWeight) == -1) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 开始日期取当天0点，结束日期取当天23:59:59
     *
     * @param mainObj 主对象
     */
    private void reSetDateTimeRange(StCExpressPriceStrategyDO mainObj) {
        if (Objects.isNull(mainObj)) {
            return;
        }
        if (Objects.nonNull(mainObj.getStartDate())) {
            mainObj.setStartDate(DateUtil.beginOfDay(mainObj.getStartDate()));
        }
        if (Objects.nonNull(mainObj.getEndDate())) {
            /*mainObj.setEndDate(DateUtil.endOfDay(mainObj.getEndDate()));*/
            DateTime dateTime = DateUtil.endOfDay(mainObj.getEndDate());
            dateTime.setTime(dateTime.getTime() - 999);
            mainObj.setEndDate(dateTime);
        }
    }
}