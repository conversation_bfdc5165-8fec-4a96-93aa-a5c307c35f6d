package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPreorderModelStrategyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName StPreorderModelStrategyMapper
 * @Description 订单预导入模板
 * <AUTHOR>
 * @Date 2022/12/22 11:53
 * @Version 1.0
 */
@Mapper
public interface StPreorderModelStrategyMapper extends ExtentionMapper<StCPreorderModelStrategyDO> {

    @Select("SELECT * FROM st_c_preorder_model_strategy  WHERE code = #{code} and isactive = 'Y'")
    StCPreorderModelStrategyDO getByCode(String modelCode);

    @Select("SELECT * FROM st_c_preorder_model_strategy  WHERE name = #{name} and isactive = 'Y'")
    StCPreorderModelStrategyDO getByName(String name);

    @Select("SELECT * FROM st_c_preorder_model_strategy  WHERE isactive = 'Y' and submit_status = 2")
    List<StCPreorderModelStrategyDO> getAllModel();
}
