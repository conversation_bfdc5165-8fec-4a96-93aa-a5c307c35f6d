package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCExpressLogisticsItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCExpressLogisticsItemMapper extends ExtentionMapper<StCExpressLogisticsItemDO> {

    @Select("select * from st_c_express_logistics_item stl where stl.st_c_express_id = #{mainId}")
    List<StCExpressLogisticsItemDO> selectStCExpressLogisticsItemDOBymainId (@Param("mainId") Long mainId);
}