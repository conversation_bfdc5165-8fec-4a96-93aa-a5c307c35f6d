package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCBansAreaStrategy;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.Set;

/**
 * 物流禁发区域
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-10 15:05:00
 */
@Mapper
public interface StCBansAreaStrategyMapper extends ExtentionMapper<StCBansAreaStrategy> {
    public class StCBansAreaStrategyUpdateSql {
        public String update(final JSONObject jsonObject) {
            return new SQL() {
                {
                    UPDATE("st_c_bans_area_strategy");
                    Set<String> keySet = jsonObject.keySet();
                    for (String key : keySet) {
                        SET(key + "=#{" + key + "}");
                    }
                    WHERE("ID=#{ID}");
                }
            }.toString();
        }

        public String insert(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    INSERT_INTO("st_c_bans_area_strategy");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }
    }

    @InsertProvider(type = StCBansAreaStrategyUpdateSql.class, method = "insert")
    int insertStCBansAreaStrategy(JSONObject jsonObject);

    /**
     * 更新
     *
     * @param jsonObject
     * @return
     */
    @UpdateProvider(type = StCBansAreaStrategyUpdateSql.class, method = "update")
    int updateStCBansAreaStrategy(JSONObject jsonObject);

}
