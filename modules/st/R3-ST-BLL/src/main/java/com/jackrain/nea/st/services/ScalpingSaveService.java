package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.mapper.StCScalpingLogisticsMapper;
import com.jackrain.nea.st.mapper.StCScalpingMapper;
import com.jackrain.nea.st.mapper.StCScalpingReplaceProMapper;
import com.jackrain.nea.st.model.table.StCScalpingDO;
import com.jackrain.nea.st.model.table.StCScalpingLogisticsDO;
import com.jackrain.nea.st.model.table.StCScalpingReplaceProDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Bll层-新增保存业务逻辑
 *
 * <AUTHOR> 黄火县
 * @since : 2019-03-08
 * create at : 2019-03-08 16:30
 */
@Component
@Slf4j
@Transactional
public class ScalpingSaveService extends CommandAdapter {
    @Autowired
    private StCScalpingMapper stCMainMapper;
    @Autowired
    private StCScalpingLogisticsMapper stCItemMapperA;
    @Autowired
    private StCScalpingReplaceProMapper stCItemMapperB;

    private String str_main = "ST_C_SCALPING";
    private String str_lista = "ST_C_SCALPING_LOGISTICS";
    private String str_listb = "ST_C_SCALPING_REPLACE_PRO";

    /**
     * 新增和修改
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject MainMap = fixColumn.getJSONObject(str_main);
                JSONArray ItemsA = fixColumn.getJSONArray(str_lista);
                JSONArray ItemsB = fixColumn.getJSONArray(str_listb);
                valueHolder = saveAction(id, MainMap, ItemsA, ItemsB, querySession);
            }
        } else {
            valueHolder.put("code", ResultCode.SUCCESS);
            return valueHolder;
        }
        return valueHolder;
    }

    public ValueHolder saveAction(Long id, JSONObject mainMap, JSONArray itemsA, JSONArray itemsB,
                                  QuerySession querySession) {
        int iSuc = 0;
        ValueHolder valueHolder = new ValueHolder();
        StCScalpingDO atCMainDO = JsonUtils.jsonParseClass(mainMap, StCScalpingDO.class);
        if (mainMap != null && atCMainDO != null) {
            if (!checkMainData(atCMainDO, id, valueHolder)) {
                return valueHolder;
            }
            //主表新增
            if (id < 0) {
                StBeanUtils.makeCreateField(atCMainDO, querySession.getUser());
                atCMainDO.setId(ModelUtil.getSequence(str_main));
                iSuc = stCMainMapper.insert(atCMainDO);
            } else {
                atCMainDO.setId(id);
                StBeanUtils.makeModifierField(atCMainDO, querySession.getUser());
                iSuc = stCMainMapper.updateById(atCMainDO);
            }
            if (iSuc == 0) {
                valueHolder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return valueHolder;
            }
        }

        if (id > 0) {
            atCMainDO = stCMainMapper.selectById(id);
        }

        //物流公司明细
        if (itemsA != null) {
            List<StCScalpingLogisticsDO> itemLogiList = JSON.parseObject(itemsA.toJSONString(),
                    new TypeReference<ArrayList<StCScalpingLogisticsDO>>() {
                    });
            if (itemLogiList!=null) {

                List<StCScalpingLogisticsDO> stCScalpingLogisticsDOAllList = stCItemMapperA.listByMainid(atCMainDO.getId());
                HashMap<Long, Long> logisticsIdMap = new HashMap<>();
                List<Long> logisticsIdList = new ArrayList<>();
                for (StCScalpingLogisticsDO all : stCScalpingLogisticsDOAllList) {
                    logisticsIdMap.put(all.getCpCLogisticsId(), all.getId());
                }

                for (StCScalpingLogisticsDO stCLogItemDO : itemLogiList) {
                    long itemid = stCLogItemDO.getId();

                    if (stCLogItemDO.getCpCLogisticsId() != null) {
                        if (logisticsIdList.contains(stCLogItemDO.getCpCLogisticsId())
                             || (logisticsIdMap.containsKey(stCLogItemDO.getCpCLogisticsId()) && itemid != logisticsIdMap.get(stCLogItemDO.getCpCLogisticsId()))) {
                            valueHolder = ValueHolderUtils.getFailValueHolder("物流公司不允许重复！");
                            return valueHolder;
                        }
                        logisticsIdList.add(stCLogItemDO.getCpCLogisticsId());
                    }

                    if (itemid < 0) {
                        stCLogItemDO.setId(ModelUtil.getSequence(str_lista));
                        stCLogItemDO.setStCScalpingId(atCMainDO.getId());
                        StBeanUtils.makeCreateField(stCLogItemDO, querySession.getUser());
                        iSuc = stCItemMapperA.insert(stCLogItemDO);
                    } else {
                        StBeanUtils.makeModifierField(stCLogItemDO, querySession.getUser());
                        iSuc = stCItemMapperA.updateById(stCLogItemDO);
                    }
                    if (iSuc == 0) {
                        valueHolder = ValueHolderUtils.getFailValueHolder("保存失败！");
                        return valueHolder;
                    }
                }
            }
        }
        //替换商品明细表
        if (itemsB != null) {
            List<StCScalpingReplaceProDO> itemProList = JSON.parseObject(itemsB.toJSONString(),
                    new TypeReference<ArrayList<StCScalpingReplaceProDO>>() {
                    });
            if (itemProList!=null) {

                List<StCScalpingReplaceProDO> StCScalpingReplaceProDOAllList = stCItemMapperB.listByMainid(atCMainDO.getId());
                HashMap<Long, Long> skuIdMap = new HashMap<>();
                List<Long> skuIdList = new ArrayList<>();
                for (StCScalpingReplaceProDO all : StCScalpingReplaceProDOAllList) {
                    skuIdMap.put(all.getPsCSkuId(), all.getId());
                }

                for (StCScalpingReplaceProDO stCProItemDO : itemProList) {
                    long itemid = stCProItemDO.getId();

                    if (stCProItemDO.getPsCSkuId() != null) {
                        if (skuIdList.contains(stCProItemDO.getPsCSkuId())
                                || (skuIdMap.containsKey(stCProItemDO.getPsCSkuId()) && itemid != skuIdMap.get(stCProItemDO.getPsCSkuId()))) {
                            valueHolder = ValueHolderUtils.getFailValueHolder("替换商品条码不允许重复！");
                            return valueHolder;
                        }
                        skuIdList.add(stCProItemDO.getPsCSkuId());
                    }

                    if (itemid < 0) {
                        stCProItemDO.setId(ModelUtil.getSequence(str_listb));
                        stCProItemDO.setStCScalpingId(atCMainDO.getId());
                        StBeanUtils.makeCreateField(stCProItemDO, querySession.getUser());
                        iSuc = stCItemMapperB.insert(stCProItemDO);
                    } else {
                        StBeanUtils.makeModifierField(stCProItemDO, querySession.getUser());
                        iSuc = stCItemMapperB.updateById(stCProItemDO);
                    }
                    if (iSuc == 0) {
                        valueHolder = ValueHolderUtils.getFailValueHolder("保存失败！");
                        return valueHolder;
                    }
                }
            }
        }
        valueHolder = ValueHolderUtils.getSuccessValueHolder(atCMainDO.getId(),str_main);
        return valueHolder;
    }
    /**
     * @param stCMainDO
     * @param valueHolder
     * @return boolean
     * @Descroption 时间判断
     * @Author: 黄火县
     * @Date 2019/3/26
     */
    private boolean checkMainData(StCScalpingDO stCMainDO, Long mid, ValueHolder valueHolder) {
        if (stCMainDO.getCpCShopId() != null) {
            List<StCScalpingDO>  listScalping = stCMainMapper.listByShopId(stCMainDO.getCpCShopId());
            if (listScalping != null && listScalping.size() > 0) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "店铺名称不能重复！");
                return false;
            }
        }

        if (mid > 0) {
            //修改方案
            StCScalpingDO stCScalpingDO = stCMainMapper.selectById(mid);
            if (stCMainDO.getBeginTime() == null) {
                stCMainDO.setBeginTime(stCScalpingDO.getBeginTime());
            }
            if (stCMainDO.getEndTime() == null) {
                stCMainDO.setEndTime(stCScalpingDO.getEndTime());
            }
        }
        if (stCMainDO.getBeginTime() == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "生效开始时间不能为空！");
            return false;
        }
        if (stCMainDO.getBeginTime().before(new Date())) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "生效开始时间不能小于当前时间！");
            return false;
        }
        if (stCMainDO.getEndTime() == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "生效结束时间不能为空！");
            return false;
        }
        if (stCMainDO.getEndTime().before(stCMainDO.getBeginTime())) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "生效结束时间不能小于开始生效开始时间！");
            return false;
        }
        return true;
    }

}

