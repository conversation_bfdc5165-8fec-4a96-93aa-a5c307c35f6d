package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyDO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.Map;

/**
 * 策略mapper
 */
@Mapper
public interface LiveCastStrategyMapper extends ExtentionMapper<StCLiveCastStrategyDO> {

    /**
     * update status
     * @param status
     * @param statusNew
     * @param strategyIds
     * @param userId
     * @param userName
     * @param userEName
     * @return
     */
    @Update("<script>" +
            "update st_c_live_cast_strategy " +
            "   set strategy_status = #{statusNew}, " +
            "       modifierid = #{userId}, " +
            "       modifierename = #{userEName}, " +
            "       modifiername = #{userName}, " +
            "       modifieddate = now() " +
            "where id in " +
            "<foreach collection=\"strategyIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            "      #{item} " +
            "</foreach> " +
            "  and strategy_status = #{status} " +
            "</script>")
    int updateStrategyStatus(
            @Param("status") String status,
            @Param("statusNew") String statusNew,
            @Param("strategyIds") List<Long> strategyIds,
            @Param("userId") Integer userId,
            @Param("userName") String userName,
            @Param("userEName") String userEName);

    /**
     * 更新状态
     * @param beforeStatus
     * @param afterStatus
     * @param offset
     * @param userId
     * @param userName
     * @param userEName
     * @return
     */
    @Update("update st_c_live_cast_strategy " +
            "   set strategy_status = #{afterStatus}, " +
            "       modifierid = #{userId}, " +
            "       modifierename = #{userEName}, " +
            "       modifiername = #{userName}, " +
            "       modifieddate = now() " +
            " where strategy_status = #{beforeStatus} " +
            "   and end_time <= date_add(now(), interval #{offset} day) ")
    int updateStrategyStatusByAuto(
            @Param("beforeStatus") String beforeStatus,
            @Param("afterStatus") String afterStatus,
            @Param("offset") Integer offset,
            @Param("userId") Integer userId,
            @Param("userName") String userName,
            @Param("userEName") String userEName);

    /**
     * <AUTHOR>
     * @Date 17:41 2021/4/6
     * @Description 直播策略列表界面查询
     */
    @Select("<script> SELECT * FROM st_c_live_cast_strategy \n" +
            "\t\t<where> \n" +
            "\t\t<if test= \"isactives != null and isactives.size() >0\"  > \n" +
            "\t\tAND isactive in \n" +
            "\t\t<foreach  item=\"item\" collection=\"isactives\" index=\"index\"  open=\"(\" separator=\",\" close=\")\"> \n" +
            "\t\t#{item} \n" +
            "\t\t</foreach>  </if> \n" +
            "\t\t<if test=\"cpCShopId != null and cpCShopId != ''\"  > \n" +
            "\t\tAND (cp_c_shop_id like '${cpCShopId},%'  \n" +
            "\t\tor cp_c_shop_id like '%,${cpCShopId}'  \n" +
            "\t\tor cp_c_shop_id like '%,${cpCShopId},%' \n" +
            "\t\tor cp_c_shop_id = '${cpCShopId}')  </if> \n" +
            "\t\t<if test=\"strategyName != null and strategyName != ''\"  > \n" +
            "\t\tAND STRATEGY_NAME like '%${strategyName}%'  </if> \n" +
            "\t\t<if test=\"strategyStatuParse != null and strategyStatuParse.size() >0 \" > \n" +
            "\t\tAND STRATEGY_STATUS in \n" +
            "\t\t<foreach  item=\"item\" collection=\"strategyStatuParse\" index=\"index\"  open=\"(\" separator=\",\" close=\")\"> \n" +
            "\t\t#{item} \n" +
            "\t\t</foreach>  </if> \n" +
            "\t\t<if test=\"billTimeTypeParse != null and billTimeTypeParse.size() >0 \" > \n" +
            "\t\tAND BILL_TIME_TYPE in \n" +
            "\t\t<foreach  item=\"item\" collection=\"billTimeTypeParse\" index=\"index\"  open=\"(\" separator=\",\" close=\")\"> \n" +
            "\t\t#{item} \n" +
            "\t\t</foreach>  </if> \n" +
            "\t\t<if test=\"(startTimeBegin != null and startTimeBegin != '') and (startTimeEnd != null and startTimeEnd != '') \" > \n" +
            "\t\tAND  START_TIME BETWEEN '${startTimeBegin}' AND '${startTimeEnd}' \n" +
            "\t\t</if> \n" +
            "\t\t<if test=\"(endTimeBegin != null and endTimeBegin != '') and (endTimeEnd != null and endTimeEnd != '') \" > \n" +
            "\t\tAND  END_TIME BETWEEN '${endTimeBegin}' AND '${endTimeEnd}' \n" +
            "\t\t</if> \n" +
            "\t\t<if test=\"livePlatformParse != null and livePlatformParse.size() >0 \" > \n" +
            "\t\tAND LIVE_PLATFORM in \n" +
            "\t\t<foreach  item=\"item\" collection=\"livePlatformParse\" index=\"index\"  open=\"(\" separator=\",\" close=\")\"> \n" +
            "\t\t#{item} \n" +
            "\t\t</foreach>  </if> \n" +
            "\t\t<if test=\"anchorId != null and anchorId != ''\"  > \n" +
            "\t\tAND ANCHOR_ID LIKE '%${anchorId}%'  </if> \n" +
            "\t\t<if test=\"anchorNickName != null and anchorNickName != ''\"  > \n" +
            "\t\tAND ANCHOR_NICK_NAME LIKE '%${anchorNickName}%'  </if> \n" +
            "\t\t<if test=\"acFManageid != null and acFManageid != ''\"  > \n" +
            "\t\tAND AC_F_MANAGE_ID = '${acFManageid}'  </if> \n" +
            "\t\t<if test=\"cooperateId != null and cooperateId != ''\"  > \n" +
            "\t\tAND COOPERATE_ID = '${cooperateId}'  </if> \n" +
            "\t\t<if test=\"liveEvents != null and liveEvents != ''\"  > \n" +
            "\t\tAND LIVE_EVENTS = '${liveEvents}'  </if> \n" +
            "\t\t</where> \n" +
            "</script>")
    List<StCLiveCastStrategyDO>  selectPageList(@Param("cpCShopId") String shopId,
                                                @Param("strategyName") String strategyName,
                                                @Param("strategyStatuParse") List<String> strategyStatuParse,
                                                @Param("billTimeTypeParse") List<String> billTimeTypeParse,
                                                @Param("startTimeBegin") String startTimeBegin,
                                                @Param("startTimeEnd") String startTimeEnd,
                                                @Param("endTimeBegin") String endTimeBegin,
                                                @Param("endTimeEnd") String endTimeEnd,
                                                @Param("livePlatformParse") List<String> livePlatformParse,
                                                @Param("anchorId") String anchorId,
                                                @Param("anchorNickName") String anchorNickName,
                                                @Param("isactives") List<String> isactiveParse,
                                                @Param("acFManageid") String acFManageid,
                                                @Param("cooperateId") String cooperateId,
                                                @Param("liveEvents") String liveEvents);
    /**
     * <AUTHOR>
     * @Date 11:30 2021/4/14
     * @Description 修改支持空值修改
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateMainTableSql")
    int updateMainTable(@Param("tableName") String tableName, @Param("insertKeys") JSONObject insertKeys);

    class SqlProvider {
        /**
         * 主表修改
         *
         * @param param
         * @return
         */
        public String updateMainTableSql(Map<String, Object> param) {
            String table = (String) param.get("tableName");
            JSONObject entity = (JSONObject) param.get("insertKeys");

            String sql = new SQL() {
                {
                    UPDATE(table);
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{insertKeys." + key + "}");
                        }
                    }
                    WHERE("ID = #{insertKeys.ID}");
                }
            }.toString();

            return sql;
        }
    }
}
