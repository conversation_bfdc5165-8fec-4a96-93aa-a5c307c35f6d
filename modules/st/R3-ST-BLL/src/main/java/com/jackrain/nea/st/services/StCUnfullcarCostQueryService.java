package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCUnfullcarCostItemMapper;
import com.jackrain.nea.st.mapper.StCUnfullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.request.StCUnfullcarCostDetailQueryRequest;
import com.jackrain.nea.st.model.request.StCUnfullcarCostQueryRequest;
import com.jackrain.nea.st.model.result.StCUnfullcarCostDetailQueryResult;
import com.jackrain.nea.st.model.result.StCUnfullcarCostQueryResult;
import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/2 上午10:55
 * @describe :
 */

@Component
@Slf4j
public class StCUnfullcarCostQueryService {

    @Autowired
    private StCUnfullcarCostMapper unfullcarCostMapper;

    @Autowired
    private StCUnfullcarCostItemMapper unfullcarCostItemMapper;

    /**
     * 根据物流公司 实体仓 查找零担报价设置
     */
    public ValueHolderV14<StCUnfullcarCostQueryResult> queryUnfullcarCost(StCUnfullcarCostQueryRequest request) {

        if (log.isDebugEnabled()) {
            log.info(this.getClass().getName() + " StCUnfullcarCostQueryService,request:{}", JSONObject.toJSONString(request));
        }

        ValueHolderV14<StCUnfullcarCostQueryResult> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        if (request == null
//                || CollectionUtils.isEmpty(request.getLogisticsIdList())
//                || CollectionUtils.isEmpty(request.getWarehouseIdList())
                || request.getRegionProvinceId() == null
                || request.getRegionCityId() == null
                || request.getTotalWeight() == null) {

            result.setCode(ResultCode.FAIL);
            result.setMessage("查询零担报价设置参数为空");
            return result;
        }

//        List<Long> logisticsIdList = request.getLogisticsIdList();
//        List<Long> warehouseIdList = request.getWarehouseIdList();

        Date date = new Date();
        List<StCUnfullcarCost> unfullcarCostList = unfullcarCostMapper.selectList(new QueryWrapper<StCUnfullcarCost>()
                .lambda()
//                .in(StCUnfullcarCost::getCpCLogisticsId, logisticsIdList)
//                .in(StCUnfullcarCost::getCpCPhyWarehouseId, warehouseIdList)
                .eq(StCUnfullcarCost::getStatus, SubmitStatusEnum.SUBMIT.getKey())
                .eq(StCUnfullcarCost::getCloseStatus, CloseStatusEnum.NO_CLOSE.getKey())
                .eq(StCUnfullcarCost::getIsactive, StConstant.ISACTIVE_Y)
                .le(StCUnfullcarCost::getStartDate, date)
                .ge(StCUnfullcarCost::getEndDate, date));

        if (CollectionUtils.isEmpty(unfullcarCostList)) {
            result.setData(getEmptyResult());
            return result;
        }

        List<Long> mainTableIdList = unfullcarCostList.stream().map(StCUnfullcarCost::getId).collect(Collectors.toList());

        List<StCUnfullcarCostItem> unfullcarCostItemList = unfullcarCostItemMapper.selectList(new QueryWrapper<StCUnfullcarCostItem>()
                .lambda()
                .in(StCUnfullcarCostItem::getUnfullcarCostId, mainTableIdList)
                .eq(StCUnfullcarCostItem::getProvinceId, request.getRegionProvinceId())
                .eq(StCUnfullcarCostItem::getCityId, request.getRegionCityId())
                .lt(StCUnfullcarCostItem::getStartWeight, request.getTotalWeight())
                .ge(StCUnfullcarCostItem::getEndWeight, request.getTotalWeight())
                .eq(StCUnfullcarCostItem::getIsactive, StConstant.ISACTIVE_Y));

        if (CollectionUtils.isEmpty(unfullcarCostItemList)) {
            result.setData(getEmptyResult());
            return result;
        }
        Set<Long> existMainTableIdSet = unfullcarCostItemList.stream().map(StCUnfullcarCostItem::getUnfullcarCostId).collect(Collectors.toSet());
        unfullcarCostList = unfullcarCostList.stream().filter(o->existMainTableIdSet.contains(o.getId())).collect(Collectors.toList());

        StCUnfullcarCostQueryResult resultModel = new StCUnfullcarCostQueryResult();
        resultModel.setUnfullcarCostList(unfullcarCostList);
        resultModel.setUnfullcarCostItemList(unfullcarCostItemList);

        result.setData(resultModel);
        return result;
    }
    private StCUnfullcarCostQueryResult getEmptyResult(){
        StCUnfullcarCostQueryResult unfullcarCostQueryResult = new StCUnfullcarCostQueryResult();
        unfullcarCostQueryResult.setUnfullcarCostList(new ArrayList<>());
        unfullcarCostQueryResult.setUnfullcarCostItemList(new ArrayList<>());
        return unfullcarCostQueryResult;
    }

    /**
     * 根据报价ID和重量查询零担报价明细
     *
     * @param request 查询请求
     * @return 查询结果
     */
    public ValueHolderV14<StCUnfullcarCostDetailQueryResult> queryUnfullcarCostDetail(StCUnfullcarCostDetailQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("StCUnfullcarCostQueryService.queryUnfullcarCostDetail, request: {}", JSONObject.toJSONString(request));
        }

        ValueHolderV14<StCUnfullcarCostDetailQueryResult> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        // 参数校验
        if (request == null || request.getUnfullcarCostId() == null || request.getWeight() == null) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询零担报价明细参数不完整");
            return result;
        }

        StCUnfullcarCostDetailQueryResult queryResult = new StCUnfullcarCostDetailQueryResult();

        try {
            // 查询零担报价设置是否存在
            StCUnfullcarCost unfullcarCost = unfullcarCostMapper.selectById(request.getUnfullcarCostId());
            if (unfullcarCost == null) {
                log.debug("未找到零担报价设置，ID: {}", request.getUnfullcarCostId());
                result.setData(queryResult);
                return result;
            }

            // 构建查询条件
            QueryWrapper<StCUnfullcarCostItem> itemWrapper = new QueryWrapper<>();
            itemWrapper.lambda()
                    .eq(StCUnfullcarCostItem::getIsactive, StConstant.ISACTIVE_Y)
                    .eq(StCUnfullcarCostItem::getUnfullcarCostId, request.getUnfullcarCostId())
                    .le(StCUnfullcarCostItem::getStartWeight, request.getWeight())
                    .ge(StCUnfullcarCostItem::getEndWeight, request.getWeight());

            // 如果指定了省份，则添加条件
            if (request.getProvinceId() != null) {
                itemWrapper.lambda().eq(StCUnfullcarCostItem::getProvinceId, request.getProvinceId());
            }

            // 如果指定了城市，则添加条件
            if (request.getCityId() != null) {
                itemWrapper.lambda().eq(StCUnfullcarCostItem::getCityId, request.getCityId());
            }

            List<StCUnfullcarCostItem> unfullcarCostItemList = unfullcarCostItemMapper.selectList(itemWrapper);

            // 设置查询结果
            queryResult.setUnfullcarCostItemList(unfullcarCostItemList);

            result.setData(queryResult);
            return result;
        } catch (Exception e) {
            log.error("查询零担报价明细异常", e);
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询零担报价明细异常: " + e.getMessage());
            return result;
        }
    }
}
