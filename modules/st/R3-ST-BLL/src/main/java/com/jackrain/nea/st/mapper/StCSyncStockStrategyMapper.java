package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.request.StStockPriorityRequest;
import com.jackrain.nea.st.model.result.OwnershipWarehouseSetSyncResult;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
import com.jackrain.nea.st.model.vo.StCSyncStockStrategyVo;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.Date;
import java.util.List;


@Mapper
public interface StCSyncStockStrategyMapper extends ExtentionMapper<StCSyncStockStrategyDO> {
    /**
     * 多店铺查询店铺同步库存
     *
     * @param shopIdList
     * @return
     */
    @Select("<script> SELECT a.CP_C_SHOP_ID,a.CP_C_SHOP_TITLE,a.STOCK_RATE,a.IS_SYNC_STOCK,b.ST_C_SYNC_STOCK_STRATEGY_ID," +
            " b.CP_C_STORE_ID,b.CP_C_STORE_ECODE,b.CP_C_STORE_ENAME,b.PRIORITY,b.RATE,b.LOW_STOCK,b.IS_SEND  FROM ST_C_SYNC_STOCK_STRATEGY a " +
            " INNER JOIN ST_C_SYNC_STOCK_STRATEGY_ITEM b ON b.ST_C_SYNC_STOCK_STRATEGY_ID = a.ID " +
            " WHERE a.CP_C_SHOP_ID IN " +
            "<foreach collection='shopIdList' item='item' open='(' separator=',' close=')'> #{item}</foreach> " +
            "</script>")
    List<StCSyncStockStrategyVo> selectStrategyByShopIds(@Param("shopIdList") List<Long> shopIdList);

    /**
     * 单店铺查询店铺同步库存
     *
     * @param shopId
     * @return
     */
    @Select(" SELECT a.CP_C_SHOP_ID,a.CP_C_SHOP_TITLE,a.STOCK_RATE,a.IS_SYNC_STOCK,c.ST_C_SYNC_STOCK_STRATEGY_ID," +
            " b.CP_C_STORE_ID,b.CP_C_STORE_ECODE,b.CP_C_STORE_ENAME,b.supply_priority,b.RATE,b.LOW_STOCK,b.IS_SEND  FROM ST_C_SYNC_STOCK_STRATEGY a " +
            " INNER JOIN ST_C_SYNC_STOCK_STRATEGY_CHANNEL c ON c.ST_C_SYNC_STOCK_STRATEGY_ID = a.ID  " +
            " INNER JOIN CP_C_ORG_CHANNEL d ON d.ID = c.CP_C_ORG_CHANNEL_ID " +
            " INNER JOIN CP_C_ORG_CHANNEL_ITEM b ON b.CP_C_ORG_CHANNEL_ID = d.ID " +
            " WHERE a.CP_C_SHOP_ID = #{shopId} AND b.IS_SEND = 1 AND a.isactive='Y' " +
            " AND b.isactive='Y' AND c.isactive='Y' AND d.isactive='Y' order by b.supply_priority")
    List<StCSyncStockStrategyVo> selectStrategyByShopId(@Param("shopId") Long shopId);

    /**
     * 单店铺查询店铺同步库存
     *
     * @param shopId
     * @return
     */
    @Select("SELECT b.cp_c_store_id as supplyStoreId,b.supply_priority as priority,b.cp_c_store_ecode as supplyStoreEcode ,b.cp_c_store_ename as supplyStoreEname "
            + "FROM  ST_C_SYNC_STOCK_STRATEGY a LEFT JOIN ST_C_SYNC_STOCK_STRATEGY_CHANNEL c "
            + "ON a.ID = c.ST_C_SYNC_STOCK_STRATEGY_ID LEFT JOIN CP_C_ORG_CHANNEL d "
            + "ON d.ID = c.CP_C_ORG_CHANNEL_ID  LEFT JOIN CP_C_ORG_CHANNEL_ITEM b "
            + "ON d.ID = b.CP_C_ORG_CHANNEL_ID WHERE a.cp_c_shop_id=#{cpCShopId} "
            + "AND a.isactive='Y' AND b.isactive='Y' AND c.isactive='Y' AND d.isactive='Y'")
    List<StStockPriorityRequest> selectStockPriorityByShopId(@Param("cpCShopId") Long shopId);

    /**
     * 根据店铺编码 查询 店铺库存同步策略的 数据集合
     *
     * @param cpCShopId 店铺编码
     * @return 返回店铺库存同步策略的实体集合
     */
    @Select("SELECT * FROM ST_C_SYNC_STOCK_STRATEGY WHERE CP_C_SHOP_ID = #{cpCShopId} and isactive='Y'")
    List<StCSyncStockStrategyDO> selectBycpCShopId(@Param("cpCShopId") Long cpCShopId);

    /**
     * 更新店铺库存同步策略的店铺名称
     *
     * @param cpCShopTitle 店铺名称
     * @param cpCShopId    店铺编码
     * @param channelType  渠道类型
     * @return
     */
    @Update("UPDATE ST_C_SYNC_STOCK_STRATEGY\n" +
            "SET CP_C_SHOP_TITLE = #{cpCShopTitle}, CP_C_PLATFORM_ID = #{cpCPlatformId}, MODIFIERID = #{modifierid}," +
            "CHANNEL_TYPE = #{channelType}, MODIFIERENAME = #{modifierename}, MODIFIEDDATE = #{modifieddate}\n" +
            "WHERE CP_C_SHOP_ID = #{cpCShopId}")
    int updateShopTitleByShopId(@Param("cpCShopTitle") String cpCShopTitle,
                                @Param("cpCPlatformId") Long cpCPlatformId,
                                @Param("channelType") String channelType,
                                @Param("modifierid") Long modifierid,
                                @Param("modifierename") String modifierename,
                                @Param("modifieddate") Date modifieddate,
                                @Param("cpCShopId") Long cpCShopId);

    /**
     * 根据店铺id查询店仓id集合
     *
     * @param shopId 店铺id
     * @return 店仓id集合
     */
    @Select("SELECT cp_c_store_id FROM st_c_sync_stock_strategy a " +
            "INNER JOIN st_c_sync_stock_strategy_channel c ON a.id = c.st_c_sync_stock_strategy_id " +
            "INNER JOIN CP_C_ORG_CHANNEL d ON d.ID = c.CP_C_ORG_CHANNEL_ID " +
            "INNER JOIN CP_C_ORG_CHANNEL_ITEM b ON d.id=b.CP_C_ORG_CHANNEL_ID " +
            "WHERE a.cp_c_shop_id =#{shopId} AND a.isactive='Y' AND b.isactive='Y' AND b.is_send=1 AND c.isactive='Y' AND d.isactive='Y'")
    List<Long> queryStoreIdsByShopId(Long shopId);


    /**
     * 查找店铺下面的对应逻辑供货仓
     *
     * @param cpCShopId 店铺Id
     * @param storeList 逻辑仓集合
     * @return List<Long>
     */
    @Select("<script> SELECT cp_c_store_id FROM  ST_C_SYNC_STOCK_STRATEGY a LEFT JOIN ST_C_SYNC_STOCK_STRATEGY_CHANNEL c "
            + "ON a.id = c.st_c_sync_stock_strategy_id LEFT JOIN CP_C_ORG_CHANNEL d ON d.ID = c.CP_C_ORG_CHANNEL_ID "
            + "LEFT JOIN CP_C_ORG_CHANNEL_ITEM b "
            + "ON d.id = b.CP_C_ORG_CHANNEL_ID WHERE a.cp_c_shop_id=#{cpCShopId} and b.cp_c_store_id in "
            + "<foreach collection='storeList' item='item' open='(' separator=',' close=')'> #{item} </foreach> "
            + "AND a.isactive='Y' AND b.isactive='Y'  AND b.is_send=1 AND c.isactive='Y' AND d.isactive='Y' </script>")
    List<Long> queryShopStoreList(@Param("cpCShopId") Long cpCShopId,
                                  @Param("storeList") List<Long> storeList);

    /***
     * 查找店仓默认优先级
     * @param cpCShopId 店铺Id
     * @param storeList 逻辑仓集合
     * @return List<StStockPriorityRequest>
     */
    @Select("<script> SELECT b.cp_c_store_id as supplyStoreId,b.supply_priority as priority,cp.ECODE "
            + "as supplyStoreEcode,cp.ENAME as supplyStoreEname FROM  ST_C_SYNC_STOCK_STRATEGY a "
            + "LEFT JOIN ST_C_SYNC_STOCK_STRATEGY_CHANNEL c ON a.id = c.st_c_sync_stock_strategy_id "
            + "LEFT JOIN CP_C_ORG_CHANNEL d ON d.ID = c.CP_C_ORG_CHANNEL_ID "
            + "LEFT JOIN CP_C_ORG_CHANNEL_ITEM b ON d.id = b.CP_C_ORG_CHANNEL_ID LEFT JOIN CP_C_STORE cp on cp.id = b.cp_c_store_id WHERE "
            + "a.cp_c_shop_id=#{cpCShopId} and b.cp_c_store_id in "
            + "<foreach collection='storeList' item='item' open='(' separator=',' close=')'> #{item} </foreach> "
            + "AND a.isactive='Y' AND b.isactive='Y' AND c.isactive='Y' AND d.isactive='Y' </script>")
    List<StStockPriorityRequest> queryStStockPriority(@Param("cpCShopId") Long cpCShopId,
                                                      @Param("storeList") List<Long> storeList);

    /**
     * 查找店铺下面的对应逻辑供货仓
     *
     * @param cpCShopId 店铺Id
     * @return List<Long>
     */
    @Select("SELECT cp_c_store_id FROM  ST_C_SYNC_STOCK_STRATEGY a LEFT JOIN ST_C_SYNC_STOCK_STRATEGY_CHANNEL c "
            + "ON a.id = c.st_c_sync_stock_strategy_id LEFT JOIN CP_C_ORG_CHANNEL d "
            + "ON d.ID = c.CP_C_ORG_CHANNEL_ID LEFT JOIN CP_C_ORG_CHANNEL_ITEM b "
            + "ON d.id = b.CP_C_ORG_CHANNEL_ID WHERE a.cp_c_shop_id=#{cpCShopId} AND a.isactive='Y' "
            + "AND b.isactive='Y' AND c.isactive='Y' AND d.isactive='Y'")
    List<Long> queryShopStoreNextList(@Param("cpCShopId") Long cpCShopId);

    @Select("SELECT s.* FROM `st_c_sync_stock_strategy` s  " +
            "LEFT JOIN st_c_sync_stock_strategy_channel c ON s.id = c.st_c_sync_stock_strategy_id " +
            "WHERE s.isactive = 'Y' AND c.st_c_channel_strategy_id = #{stCChannelStrategyId}")
    List<StCSyncStockStrategyDO> selectSyncStockStrategyIdsByChannelStrategyId(@Param("stCChannelStrategyId") Long id);

    /**
     * 店铺同步库存策略 批量更新SQL创建器
     */
    class sqlBuilder {
        /**
         * 创建更新订单转换状态SQL
         *
         * @param stockRate   整体库存比例
         * @param lowStock    低库存数
         * @param isSyncStock 是否同步库存
         * @param id          店铺库存策略id
         * @return 更新SQL语句
         */
        public String buildUpdateSQL(@Param("stockRate") String stockRate,
                                     @Param("lowStock") String lowStock,
                                     @Param("isSyncStock") String isSyncStock,
                                     @Param("id") String id) {

            return new SQL() {
                {
                    UPDATE("ST_C_SYNC_STOCK_STRATEGY");
                    if (!"".equals(stockRate)) {
                        SET("STOCK_RATE = #{stockRate}");
                    }
                    if (!"".equals(lowStock)) {
                        SET("LOW_STOCK = #{lowStock}");
                    }
                    if (!"".equals(isSyncStock)) {
                        SET("IS_SYNC_STOCK = #{isSyncStock}");
                    }
                    if (!"".equals(id)) {

                        WHERE("id =  #{id}");
                    }
                }
            }.toString();
        }
    }

    /**
     * 店铺同步库存策略 批量更新
     *
     * @param stockRate   整体库存比例
     * @param lowStock    低库存数
     * @param isSyncStock 是否同步库存
     * @param id          店铺库存策略id
     * @return 更新SQL语句
     */
    @UpdateProvider(type = sqlBuilder.class, method = "buildUpdateSQL")
    int updateMain(@Param("stockRate") String stockRate, @Param("lowStock") String lowStock,
                   @Param("isSyncStock") String isSyncStock, @Param("id") String id);


    @Select("SELECT * FROM ST_C_SYNC_STOCK_STRATEGY")
    List<StCSyncStockStrategyDO> selectAll();

    @Select("SELECT\n" +
            "\ta.*\n" +
            "FROM\n" +
            "\tST_C_SYNC_STOCK_STRATEGY a\n" +
            "INNER JOIN ST_C_SYNC_STOCK_STRATEGY_CHANNEL c ON c.ST_C_SYNC_STOCK_STRATEGY_ID = a.ID\n" +
            "INNER JOIN CP_C_ORG_CHANNEL d ON d.ID = c.CP_C_ORG_CHANNEL_ID\n" +
            "INNER JOIN CP_C_ORG_CHANNEL_ITEM b ON b.CP_C_ORG_CHANNEL_ID = d.ID\n" +
            "WHERE\n" +
            "\ta.isactive = 'Y'\n" +
            "AND b.isactive = 'Y'\n" +
            "AND c.isactive = 'Y'\n" +
            "AND d.isactive = 'Y'\n" +
            "GROUP BY a.id")
    List<StCSyncStockStrategyDO> selectAllSyncStockStrategy();

    @Select("select * from st_c_sync_stock_strategy a join st_c_sync_stock_strategy_item c on " +
            "a.id = c.st_c_sync_stock_strategy_id and a.isactive = 'Y' and (a.CHANNEL_TYPE = 1 or a.CHANNEL_TYPE = 2)" +
            " where a.id in(select b.st_c_sync_stock_strategy_id from st_c_sync_stock_strategy_channel b) group by a.CHANNEL_TYPE,c.cp_c_store_id")
    List<OwnershipWarehouseSetSyncResult> selectAllByChannelTypeAndStoreId();

    @Select("select st.cp_c_shop_id from st_c_sync_stock_strategy st join " +
            "st_c_sync_stock_strategy_channel c on st.id = c.st_c_sync_stock_strategy_id " +
            "where c.cp_c_org_channel_id = #{channelId}")
    List<Long> selectShopIdsByChannelId(@Param("channelId") Long channelId);
}