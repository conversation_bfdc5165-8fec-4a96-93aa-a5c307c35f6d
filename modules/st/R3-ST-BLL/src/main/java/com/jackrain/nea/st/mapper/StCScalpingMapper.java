package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCScalpingDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Mapper
@Component
public interface StCScalpingMapper extends ExtentionMapper<StCScalpingDO> {

    @Select("select * from st_c_scalping where cp_c_shop_id = #{shopId} ")
    List<StCScalpingDO> listByShopId(@Param("shopId") Long shopId);


    /**
     * ljp add
     *
     * @param shopId
     * @param keyword
     * @param payTime
     * @return
     */
    @Select("select * from ST_C_SCALPING where isactive = 'Y' and estatus = 2 " +
            "and cp_c_shop_id=#{shopId}  " +
            "and keyword = #{keyword} " +
            "and begin_time<= #{payTime} " +
            "and end_time > #{payTime}")
    StCScalpingDO queryScalpingByshopId(@Param("shopId") Long shopId, @Param("keyword") String keyword,
                                        @Param("payTime") Date payTime);
}
