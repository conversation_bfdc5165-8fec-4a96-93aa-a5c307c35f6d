package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StcMsgDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 短信策略
 */
@Component
@Mapper
public interface StCMsgMapper extends ExtentionMapper<StcMsgDO> {

    /**
     * 查询短信策略by店铺id
     * @param cpCShopId: 店铺ID
     * @param adviceType: 通知类型
     * @param taskNode: 任务节点
     * @return
     */
    @Select("<script> SELECT * FROM st_c_msg_strategy\n" +
                    "WHERE isactive = 'Y'\n" +
                    "<if test=\"cpCShopId != null and cpCShopId != '' \" >" +
                    "AND (cp_c_shop_id like '${cpCShopId},%' \n" +
                        "or cp_c_shop_id like '%,${cpCShopId}' \n" +
                        "or cp_c_shop_id like '%,${cpCShopId},%'\n" +
                        "or cp_c_shop_id = '${cpCShopId}')" + "</if>\n" +
                    "<if test=\"msgName != null and msgName != '' \" >" +
                    "AND msg_name = #{msgName}" + "</if>" +
                    "<if test=\"adviceType != null and adviceType != '' \" >" +
                    "AND advice_type = #{adviceType}" + "</if>" +
                    "<if test=\"taskNode != null and taskNode != '' \" >" +
                    "AND task_node = #{taskNode}" + "</if>" +
            "</script>"
    )
    List<StcMsgDO> selectByShopId(@Param("cpCShopId") Long cpCShopId,
                                           @Param("msgName") String msgName,
                                           @Param("adviceType") String adviceType,
                                           @Param("taskNode") String taskNode);

    /**
     * 查询短信策略by店铺id 不区分是否可用
     * @param cpCShopId: 店铺ID
     * @param adviceType: 通知类型
     * @param taskNode: 任务节点
     * @return
     */
    @Select("<script> SELECT * FROM st_c_msg_strategy\n" +
            "<where>" +
            "<if test=\"isactive != null and isactive != '' \" >" +
            "AND isactive = #{isactive}" + "</if>" +
            "<if test=\"cpCShopId != null and cpCShopId != '' \" >" +
            "AND (cp_c_shop_id like '${cpCShopId},%' \n" +
            "or cp_c_shop_id like '%,${cpCShopId}' \n" +
            "or cp_c_shop_id like '%,${cpCShopId},%'\n" +
            "or cp_c_shop_id = '${cpCShopId}')" + "</if>\n" +
            "<if test=\"msgName != null and msgName != '' \" >" +
            "AND msg_name = #{msgName}" + "</if>" +
            "<if test=\"adviceType != null and adviceType != '' \" >" +
            "AND advice_type = #{adviceType}" + "</if>" +
            "<if test=\"taskNode != null and taskNode != '' \" >" +
            "AND task_node = #{taskNode}" + "</if>" +
            "</where>" +
            "</script>"
    )
    List<StcMsgDO> selectByParam(@Param("cpCShopId") Long cpCShopId,
                                  @Param("msgName") String msgName,
                                  @Param("isactive") String isactive,
                                  @Param("adviceType") String adviceType,
                                  @Param("taskNode") String taskNode);


    @Select("<script> SELECT * FROM st_c_msg_strategy\n" +
            "<where>" +
            "<if test=\"isactives != null and isactives.size() >0 \" >" +
            "AND isactive in" +
            "<foreach  item=\"item\" collection=\"isactives\" index=\"index\"  open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}\n" +
            "</foreach>" + "</if>" +
            "<if test=\"cpCShopId != null and cpCShopId != '' \" >" +
            "AND (cp_c_shop_id like '${cpCShopId},%' \n" +
            "or cp_c_shop_id like '%,${cpCShopId}' \n" +
            "or cp_c_shop_id like '%,${cpCShopId},%'\n" +
            "or cp_c_shop_id = '${cpCShopId}')" + "</if>\n" +
            "<if test=\"msgName != null and msgName != '' \" >" +
            "AND msg_name like '%${msgName}%'" + "</if>" +
            "<if test=\"adviceTypes != null and adviceTypes.size() >0\" >" +
            "AND advice_type in" +
            "<foreach  item=\"item\" collection=\"adviceTypes\" index=\"index\"  open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}\n" +
            "</foreach>" + "</if>" +
            "<if test=\"taskNodes != null and taskNodes.size() >0 \" >" +
            "AND task_node in" +
            "<foreach  item=\"item\" collection=\"taskNodes\" index=\"index\"  open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}\n" +
            "</foreach>" + "</if>" +
            "<if test=\"isSends != null and isSends.size() >0 \" >" +
            "AND is_send in" +
            "<foreach  item=\"item\" collection=\"isSends\" index=\"index\"  open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}\n" +
            "</foreach>" + "</if>" +
            "</where>" +
            "</script>"
    )
    List<StcMsgDO> selectPageList(@Param("cpCShopId") String cpCShopId,
                                  @Param("msgName") String msgName,
                                  @Param("isSends") List<String> isSends,
                                  @Param("isactives") List<String> isactives,
                                  @Param("adviceTypes") List<String> adviceTypes,
                                  @Param("taskNodes") List<String> taskNodes);

}