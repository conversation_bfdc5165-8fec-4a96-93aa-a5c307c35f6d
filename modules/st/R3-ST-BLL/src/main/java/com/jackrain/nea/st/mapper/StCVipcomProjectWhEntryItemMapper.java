package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCVipcomProjectWhEntryItem;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper
public interface StCVipcomProjectWhEntryItemMapper extends ExtentionMapper<StCVipcomProjectWhEntryItem> {

    @Delete("delete from st_c_vipcom_project_wh_entry_item where id = #{id}")
    int deleteProjectWarehouseEntryItemById(@Param("id") Long id);

    @Delete("delete from st_c_vipcom_project_wh_entry_item where st_c_vipcom_project_id = #{masterId}")
    int deleteProjectWarehouseEntryItemByMasterId(@Param("masterId") Long masterId);

    @Select("select * from st_c_vipcom_project_wh_entry_item where st_c_vipcom_project_id = #{masterId}")
    List<StCVipcomProjectWhEntryItem> queryPickorderItemByMasterId(@Param("masterId") Long masterId);

}