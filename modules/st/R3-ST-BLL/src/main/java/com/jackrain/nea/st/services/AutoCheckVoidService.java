package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCAutoCheckMapper;
import com.jackrain.nea.st.model.table.StCAutoCheckDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Descroption 订单自动审核-作废逻辑
 * <AUTHOR>
 * @Date 2019/3/12 18:13
 */
@Component
@Slf4j
@Transactional
public class AutoCheckVoidService extends CommandAdapter {
    @Autowired
    private StCAutoCheckMapper stCAutocheckMapper;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        JSONArray itemArray = StBeanUtils.makeVoidJsonArray(param);
        JSONArray errorArray = new JSONArray();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                saveAutocheckByID(querySession, itemid, errorArray);
            }
        }
        return StBeanUtils.getProcessValueHolder(itemArray, errorArray, StConstant.CON_BILL_ACTION_VOID);
    }

    /**
     * @param session
     * @param id
     * @param errorArray
     * @return void
     * @Descroption 作废保存逻辑
     * @Author: 郑小龙
     * @Date 2019/3/12
     */
    private void saveAutocheckByID(QuerySession session, Long id, JSONArray errorArray) {
        StCAutoCheckDO stCAutocheckDO = stCAutocheckMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkAutocheckStatus(stCAutocheckDO, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }
        StBeanUtils.makeModifierField(stCAutocheckDO, session.getUser());//修改信息
        stCAutocheckDO.setIsactive(StConstant.ISACTIVE_N);//作废状态
        stCAutocheckDO.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        stCAutocheckDO.setDelTime(new Date());//作废时间
        stCAutocheckDO.setDelname(session.getUser().getName());//作废人姓名
        stCAutocheckDO.setDelename(session.getUser().getEname());//作废人账号
        stCAutocheckDO.setModifierename(session.getUser().getEname());//修改人账号
        if ((stCAutocheckMapper.updateById(stCAutocheckDO)) <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "店铺名称:" + stCAutocheckDO.getCpCShopTitle() + ",作废失败！"));
        }
        //店铺清除rediskey
        delRedisKey(id);
    }

    /**
     * @param stCAutocheckDO
     * @param id
     * @Descroption 数据判断
     * @Author: 郑小龙
     * @Date 2019/3/12
     */
    private void checkAutocheckStatus(StCAutoCheckDO stCAutocheckDO, Long id, JSONArray errorArray) {
        if (stCAutocheckDO == null) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已不存在！"));
            return;
        }
        //记录已作废，不允许作废
        if (stCAutocheckDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已作废，不允许作废！"));
            return;
        }
    }

    private void delRedisKey(Long id) {
        try {
            String redisKey = OmsRedisKeyResources.buildAutoCheckAllListRedisKey();
            if (redisUtil.strRedisTemplate.hasKey(redisKey)) {
                redisUtil.strRedisTemplate.delete(redisKey);
            }
            if (id != null) {
                String idRedisKey = OmsRedisKeyResources.bulidLockStCAutoCheckKey(id);
                if (redisUtil.strRedisTemplate.hasKey(idRedisKey)) {
                    redisUtil.strRedisTemplate.delete(idRedisKey);
                }
            }

        } catch (Exception e) {
            log.error(LogUtil.format("redis保存自动审单策略异常{}"), Throwables.getStackTraceAsString(e));
        }
    }
}
