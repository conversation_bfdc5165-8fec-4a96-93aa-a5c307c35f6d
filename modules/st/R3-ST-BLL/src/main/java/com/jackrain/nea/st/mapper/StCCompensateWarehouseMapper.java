package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCCompensateWarehouseDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper
public interface StCCompensateWarehouseMapper extends ExtentionMapper<StCCompensateWarehouseDO> {

    @Select("SELECT * FROM ST_C_COMPENSATE_WAREHOUSE WHERE ST_C_LOGISTICS_COMPENSATE_ID = #{slaverId}")
    List<StCCompensateWarehouseDO> selectWarehouseBySlaverId(Long slaverId);

    @Delete("DELETE FROM ST_C_COMPENSATE_WAREHOUSE WHERE ST_C_LOGISTICS_COMPENSATE_ID = #{masterId}")
    int deleteByMasterId(Long masterId);
}