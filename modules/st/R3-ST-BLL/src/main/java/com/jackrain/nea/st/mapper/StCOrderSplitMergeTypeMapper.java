package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCOrderSplitMergeType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCOrderSplitMergeTypeMapper extends ExtentionMapper<StCOrderSplitMergeType> {

    /**
     *  根据拆单的所有原因
     * @return List<StCOrderSplitMergeType>
     */

    @Select("SELECT * FROM ST_C_ORDER_SPLIT_MERGE_TYPE LIMIT 100")
    List<StCOrderSplitMergeType> selectStCOrderSplitMergeTypeList();

}