package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressMapper;
import com.jackrain.nea.st.model.table.StCExpressDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * @Descroption 物流方案审核
 * <AUTHOR>
 * @Date 2019/3/22 15:55
 */
@Component
@Slf4j
public class ExpressAuditService extends CommandAdapter {
    @Autowired
    private StCExpressMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start ExpressAuditService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = auditArray.getLong(i);
            try {
                //4.遍历审核方法
                auditExpress(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    public void auditExpress(Long id, QuerySession querySession) {
        StCExpressDO expressDo = mapper.selectById(id);
        //主表校验
        checkExpress(expressDo);

        //更新单据状态
        StBeanUtils.makeModifierField(expressDo, querySession.getUser());
        expressDo.setBillStatus(StConstant.CON_BILL_STATUS_02);
        setAuditCommonField(expressDo, querySession.getUser());
        int updateNum = mapper.updateById(expressDo);
        if (updateNum < 0) {
            throw new NDSException("审核失败！");
        }
    }

    private void checkExpress(StCExpressDO expressDo) {
        if (expressDo == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(expressDo.getBillStatus())) {
                throw new NDSException("当前记录已审核，不允许重复审核！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(expressDo.getBillStatus())){
                throw new NDSException("当前记录已作废，不允许审核！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(expressDo.getBillStatus())){
                throw new NDSException("当前记录已结案，不允许审核！");
            }
        }
    }

    private void setAuditCommonField(StCExpressDO expressDo, User user) {
        expressDo.setCheckid(Long.valueOf(user.getId()));
        expressDo.setCheckename(user.getEname());
        expressDo.setCheckname(user.getName());
        expressDo.setChecktime(new Date());
    }

}
