package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCSendRuleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface StCSendRuleMapper extends ExtentionMapper<StCSendRuleDO> {
    @Select("select * from st_c_send_rule where ename = #{ename} AND isactive='Y'")
    List<StCSendRuleDO> listByEname(@Param("ename") String ename);

    /**
     * 根据派单方案下维护的派单规则进行查询
     *
     * @param sendRuleId 规则Id
     * @return json
     */
    @Select("SELECT * FROM st_c_send_rule WHERE id=#{sendRuleId} AND isactive='Y'")
    StCSendRuleDO querySendRuleTypeById(Long sendRuleId);

    /**
     * 查找分仓比例的规则Id
     *
     * @param sendRuleIds 规则Id
     * @param type 规则类型
     * @return List<Long>
     */
    @Select("<script> SELECT id FROM st_c_send_rule WHERE etype=#{type} and id in <foreach collection='sendRuleIds' item='item' "
            + " open='(' separator=',' close=')'> #{item} </foreach> AND isactive='Y' limit 1 </script>")
    Long selectSendRuleByIds(@Param("sendRuleIds") List<Long> sendRuleIds,@Param("type") String type);

    /**
     * 查找分仓比例的规则Id
     *
     * @param sendRuleIds 规则Id
     * @return List<Long>
     */
    @Select("<script> SELECT * FROM st_c_send_rule WHERE id in <foreach collection='sendRuleIds' item='item' "
            + " open='(' separator=',' close=')'> #{item} </foreach> AND isactive='Y' </script>")
    List<StCSendRuleDO> selectSendRuleByIdList(@Param("sendRuleIds") List<Long> sendRuleIds);
}