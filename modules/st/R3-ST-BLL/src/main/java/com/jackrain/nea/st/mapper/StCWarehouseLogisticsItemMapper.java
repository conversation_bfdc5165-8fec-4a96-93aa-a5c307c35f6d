package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCWarehouseLogisticsItemMapper extends ExtentionMapper<StCWarehouseLogisticsItemDO> {
    @Select("select * from st_c_warehouse_logistics_item where st_c_warehouse_logistics_id = #{mainid}")
    List<StCWarehouseLogisticsItemDO> listByMainid(@Param("mainid") Long mainid);
}