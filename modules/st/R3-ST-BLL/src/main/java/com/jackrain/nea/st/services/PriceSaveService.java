package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.observer.SequenceExec;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Descroption 商品价格策略-保存逻辑
 * <AUTHOR>
 * @Date 2019/3/11
 */
@Component
@Slf4j
@Transactional
public class PriceSaveService extends CommandAdapter {
    @Autowired
    private StCPriceMapper stCPriceMapper;
    @Autowired
    private StCPriceItemMapper stCPriceItemMapper;
    @Autowired
    private StCPriceExcludeItemMapper stCPriceExcludeItemMapper;

    @Autowired
    private StCPriceShopMapper stCPriceShopMapper;
    @Autowired
    private RpcPsService rpcPsService;
    @Autowired
    private StCOrderPriceItemMapper stCOrderPriceItemMapper;

    @Reference(version = "1.0", group = "cp-ext")
    private CpShopQueryCmd cpShopQueryCmd;

    @Override
    @StOperationLog(mainTableName = "ST_C_PRICE",itemsTableName = "ST_C_PRICE_EXCLUDE_ITEM")
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null) {
            if (id != null && id > 0) {
                /*修改*/
                return updatePrice(querySession, fixColumn, id);
            } else {
                /*新增*/
                return addPrice(querySession, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private ValueHolder addPrice(QuerySession session, JSONObject fixColumn) {
        ValueHolder valueHolder = new ValueHolder();
        String postfee = fixColumn.getString(StConstant.TAB_ST_C_PRICE);
        if (StringUtils.isNotEmpty(postfee)) {
            StCPriceDO stCPriceDO = JSON.parseObject(postfee, StCPriceDO.class);
            Long id = null;

            /*业务要求店铺多选,新增时一个店铺增加一条记录*/
            String[] shopIdArr = stCPriceDO.getCpCShopId().split(",");

            /*如果只有一个店铺，直接新增即可*/
            if (shopIdArr.length == 1) {
                stCPriceDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRICE));
                if (!checkPrice(stCPriceDO, valueHolder)) {
                    return valueHolder;
                }
                id = createPrice(session, fixColumn, stCPriceDO);
                return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_PRICE);
            }

            /*多个店铺时，拼接店铺code作为名称，生成多条数据*/
            Map<String, String> shopIdCodeMap = queryCpShopCode(shopIdArr);
            for (int i = 0; i < shopIdArr.length; i++) {
                StCPriceDO priceDO = new StCPriceDO();
                BeanUtils.copyProperties(stCPriceDO, priceDO);
                priceDO.setCpCShopId(shopIdArr[i]);

                priceDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRICE));
                if (i == 0 && !checkPrice(priceDO, valueHolder)) {
                    return valueHolder;
                }
                priceDO.setEname(priceDO.getEname() + "-" + shopIdCodeMap.get(shopIdArr[i]));
                id = createPrice(session, fixColumn, priceDO);
            }

            return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_PRICE);
        }
        throw new NDSException("当前表" + StConstant.TAB_ST_C_PRICE + "不存在！");
    }


    private Map<String, String> queryCpShopCode(String[] shopIdArr) {
        if (shopIdArr == null || shopIdArr.length == 0) {
            return Collections.emptyMap();
        }

        List<CpShop> shopList = cpShopQueryCmd.queryShopByIds(Arrays.stream(shopIdArr).map(Long::valueOf)
                .distinct().collect(Collectors.toList()));
        return ListUtils.emptyIfNull(shopList).stream()
                .collect(Collectors.toMap(a -> String.valueOf(a.getId()), CpShop::getEcode));
    }

    private Long createPrice(QuerySession session, JSONObject fixColumn, StCPriceDO priceDO) {
        //基本字段值设置
        StBeanUtils.makeCreateField(priceDO, session.getUser());//创建修改等信息
        priceDO.setModifierename(session.getUser().getEname());//修改人账号
        priceDO.setOwnerename(session.getUser().getEname());//创建人账号
        JSONArray errorArray = new JSONArray();

        int insertRows = stCPriceMapper.insert(priceDO);
        if (insertRows <= 0) {
            log.error(LogUtil.format("创建商品价格策略失败，影响行数不为1，入参：{}", "PriceSaveService.createPrice"), fixColumn.toString());
            throw new NDSException("创建商品价格策略失败");
        }

        //单据编号
        JSONObject sequence = new JSONObject();
        sequence.put("TAB_ST_C_PRICE", "PARAN");
        SequenceExec exec = SequenceGenUtil.preGenerateSequence()
                .add("SEQ_ST_C_PRICE", sequence, priceDO.getId(), stCPriceMapper, "updateSequence");
        exec.exec();

        //商品价格策略店铺明细
        if (priceDO.getCpCShopId() != null) {
            String ids = priceDO.getCpCShopId();
            String[] lIds = ids.split(",");
            /*for循环insert 店铺明细 表 ST_C_PRICE_SHOP (其实这里肯定只有一个，不想改动原有代码)*/
            savePriceShop(session, priceDO.getId(), lIds);
        }

        /*保存商品价格策略明细 表 ST_C_PRICE_ITEM */
        savePriceItem(session, fixColumn, priceDO.getId(), errorArray);
        /*添加订单价格策略明细 表 ST_C_ORDER_PRICE_ITEM*/
        saveOrderPriceItem(session, fixColumn, priceDO.getId(), errorArray);
        /*添加排除商品明细 表 ST_C_PRICE_EXCLUDE_ITEM*/
        savePriceExcludeItem(session, fixColumn, priceDO.getId(), errorArray);

        /*删除redis缓存*/
        deleteCacheByCpShopIds(priceDO.getCpCShopId());
        return priceDO.getId();
    }

    /**
     * 删除缓存，多选的时候参数形式为："CP_C_SHOP_ID":"4539,5538"
     *
     * @param cpCShopId 店铺ID列表，【,】隔开
     */
    private void deleteCacheByCpShopIds(String cpCShopId) {
        if (StringUtils.isBlank(cpCShopId)) {
            return;
        }
        String[] ids = cpCShopId.split(",");
        for (String id : ids) {
            Long shopId = StringUtils.isBlank(id) ? null : Long.valueOf(id);
            RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY);
            RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY_INFO);
        }
    }

    /**
     * <AUTHOR>
     * @Date 14:36 2021/5/27
     * @Description 商品价格策略明细 保存
     */
    private void saveOrderPriceItem(QuerySession session, JSONObject fixColumn, Long id, JSONArray errorArray) {
        log.info(LogUtil.format("开始更新商品价格策略明细，{}"), fixColumn);
        String orderPriceItem = fixColumn.getString(StConstant.TAB_ST_C_ORDER_PRICE_ITEM);
        if (StringUtils.isNotEmpty(orderPriceItem)) {
            try {
                List<StCOrderPriceItemDO> stCOrderPriceItemDOList = JSON.parseArray(orderPriceItem, StCOrderPriceItemDO.class);
                if (stCOrderPriceItemDOList.isEmpty() || stCOrderPriceItemDOList.size() <= 0) {
                    return;
                }
                //商品价格明细保存
                for (StCOrderPriceItemDO stCOrderPriceItemDO : stCOrderPriceItemDOList) {
                    checkPriceType(stCOrderPriceItemDO);
                    checkOrderPriceItem(stCOrderPriceItemDO,id);
                    stCOrderPriceItemDO.setModifierename(session.getUser().getEname());//修改人账号
                    if (stCOrderPriceItemDO.getId() != null && stCOrderPriceItemDO.getId() > 0) {
                        //明细修改
                        StBeanUtils.makeModifierField(stCOrderPriceItemDO, session.getUser());//修改信息
                        if ((stCOrderPriceItemMapper.updateById(stCOrderPriceItemDO)) <= 0) {
                            throw new NDSException("订单价格策略-明细修改失败！");
                        }
                    } else {
                        //明细创建
                        stCOrderPriceItemDO.setStCPriceId(id);
                        stCOrderPriceItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRICE_ITEM));
                        StBeanUtils.makeCreateField(stCOrderPriceItemDO, session.getUser());//创建信息
                        stCOrderPriceItemDO.setOwnerename(session.getUser().getEname());//创建人账号
                        if ((stCOrderPriceItemMapper.insert(stCOrderPriceItemDO)) <= 0) {
                            throw new NDSException("订单价格策略-明细保存失败！");
                        }
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("PriceSaveService.saveOrderPriceItem.Error{}")
                        , Throwables.getStackTraceAsString(ex));
                throw new NDSException("订单价格策略明细保存异常" + ex.getMessage());
            }
        }
    }

    /**
     * <AUTHOR>
     * @Date 16:31 2021/5/27
     * @Description 校验订单价格策略明细表
     */
    private void checkOrderPriceItem(StCOrderPriceItemDO stCOrderPriceItemDO,Long id ) {
        BigDecimal qtyDiscount = stCOrderPriceItemDO.getQtyDiscount();
        if (BigDecimal.ZERO.compareTo(qtyDiscount) > 0){
            throw new NDSException("价格策略最低成交价/折扣不能小于0！");
        }
       int n = stCOrderPriceItemMapper.selectCount(new QueryWrapper<StCOrderPriceItemDO>().lambda()
               .eq(StCOrderPriceItemDO::getStCPriceId,id)
               .eq(StCOrderPriceItemDO::getPolicyType,stCOrderPriceItemDO.getPolicyType()));
       if (n>0){
           throw new NDSException("每种价格策略类型仅允许新增一条数据！");
       }
    }

    private ValueHolder updatePrice(QuerySession session, JSONObject fixColumn, Long id) {
        ValueHolder valueHolder = new ValueHolder();
        //商品价格策略逻辑判断
        if (!selectCheckPriceStatus(id, valueHolder)) {
            return valueHolder;
        }
        JSONArray errorArray = new JSONArray();
        String price = fixColumn.getString(StConstant.TAB_ST_C_PRICE);
        int insertPrice = 1;//保存明细
        if (StringUtils.isNotEmpty(price)) {
            StCPriceDO stCPriceDO = JSON.parseObject(price, StCPriceDO.class);
            stCPriceDO.setId(id);

            StCPriceDO stCPriceDO_Old = stCPriceMapper.selectById(id);
            if (stCPriceDO.getBeginTime() == null) {
                stCPriceDO.setBeginTime(stCPriceDO_Old.getBeginTime());
            }
            if (stCPriceDO.getEndTime() == null) {
                stCPriceDO.setEndTime(stCPriceDO_Old.getEndTime());
            }
            if (stCPriceDO.getProRange() == null) {
                stCPriceDO.setProRange(stCPriceDO_Old.getProRange());
            }
            if (stCPriceDO.getPriceType() == null) {
                stCPriceDO.setPriceType(stCPriceDO_Old.getPriceType());
            }

            //检查
            if (!checkPrice(stCPriceDO, valueHolder)) {
                return valueHolder;
            }
            StBeanUtils.makeModifierField(stCPriceDO, session.getUser());//基本字段值设置-修改信息
            stCPriceDO.setModifierename(session.getUser().getEname());//修改人账号
            insertPrice = stCPriceMapper.updateById(stCPriceDO);
            /*删除redis缓存*/
            deleteCacheByCpShopIds(stCPriceDO.getCpCShopId());

            //商品价格策略店铺明细
            if (stCPriceDO.getCpCShopId() != null) {
                String ids = stCPriceDO.getCpCShopId();
                String[] lIds = ids.split(",");
                savePriceShop(session, stCPriceDO.getId(), lIds);
            }
        }
        if (insertPrice > 0) {
            savePriceItem(session, fixColumn, id, errorArray);
            //添加商品价格策略明细
            saveOrderPriceItem(session, fixColumn,id, errorArray);
            //添加排除商品明细
            savePriceExcludeItem(session, fixColumn, id, errorArray);
        } else {
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }
        updatePriceDate(session, id);
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

    private void savePriceItem(QuerySession session, JSONObject fixColumn, Long id, JSONArray errorArray) {
        /*表ST_C_PRICE_ITEM*/
        String priceItem = fixColumn.getString(StConstant.TAB_ST_C_PRICE_ITEM);
        if (StringUtils.isNotEmpty(priceItem)) {
            try {
                List<StCPriceItemDO> stCPriceItemDOList = JSON.parseArray(priceItem, StCPriceItemDO.class);
                if (stCPriceItemDOList.isEmpty() || stCPriceItemDOList.size() <= 0) {
                    //errorArray.add(StBeanUtils.getJsonObjectInfo(id, "商品价格策略-明细无数据！"));
                    //throw new NDSException("商品价格策略-明细无数据！");
                    return;
                }
                //商品价格明细保存
                for (StCPriceItemDO stCPriceItemDO : stCPriceItemDOList) {
                    //商品ID存在，更新当前商品ID信息
                    if (stCPriceItemDO.getPsCProId() != null && stCPriceItemDO.getPsCProId() > 0) {
                        List<StCPriceItemDO> stCPriceItemDOID = stCPriceItemMapper.selectItemByProIdAndPriceId(stCPriceItemDO.getPsCProId(), id);
                        if (!stCPriceItemDOID.isEmpty() && stCPriceItemDOID.size() > 0) {
                            //stCPriceItemDO.setId(stCPriceItemDOID.get(0).getId());
                            List<Long> stCPriceItemIds = stCPriceItemDOID
                                    .stream().map(StCPriceItemDO::getId).distinct().collect(Collectors.toList());
                            /*删掉原来的*/
                            stCPriceItemMapper.deleteBatchIds(stCPriceItemIds);
                        }
                    }
                    stCPriceItemDO.setModifierename(session.getUser().getEname());//修改人账号
                    getProPrice(stCPriceItemDO);//获取吊牌价信息
                    if (stCPriceItemDO.getId() != null && stCPriceItemDO.getId() > 0) {
                        //明细修改
                        StBeanUtils.makeModifierField(stCPriceItemDO, session.getUser());//修改信息
                        if ((stCPriceItemMapper.updateById(stCPriceItemDO)) <= 0) {
                            //errorArray.add(StBeanUtils.getJsonObjectInfo(stCPriceItemDO.getId(), "商品价格策略-明细修改失败！"));
                            throw new NDSException("商品价格策略-明细修改失败！");
                        }
                    } else {
                        //明细创建
                        stCPriceItemDO.setStCPriceId(id);
                        stCPriceItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRICE_ITEM));
                        StBeanUtils.makeCreateField(stCPriceItemDO, session.getUser());//创建信息
                        stCPriceItemDO.setOwnerename(session.getUser().getEname());//创建人账号
                        if ((stCPriceItemMapper.insert(stCPriceItemDO)) <= 0) {
                            //errorArray.add(StBeanUtils.getJsonObjectInfo(stCPriceItemDO.getId(), "商品价格策略-明细保存失败！"));
                            throw new NDSException("商品价格策略-明细保存失败！");
                        }
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("PriceSaveService.savePriceItem.Error{}"),
                        Throwables.getStackTraceAsString(ex));
                throw new NDSException("商品价格策略明细保存异常" + ex.getMessage());
            }
        }
    }

    /**
     * 保存排除商品明细
     * @param session
     * @param fixColumn
     * @param id
     * @param errorArray
     */
    private void savePriceExcludeItem(QuerySession session, JSONObject fixColumn, Long id, JSONArray errorArray) {
        String priceExcludeItem = fixColumn.getString(StConstant.TAB_ST_C_PRICE_EXCLUDE_ITEM);
        if (StringUtils.isNotEmpty(priceExcludeItem)) {
            try {
                List<StCPriceExcludeItemDO> stCPriceExcludeItemDOList = JSON.parseArray(priceExcludeItem, StCPriceExcludeItemDO.class);
                if (CollectionUtils.isEmpty(stCPriceExcludeItemDOList)) {
                    return;
                }
                //商品价格明细保存
                for (StCPriceExcludeItemDO stCPriceExcludeItemDO : stCPriceExcludeItemDOList) {
                    if(stCPriceExcludeItemDO.getId() != null && stCPriceExcludeItemDO.getId() > 0){
                        StBeanUtils.makeModifierField(stCPriceExcludeItemDO, session.getUser());//修改信息
                        stCPriceExcludeItemMapper.updateById(stCPriceExcludeItemDO);
                    }else {
                        stCPriceExcludeItemDO.setModifierename(session.getUser().getEname());//修改人账号
                        //明细创建
                        stCPriceExcludeItemDO.setStCPriceId(id);
                        stCPriceExcludeItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRICE_EXCLUDE_ITEM));
                        StBeanUtils.makeCreateField(stCPriceExcludeItemDO, session.getUser());//创建信息
                        stCPriceExcludeItemDO.setOwnerename(session.getUser().getEname());//创建人账号
                        if ((stCPriceExcludeItemMapper.insert(stCPriceExcludeItemDO)) <= 0) {
                            throw new NDSException("商品价格策略-排除商品明细保存失败！");
                        }
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("商品价格策略-排除商品明细保存失败！error={}",
                        "PriceSaveService"), Throwables.getStackTraceAsString(ex));
                throw new NDSException("商品价格策略排除商品明细保存异常" + ex.getMessage());
            }
        }
    }

    private void savePriceShop(QuerySession session, Long id, String[] shopIds) {
        //先删除
        HashMap<String, Object> map = new HashMap<>();
        map.put("ST_C_PRICE_ID", id);
        int delete = stCPriceShopMapper.deleteByMap(map);
        if (delete < 0) {
            throw new NDSException("商品价格策略-店铺明细插入失败！");
        }
        for (String shopid : shopIds) {
            if (shopid.isEmpty()) continue;
            StCPriceShopDO item = new StCPriceShopDO();
            /* 表 ST_C_PRICE_SHOP */
            item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRICE_SHOP));//主键
            item.setStCPriceId(id);//主表外键
            item.setCpCShopId(Long.valueOf(shopid));//店铺外键
            StBeanUtils.makeCreateField(item, session.getUser());
            item.setModifierename(session.getUser().getEname());//修改人账号
            item.setOwnerename(session.getUser().getEname());//创建人账号
            int insert = stCPriceShopMapper.insert(item);
            if (insert < 0) {
                throw new NDSException("商品价格策略-店铺明细插入失败！");
            }
        }
    }

    private void updatePriceDate(QuerySession session, Long id) {
        StCPriceDO stCPriceDO = new StCPriceDO();
        stCPriceDO.setId(id);
        StBeanUtils.makeModifierField(stCPriceDO, session.getUser());//修改信息
        if ((stCPriceMapper.updateById(stCPriceDO)) <= 0) {
            log.error(LogUtil.format("PriceSaveService.updatePriceDate.Error", "保存明细，主表修改字段信息更新出错id:", id));
        } else {
            /*删除redis缓存*/
            deleteCacheByCpShopIds(stCPriceDO.getCpCShopId());
        }
    }

    private boolean selectCheckPriceStatus(Long id, ValueHolder valueHolder) {
        StCPriceDO stCPriceDO = stCPriceMapper.selectById(id);
        if (stCPriceDO == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录已不存在！");
            return false;
        }
        //已作废
        if (stCPriceDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录早已作废，不允许编辑！");
            return false;
        }
        return true;
    }

    private boolean checkPrice(StCPriceDO stCPriceDO, ValueHolder valueHolder) {
        /*新增时单个校验，修改时仅能有一个店铺*/
        if (StringUtils.isNotEmpty(stCPriceDO.getCpCShopId()) && stCPriceDO.getCpCShopId().contains(",")) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "店铺不允许多个！");
            return false;
        }

        if (stCPriceDO.getEndTime() != null && stCPriceDO.getBeginTime() != null) {
            if (stCPriceDO.getBeginTime().before(new Date())) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "开始日期不能小于当前日期！");
                return false;
            }

            if (stCPriceDO.getEndTime().before(stCPriceDO.getBeginTime())) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "结束日期不能小于开始日期！");
                return false;
            }
        }
        // 如果商品范围不等于空 且=全部商品
        if(stCPriceDO.getProRange() != null && stCPriceDO.getProRange() == 1){
            if(stCPriceDO.getPriceType() == null){
                valueHolder.put("code", -1);
                valueHolder.put("message", "商品范围为全部商品，价格取值类型不能为空！");
                return false;
            }
        }
        if (stCPriceDO.getEname() != null) {
            List<StCPriceDO> stCPriceDOList = stCPriceMapper.selectPriceByEname(stCPriceDO.getEname());
            if (!stCPriceDOList.isEmpty() && stCPriceDOList.size() > 0) {
                if (Objects.isNull(stCPriceDO.getId()) || !stCPriceDO.getId().equals(stCPriceDOList.get(0).getId())) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "当前活动名称存在重复！");
                    return false;
                }
            }
        }
        return true;
    }

    private void getProPrice(StCPriceItemDO stCPriceItemDO) {
        //吊牌价
        if (stCPriceItemDO.getPriceList() == null && stCPriceItemDO.getPsCProId() != null) {
            PsCPro psCPro = rpcPsService.queryProByID(stCPriceItemDO.getPsCProId());
            // @20200813 bug#21295 商品价格策略保存时未校验商品款的有效性
            checkPsCProActive(psCPro);

            if (psCPro != null) {
                stCPriceItemDO.setPriceList(psCPro.getPricelist());
            }
        }
        //销售价
        if (stCPriceItemDO.getPriceSale() == null && stCPriceItemDO.getPsCProId() != null) {
            //待添加销售价赋值：根据商品编码带出
        }

    }

    /**
     * 校验有效性
     * @param psCPro
     */
    private void checkPsCProActive(PsCPro psCPro) {
        if (Objects.isNull(psCPro)) {
            throw new NDSException("商品款不存在");
        }

        if (!"Y".equalsIgnoreCase(psCPro.getIsactive())) {
            throw new NDSException("商品款已失效:" + psCPro.getId());
        }
    }

    private void checkPriceType(StCOrderPriceItemDO stCOrderPriceItemDO){
        String policyType = stCOrderPriceItemDO.getPolicyType();
        if(StConstant.POLICY_TYPE_02.equals(policyType)){
            BigDecimal qtyDiscount = stCOrderPriceItemDO.getQtyDiscount();
            if (qtyDiscount != null){
                if(BigDecimal.ONE.compareTo(qtyDiscount) < 0){
                    AssertUtils.logAndThrow("若填写折扣，比如3折请填写0.3");
                }
            }
        }
    }
}
