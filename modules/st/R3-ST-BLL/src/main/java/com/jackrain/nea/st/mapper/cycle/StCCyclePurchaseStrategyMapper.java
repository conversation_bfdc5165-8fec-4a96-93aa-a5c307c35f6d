package com.jackrain.nea.st.mapper.cycle;


import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.cycle.StCCyclePurchaseStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

@Mapper
public interface StCCyclePurchaseStrategyMapper extends ExtentionMapper<StCCyclePurchaseStrategy> {

    @Select("SELECT " +
            "COUNT(*) " +
            "FROM st_c_cycle_purchase_strategy " +
            "WHERE " +
            "((start_date>=#{beginTime,jdbcType=TIMESTAMP} AND start_date<=#{endTime,jdbcType=TIMESTAMP}) OR " +
            "(start_date<=#{beginTime,jdbcType=TIMESTAMP} AND end_date>=#{endTime,jdbcType=TIMESTAMP}) OR " +
            "(end_date>=#{beginTime,jdbcType=TIMESTAMP} AND end_date<=#{endTime,jdbcType=TIMESTAMP}) OR " +
            "(start_date>=#{beginTime,jdbcType=TIMESTAMP} AND end_date<=#{endTime,jdbcType=TIMESTAMP})) " +
            "AND id<> #{id} " +
            "AND `STATUS` IN (1,2) " +
            "AND isactive='Y' " +
            "AND ps_c_pro_id = #{proId}")
    int selectDateCross(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime,
                        @Param("id") Long id, @Param("proId") Long proId);
}