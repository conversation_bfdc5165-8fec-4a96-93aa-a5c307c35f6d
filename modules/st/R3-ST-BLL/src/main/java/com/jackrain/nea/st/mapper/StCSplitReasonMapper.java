package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.request.StCSplitReasonRequest;
import com.jackrain.nea.st.model.table.StCSplitReasonDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCSplitReasonMapper extends ExtentionMapper<StCSplitReasonDO> {

    @Select("SELECT\n" +
            "\tr.id,\n" +
            "\ti.SYSTEM_SPLIT_REASON,\n" +
            "\tr.SPLIT_REASON_CONFIG_ID,\n" +
            "\tg.CUSTOM_REASON \n" +
            "FROM\n" +
            "\tST_C_SPLIT_REASON r\n" +
            "\tJOIN ST_C_SPLIT_REASON_ITEM i ON r.ID = i.SPLIT_REASON_ID\n" +
            "\tJOIN st_c_split_reason_config g ON r.SPLIT_REASON_CONFIG_ID = g.id \n" +
            "WHERE\n" +
            "\ti.SYSTEM_SPLIT_REASON = #{systemSplitReason}")
    List<StCSplitReasonRequest> queryStCSplitReasonList(@Param("systemSplitReason")String systemSplitReason);

    @Select("SELECT\n" +
            "\tr.id,\n" +
            "\ti.SYSTEM_SPLIT_REASON,\n" +
            "\tr.SPLIT_REASON_CONFIG_ID,\n" +
            "\tg.CUSTOM_REASON \n" +
            "FROM\n" +
            "\tST_C_SPLIT_REASON r\n" +
            "\tJOIN ST_C_SPLIT_REASON_ITEM i ON r.ID = i.SPLIT_REASON_ID\n" +
            "\tJOIN st_c_split_reason_config g ON r.SPLIT_REASON_CONFIG_ID = g.id \n")
    List<StCSplitReasonRequest> queryStCSplitReasonAll();
}
