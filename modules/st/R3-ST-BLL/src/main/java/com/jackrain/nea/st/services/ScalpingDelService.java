package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCScalpingLogisticsMapper;
import com.jackrain.nea.st.mapper.StCScalpingMapper;
import com.jackrain.nea.st.mapper.StCScalpingReplaceProMapper;
import com.jackrain.nea.st.model.table.StCScalpingDO;
import com.jackrain.nea.st.model.table.StCScalpingLogisticsDO;
import com.jackrain.nea.st.model.table.StCScalpingReplaceProDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 删除业务逻辑
 *
 * <AUTHOR> 黄火县
 * @since : 2019-03-11
 * create at : 2019-03-11 10:00
 */

@Component
@Slf4j
@Transactional
public class ScalpingDelService extends CommandAdapter {
    @Autowired
    private StCScalpingMapper stCMainMapper;
    @Autowired
    private StCScalpingLogisticsMapper stCItemMapperA;
    @Autowired
    private StCScalpingReplaceProMapper stCItemMapperB;

    /**
     * 主子表删除
     *
     * @param querySession
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder holder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);

        if (param == null || param.size() == 0) {
            holder = ValueHolderUtils.getFailValueHolder("参数为空!");
            return holder;
        }

        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");

        //判断主表是否存在
        StCScalpingDO stCScalpingDO = stCMainMapper.selectById(objid);
        if (stCScalpingDO == null) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            return holder;
        }

        String isActive = stCScalpingDO.getIsactive();
        if (StringUtils.isNotBlank(isActive) && "N".equals(isActive)) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已作废，不允许重复作废！");
            return holder;
        }

        JSONObject tabitem = param.getJSONObject("tabitem");

        //判断是删除主表还是明细表单独删除
        if ("false".equals(isDel)) {
            JSONArray itemArrA = tabitem.getJSONArray("ST_C_SCALPING_LOGISTICS");
            JSONArray itemArrB = tabitem.getJSONArray("ST_C_SCALPING_REPLACE_PRO");
            //单独删除明细
            holder = delItem(itemArrA, itemArrB, objid, querySession, stCScalpingDO);
        } else {
            stCScalpingDO.setIsactive("N");
            StBeanUtils.makeModifierField(stCScalpingDO, querySession.getUser());
            stCMainMapper.updateById(stCScalpingDO);
            holder = ValueHolderUtils.getDeleteSuccessValueHolder();
        }
        return holder;
    }

    /**
     * 删除明细表
     *
     * @param itemA  子表数据A
     * @param itemB  子表数据B
     * @param mainId 主表id
     * @return 返回状态
     */
    public ValueHolder delItem(JSONArray itemA, JSONArray itemB, Long mainId, QuerySession querySession,
                               StCScalpingDO stCScalpingDO) {
        ValueHolder holder = new ValueHolder();
        int iSuc = 0;
        //明细表记录不存在，则提示：当前记录已不存在！
        if (itemA != null) {
            for (int i = 0; i < itemA.size(); i++) {
                Long itemId = itemA.getLong(i);
                StCScalpingLogisticsDO stCItemADO = stCItemMapperA.selectById(itemId);
                if (stCItemADO != null) {
                    if (stCItemMapperA.deleteById(itemId) > 0) {
                        iSuc += 1;
                    }
                }
            }
        }
        if (itemB != null) {
            for (int i = 0; i < itemB.size(); i++) {
                Long itemId = itemB.getLong(i);
                StCScalpingReplaceProDO stCItemBDO = stCItemMapperB.selectById(itemId);
                if (stCItemBDO != null) {
                    if (stCItemMapperB.deleteById(itemId) > 0) {
                        iSuc += 1;
                    }
                }
            }
        }
        if (iSuc > 0) {
            StBeanUtils.makeModifierField(stCScalpingDO, querySession.getUser());
            stCMainMapper.updateById(stCScalpingDO);
            holder = ValueHolderUtils.getDeleteSuccessValueHolder();
        } else {
            holder = ValueHolderUtils.getFailValueHolder("删除记录不存在！");
        }
        return holder;
    }


}
