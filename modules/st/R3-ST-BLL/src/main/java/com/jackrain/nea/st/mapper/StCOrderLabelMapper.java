package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkDO;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper
public interface StCOrderLabelMapper extends ExtentionMapper<StCOrderLabelDO> {

    @Select("SELECT * from ST_C_ORDER_LABEL WHERE FIND_IN_SET(#{val},CP_C_SHOP_ID) and STATUS ='2' and ISACTIVE ='Y'  ORDER BY creationdate desc")
    List<StCOrderLabelDO> selectByShopId(@Param("val") String val);


    @Select("SELECT\n" +
            "\ta.* \n" +
            "FROM\n" +
            "\tST_C_ORDER_LABEL a\n" +
            "\tLEFT JOIN st_c_order_label_shop_item b ON a.id = b.st_c_order_label_id \n" +
            "WHERE\n" +
            "  b.cp_c_shop_id = #{shopId}\n" +
            "\tAND\n" +
            "\ta.STATUS ='2' and a.ISACTIVE ='Y' and b.ISACTIVE ='Y'")
    List<StCOrderLabelDO> selectStCOrderLabelList(@Param("shopId") Long shopId);

}