package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCSendPlanDO;
import com.jackrain.nea.st.model.table.StCSendPlanItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Mapper
@Component
public interface StCSendPlanMapper extends ExtentionMapper<StCSendPlanDO> {
    @Select("select count(1) from st_c_send_plan where id<>#{id} and isactive='Y' and ename= #{ename} and ESTATUS != 3 and ESTATUS != 4")
    int chenkRepeatByEname(@Param("id") Long id,@Param("ename") String ename);
    /**
     * 店铺派单方案
     *
     * @param cpShopId  店铺ID
     * @param orderDate 当前日期
     * @return 方案集合
     */
    @Select("SELECT id FROM ST_C_SEND_PLAN WHERE cp_c_shop_id = #{cpShopId} and (BEGIN_TIME <= #{currentDate} "
            + "and END_TIME >= #{currentDate}) and isactive='Y' and estatus=2 ORDER BY rank,creationdate DESC")
    List<Long> querySendPlanList(@Param("cpShopId") Long cpShopId, @Param("currentDate") Date orderDate);

    /**
     * 店铺派单方案
     *
     * @param cpShopId  店铺ID
     * @return 方案集合
     */
    @Select("SELECT * FROM ST_C_SEND_PLAN WHERE cp_c_shop_id = #{cpShopId} "
            + "and isactive='Y' and estatus=2 ORDER BY rank,creationdate DESC")
    List<StCSendPlanDO> selectSendPlanListByShopId(@Param("cpShopId") Long cpShopId);

    /**
     * 查找方案下派单规则 根据优先级和创建时间排序
     *
     * @param sendPlanId 方案Id
     * @return List<Long>
     */
    @Select("SELECT st_c_send_rule_id FROM st_c_send_plan_item WHERE st_c_send_plan_id=#{sendPlanId} "
            + "AND isactive='Y' order by rank,creationdate DESC")
    List<Long> querySendPlanItemList(@Param("sendPlanId") Long sendPlanId);

    /**
     * 查找方案下派单规则 根据优先级和创建时间排序
     *
     * @param sendPlanId 方案Id
     * @return List<Long>
     */
    @Select("SELECT * FROM st_c_send_plan_item WHERE st_c_send_plan_id=#{sendPlanId} "
            + "AND isactive='Y' order by rank,creationdate DESC")
    List<StCSendPlanItemDO> selectSendPlanItemListByPlanId(@Param("sendPlanId") Long sendPlanId);

    /**
     * 根据优先级和创建时间排序
     *
     * @param sendPlanList 方案Id集合
     * @return List<Long>
     */
    @Select("<script> SELECT id FROM ST_C_SEND_PLAN WHERE ID in <foreach collection='sendPlanList' item='item' "
            + " open='(' separator=',' close=')'> #{item} </foreach> AND isactive='Y' ORDER BY rank,creationdate "
            + " DESC </script>")
    List<Long> querySendPlanByActiveList(@Param("sendPlanList") List<Long> sendPlanList);


    /**
     * 更新发货仓库发货数量
     *
     * @param warehouseId 发货仓库Id
     * @return Integer
     */
    @Update("update st_c_send_rule_warehouse_rate "
            + "SET qty_send=IFNULL(qty_send,0)+1 where cp_c_phy_warehouse_id=#{warehouseId} ")
    Integer updateRuleWarehouseRate(@Param("warehouseId") Long warehouseId);
}
