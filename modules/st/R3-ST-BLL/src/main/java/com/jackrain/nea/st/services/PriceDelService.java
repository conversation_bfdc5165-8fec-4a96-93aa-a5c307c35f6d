package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Descroption 商品价格策略-删除逻辑
 * <AUTHOR>
 * @Date 2019/3/11
 */
@Component
@Slf4j
@Transactional
public class PriceDelService extends CommandAdapter {
    @Autowired
    private StCPriceMapper stCPriceMapper;
    @Autowired
    private StCPriceItemMapper stCPriceItemMapper;

    @Autowired
    private StCPriceExcludeItemMapper stCPriceExcludeItemMapper;
    @Autowired
    private StCPriceShopMapper stCPriceShopMapper;
    @Autowired
    private StCOrderPriceItemMapper stCOrderPriceItemMapper;

    @Override
    @StOperationLog(operationType = "DEL",mainTableName = "ST_C_PRICE",itemsTableName = "ST_C_PRICE_EXCLUDE_ITEM")
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");
        if (id != null && id > 0) {
            ValueHolder valueHolder = new ValueHolder();
            StCPriceDO stCPriceDO = stCPriceMapper.selectById(id);
            if (!checkPriceStatus(stCPriceDO, valueHolder)) {
                return valueHolder;
            }
            if (delMainFlag) {
                List<StCPriceItemDO> stCPriceItemDOList = stCPriceItemMapper.selectItemByPriceId(id);
                List<StCPriceShopDO> stCPriceShopDOList = stCPriceShopMapper.selectShopByPriceId(id);
                List<StCOrderPriceItemDO> stCOrderPriceItemDOList = stCOrderPriceItemMapper.selectOrderItemByPriceId(id);
                if ((!stCPriceItemDOList.isEmpty() && stCPriceItemDOList.size() > 0) ||
                        (!stCPriceShopDOList.isEmpty() && stCPriceShopDOList.size() > 0 || (!stCOrderPriceItemDOList.isEmpty() && stCOrderPriceItemDOList.size()>0))) {
                    return ValueHolderUtils.getFailValueHolder("商品价格策略存在明细，不允许删除！");
                }
                if ((stCPriceMapper.deleteById(stCPriceDO)) > 0) {
                    Long shopId = StringUtils.isBlank(stCPriceDO.getCpCShopId()) ? null : Long.valueOf(stCPriceDO.getCpCShopId());
                    RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY);
                    RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY_INFO);
                    return ValueHolderUtils.getFailValueHolder("删除主表成功！");
                }
            } else {
                //单独删除明细
                JSONObject tabitem = param.getJSONObject("tabitem");
                JSONArray errorArray = new JSONArray();
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_PRICE_ITEM);
                JSONArray shopArray = tabitem.getJSONArray(StConstant.TAB_ST_C_PRICE_SHOP);
                JSONArray orderItemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_ORDER_PRICE_ITEM);
                JSONArray excludeItemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_PRICE_EXCLUDE_ITEM);
                if (itemArray != null && itemArray.size() > 0) {
                    deletePriceItemByPriceID(itemArray, querySession, errorArray);
                }
                if (shopArray != null && shopArray.size() > 0) {
                    deletePriceShopByPriceID(shopArray, querySession, errorArray);
                }
                if (orderItemArray != null && orderItemArray.size() > 0) {
                    deleteOrderPriceByPriceID(orderItemArray, querySession, errorArray);
                }
                if(excludeItemArray != null && excludeItemArray.size() > 0){
                    deletePriceExcludeItemByPriceID(excludeItemArray, querySession, errorArray);
                }
                updatePriceDate(querySession, id);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private void deleteOrderPriceByPriceID(JSONArray orderItemArray, QuerySession querySession, JSONArray errorArray) {
        if (orderItemArray.size() > 0) {
            for (int i = 0; i < orderItemArray.size(); i++) {
                Long itemid = orderItemArray.getLong(i);
                if ((stCOrderPriceItemMapper.deleteById(itemid)) <= 0) {
                    errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "商品价格策略-订单价格策略明细已不存在！"));
                }
            }
        }
    }

    private void deletePriceItemByPriceID(JSONArray itemArray, QuerySession session, JSONArray errorArray) {
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                if ((stCPriceItemMapper.deleteById(itemid)) <= 0) {
                    errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "商品价格策略-明细记录已不存在！"));
                }
            }
        }
    }

    private void deletePriceExcludeItemByPriceID(JSONArray itemArray, QuerySession session, JSONArray errorArray) {
        Map<Long,String> beforeDelObjMap = new HashMap<>();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                StCPriceExcludeItemDO delItem = stCPriceExcludeItemMapper.selectById(itemid);
                if (delItem != null) {
                    String delJsonStr = JSON.toJSONString(delItem);
                    beforeDelObjMap.put(itemid,delJsonStr);
                    if ((stCPriceExcludeItemMapper.deleteById(itemid)) <= 0) {
                        errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "商品价格策略-排除商品明细记录已不存在！"));
                    }
                }
            }
        }
        session.setAttribute("beforeDelObjMap",beforeDelObjMap);
    }

    private void deletePriceShopByPriceID(JSONArray itemArray, QuerySession session, JSONArray errorArray) {
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                if ((stCPriceShopMapper.deleteById(itemid)) <= 0) {
                    errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "商品价格策略-店铺记录已不存在！"));
                }
            }
        }
    }

    private void updatePriceDate(QuerySession session, Long id) {
        StCPriceDO stCPriceDO = new StCPriceDO();
        stCPriceDO.setId(id);
        StBeanUtils.makeModifierField(stCPriceDO, session.getUser());//修改信息
        stCPriceDO.setModifierename(session.getUser().getEname());//修改人账号
        if ((stCPriceMapper.updateById(stCPriceDO)) <= 0) {
            log.error(LogUtil.format("PriceDelService.updatePriceDate.Error", "删除明细，主表修改字段信息更新出错id:", id));
        }else {
            Long shopId = StringUtils.isBlank(stCPriceDO.getCpCShopId()) ? null : Long.valueOf(stCPriceDO.getCpCShopId());
            RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY);
            RedisCacheUtil.delete(shopId, RedisConstant.SHOP_PRICE_STRATEGY_INFO);

        }
    }

    private boolean checkPriceStatus(StCPriceDO stCPriceDO, ValueHolder valueHolder) {
        if (stCPriceDO == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不存在！");
            return false;
        }
        if (stCPriceDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不存在！");
            return false;
        }
        return true;
    }
}
