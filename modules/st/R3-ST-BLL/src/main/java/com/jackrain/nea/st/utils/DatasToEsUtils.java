package com.jackrain.nea.st.utils;

import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.st.common.EsConstant;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: tianqinhua
 * @since: 2019-05-20
 * create at : 2019-05-20 16:27
 */
@Slf4j
public class DatasToEsUtils {
    //==================================店铺商品特殊设置ES========================================================//
    private static Boolean createProductEsIndex() {
        Boolean isExists = false;
        if (!ElasticSearchUtil.indexExists(EsConstant.ST_C_PRODUCT_STRATEGY_INDEX)) {
            try {
                //建索引
                List<Class> childs = new ArrayList<>();
                childs.add(StCProductStrategyItemDO.class);
                ElasticSearchUtil.indexCreate(childs, StCProductStrategyDO.class);
                isExists = true;
            } catch (IOException e) {
                log.debug(LogUtil.format("创建索引 失败！{}") , Throwables.getStackTraceAsString(e));
            }
        } else {
            isExists = true;
        }
        return isExists;
    }

    public static void insertProductEsData(StCProductStrategyDO stCProductStrategyDO, List<StCProductStrategyItemDO> stCProductStrategyItemDOs, String table) throws IOException {
        // 创建索引
        createProductEsIndex();
        switch (table.toUpperCase()) {
            case StConstant.TAB_ST_C_PRODUCT_STRATEGY:
                // 主表
                if (stCProductStrategyDO != null) {
                    ElasticSearchUtil.indexDocument(EsConstant.ST_C_PRODUCT_STRATEGY_INDEX, EsConstant.ST_C_PRODUCT_STRATEGY_TYPE, stCProductStrategyDO, stCProductStrategyDO.getId());
                }
                break;
            case StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM:
                // 店铺商品特殊设置从表
                if (CollectionUtils.isNotEmpty(stCProductStrategyItemDOs)) {
                    ElasticSearchUtil.indexDocuments(EsConstant.ST_C_PRODUCT_STRATEGY_INDEX, EsConstant.ST_C_PRODUCT_STRATEGY_ITEM_TYPE, stCProductStrategyItemDOs, EsConstant.ST_C_PRODUCT_STRATEGY_ITEM_PID);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 批量推送 商品特殊设置策略明细到ES
     *
     * @param stCProductStrategyDO 策略信息
     * @param itemList             明细集合
     * @throws IOException
     */
    public static void batchInsertProductEsData(StCProductStrategyDO stCProductStrategyDO,
                                                List<StCProductStrategyItemDO> itemList) throws IOException {
        // 创建索引
        createProductEsIndex();
        // 主表
        if (stCProductStrategyDO != null) {
            ElasticSearchUtil.indexDocument(EsConstant.ST_C_PRODUCT_STRATEGY_INDEX, EsConstant.ST_C_PRODUCT_STRATEGY_TYPE, stCProductStrategyDO, stCProductStrategyDO.getId());
        }
        if (CollectionUtils.isNotEmpty(itemList)) {
            ElasticSearchUtil.indexDocuments(EsConstant.ST_C_PRODUCT_STRATEGY_INDEX,
                    EsConstant.ST_C_PRODUCT_STRATEGY_ITEM_TYPE,
                    itemList, EsConstant.ST_C_PRODUCT_STRATEGY_ITEM_PID);
        }
    }

    public static void deleteProductEsData(StCProductStrategyDO stCProductStrategyDO, StCProductStrategyItemDO stCProductStrategyItemDO, String table) throws IOException {
        // 创建索引
        createProductEsIndex();
        switch (table.toUpperCase()) {
            case StConstant.TAB_ST_C_PRODUCT_STRATEGY:
                // 主表
                if (stCProductStrategyDO != null) {
                    ElasticSearchUtil.delDocument(EsConstant.ST_C_PRODUCT_STRATEGY_INDEX, EsConstant.ST_C_PRODUCT_STRATEGY_TYPE, stCProductStrategyDO.getId());
                }
                break;
            case StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM:
                // 从表
                if (stCProductStrategyItemDO != null) {
                    ElasticSearchUtil.delDocument(EsConstant.ST_C_PRODUCT_STRATEGY_INDEX, EsConstant.ST_C_PRODUCT_STRATEGY_ITEM_TYPE, stCProductStrategyItemDO.getId(), stCProductStrategyDO.getId());
                }
                break;
            default:
                break;
        }
    }

    //==================================店铺库存锁库策略ES========================================================//
    private static Boolean createLoclStockEsIndex() {
        Boolean isExists = false;
        if (!ElasticSearchUtil.indexExists(EsConstant.ST_C_LOCK_STOCK_STRATEGY_INDEX)) {
            try {
                //建索引
                List<Class> childs = new ArrayList<>();
                childs.add(StCLockStockStrategyItemDO.class);
                ElasticSearchUtil.indexCreate(childs, StCLockStockStrategyDO.class);
                isExists = true;
            } catch (IOException e) {
                log.debug(LogUtil.format("创建索引 失败！{}"), Throwables.getStackTraceAsString(e));
            }
        } else {
            isExists = true;
        }
        return isExists;
    }

    public static void insertLoclStockEsData(StCLockStockStrategyDO stCLockStockStrategyDO, List<StCLockStockStrategyItemDO> stCLockStockStrategyItemDOs, String table) throws IOException {
        // 创建索引
        createLoclStockEsIndex();
        switch (table.toUpperCase()) {
            case StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY:
                // 主表
                if (stCLockStockStrategyDO != null) {
                    ElasticSearchUtil.indexDocument(EsConstant.ST_C_LOCK_STOCK_STRATEGY_INDEX, EsConstant.ST_C_LOCK_STOCK_STRATEGY_TYPE, stCLockStockStrategyDO, stCLockStockStrategyDO.getId());
                }
                break;
            case StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY_ITEM:
                // 店铺库存锁库策略
                if (CollectionUtils.isNotEmpty(stCLockStockStrategyItemDOs)) {
                    ElasticSearchUtil.indexDocuments(EsConstant.ST_C_LOCK_STOCK_STRATEGY_INDEX, EsConstant.ST_C_LOCK_STOCK_STRATEGY_ITEM_TYPE, stCLockStockStrategyItemDOs, EsConstant.ST_C_LOCK_STOCK_STRATEGY_ITEM_PID);
                }
                break;
            default:
                break;
        }
    }

    public static void batchInsertLockStockEsData(StCLockStockStrategyDO stCLockStockStrategyDO, List<StCLockStockStrategyItemDO> itemDOList, String table) throws IOException {
        // 创建索引
        createLoclStockEsIndex();
        // 主表
        if (stCLockStockStrategyDO != null) {
            ElasticSearchUtil.indexDocument(EsConstant.ST_C_LOCK_STOCK_STRATEGY_INDEX, EsConstant.ST_C_LOCK_STOCK_STRATEGY_TYPE, stCLockStockStrategyDO, stCLockStockStrategyDO.getId());
        }
        // 店铺库存锁库策略
        if (!CollectionUtils.isEmpty(itemDOList)) {
            ElasticSearchUtil.indexDocuments(EsConstant.ST_C_LOCK_STOCK_STRATEGY_INDEX, EsConstant.ST_C_LOCK_STOCK_STRATEGY_ITEM_TYPE, itemDOList, "ST_C_LOCK_STOCK_STRATEGY_ID");
        }
    }

    public static void deleteLoclStockEsData(StCLockStockStrategyDO stCLockStockStrategyDO, StCLockStockStrategyItemDO stCLockStockStrategyItemDO, String table) throws IOException {
        // 创建索引
        createLoclStockEsIndex();
        switch (table.toUpperCase()) {
            case StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY:
                // 主表
                if (stCLockStockStrategyDO != null) {
                    ElasticSearchUtil.delDocument(EsConstant.ST_C_LOCK_STOCK_STRATEGY_INDEX, EsConstant.ST_C_LOCK_STOCK_STRATEGY_TYPE, stCLockStockStrategyDO.getId());
                }
                break;
            case StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY_ITEM:
                // 店铺库存锁库策略明细删除
                if (stCLockStockStrategyItemDO != null) {
                    ElasticSearchUtil.delDocument(EsConstant.ST_C_LOCK_STOCK_STRATEGY_INDEX, EsConstant.ST_C_LOCK_STOCK_STRATEGY_ITEM_TYPE, stCLockStockStrategyItemDO.getId(), stCLockStockStrategyDO.getId());
                }
                break;
            default:
                break;
        }
    }

    //==================================店铺锁库条码特殊设置ES========================================================//
    private static Boolean createLoclSkuEsIndex() {
        Boolean isExists = false;
        if (!ElasticSearchUtil.indexExists(EsConstant.ST_C_LOCK_SKU_STRATEGY_INDEX)) {
            try {
                //建索引
                List<Class> childs = new ArrayList<>();
                childs.add(StCLockSkuStrategyItemDO.class);
                ElasticSearchUtil.indexCreate(childs, StCLockSkuStrategyDO.class);
                isExists = true;
            } catch (IOException e) {
                log.debug(LogUtil.format("创建索引 失败！{}"), Throwables.getStackTraceAsString(e));
            }
        } else {
            isExists = true;
        }
        return isExists;
    }

    public static void insertLoclSkuEsData(StCLockSkuStrategyDO stCLockSkuStrategyDO, List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOs, String table) throws IOException {
        // 创建索引
        createLoclSkuEsIndex();
        switch (table.toUpperCase()) {
            case StConstant.TAB_ST_C_LOCK_SKU_STRATEGY:
                // 主表
                if (stCLockSkuStrategyDO != null) {
                    ElasticSearchUtil.indexDocument(EsConstant.ST_C_LOCK_SKU_STRATEGY_INDEX, EsConstant.ST_C_LOCK_SKU_STRATEGY_TYPE, stCLockSkuStrategyDO, stCLockSkuStrategyDO.getId());
                }
                break;
            case StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM:
                // 店铺库存锁库策略
                if (CollectionUtils.isNotEmpty(stCLockSkuStrategyItemDOs)) {
                    ElasticSearchUtil.indexDocuments(EsConstant.ST_C_LOCK_SKU_STRATEGY_INDEX, EsConstant.ST_C_LOCK_SKU_STRATEGY_ITEM_TYPE, stCLockSkuStrategyItemDOs, EsConstant.ST_C_LOCK_SKU_STRATEGY_PID);
                }
                break;
            default:
                break;
        }
    }

    public static void deleteLoclSkuEsData(StCLockSkuStrategyDO stCLockSkuStrategyDO, StCLockSkuStrategyItemDO stCLockSkuStrategyItemDO, String table) throws IOException {
        // 创建索引
        createLoclSkuEsIndex();
        switch (table.toUpperCase()) {
            case StConstant.TAB_ST_C_LOCK_SKU_STRATEGY:
                // 主表
                if (stCLockSkuStrategyDO != null) {
                    ElasticSearchUtil.delDocument(EsConstant.ST_C_LOCK_SKU_STRATEGY_INDEX, EsConstant.ST_C_LOCK_SKU_STRATEGY_TYPE, stCLockSkuStrategyDO.getId());
                }
                break;
            case StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM:
                // 明细删除
                if (stCLockSkuStrategyItemDO != null) {
                    ElasticSearchUtil.delDocument(EsConstant.ST_C_LOCK_SKU_STRATEGY_INDEX, EsConstant.ST_C_LOCK_SKU_STRATEGY_ITEM_TYPE, stCLockSkuStrategyItemDO.getId(), stCLockSkuStrategyDO.getId());
                }
                break;
            default:
                break;
        }
    }


    /**
     * 店铺商品虚高库存设置es
     */
    private static Boolean createVirtualHighStockEsIndex() {
        Boolean isExists = false;
        if (!ElasticSearchUtil.indexExists(EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_INDEX)) {
            try {
                //建索引
                List<Class> childs = new ArrayList<>();
                childs.add(StCShopVirtualHighStockItemDO.class);
                ElasticSearchUtil.indexCreate(childs, StCShopVirtualHighStockDO.class);
                isExists = true;
            } catch (IOException e) {
                log.debug(LogUtil.format("创建索引失败！{}"), Throwables.getStackTraceAsString(e));
            }
        } else {
            isExists = true;
        }
        return isExists;
    }

    public static void batchInsertVirtualHighStockEsData(StCShopVirtualHighStockDO virtualHighStockDO, List<StCShopVirtualHighStockItemDO> itemDOList, String table) throws IOException {
        // 创建索引
        createVirtualHighStockEsIndex();
        // 主表
        if (virtualHighStockDO != null) {
            ElasticSearchUtil.indexDocument(EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_INDEX, EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_TYPE, virtualHighStockDO, virtualHighStockDO.getId());
        }

        if (!CollectionUtils.isEmpty(itemDOList)) {
            ElasticSearchUtil.indexDocuments(EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_INDEX, EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM_TYPE, itemDOList, "ST_C_SHOP_VIRTUAL_HIGH_STOCK_ID");
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("店铺商品虚高库存明细表推数据到ES成功："));
        }
    }

    public static void insertVirtualHighStockEsData(StCShopVirtualHighStockDO virtualHighStockDO, List<StCShopVirtualHighStockItemDO> itemDOs, String table) throws IOException {
        // 创建索引
        createVirtualHighStockEsIndex();
        switch (table.toUpperCase()) {
            case StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK:
                // 主表
                if (virtualHighStockDO != null) {
                    ElasticSearchUtil.indexDocument(EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_INDEX, EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_TYPE, virtualHighStockDO, virtualHighStockDO.getId());
                }
                break;
            case StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM:
                // 明细删除
                if (CollectionUtils.isNotEmpty(itemDOs)) {
                    ElasticSearchUtil.indexDocuments(EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_INDEX, EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM_TYPE, itemDOs, EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM_TYPE_PID);
                }
                break;
            default:
                break;
        }
    }

    public static void deleteVirtualHighStockEsData(StCShopVirtualHighStockDO virtualHighStockDO, StCShopVirtualHighStockItemDO virtualHighStockItemDO, String table) throws IOException {
        // 创建索引
        createVirtualHighStockEsIndex();
        switch (table.toUpperCase()) {
            case StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK:
                // 主表
                if (virtualHighStockDO != null) {
                    ElasticSearchUtil.delDocument(EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_INDEX, EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_TYPE, virtualHighStockDO.getId());
                }
                break;
            case StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM:
                // 明细删除
                if (virtualHighStockItemDO != null) {
                    ElasticSearchUtil.delDocument(EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_INDEX, EsConstant.ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM_TYPE, virtualHighStockItemDO.getId(), virtualHighStockDO.getId());
                }
                break;
            default:
                break;
        }
    }


    public static <T extends SubBaseModel> void pushESBModelByUpdate(T sourceModel, List sourceModels, Long objid, JSONArray ids,
                                                                     String index, String type, String childType, String parentKey,
                                                                     Class a, Class b, boolean isDeletedItems) {
        try {
            // 判断索引是否存在
            if (!ElasticSearchUtil.indexExists(index)) {
                ElasticSearchUtil.indexCreate(b, a);
            }
            //删除明细记录
            if (isDeletedItems) {
                for (Object id : ids) {
                    ElasticSearchUtil.delDocument(index, childType, id, objid);
                }
            }

            if (sourceModel != null) {
                ElasticSearchUtil.indexDocument(index, type, sourceModel, objid);
            }

            if (CollectionUtils.isNotEmpty(sourceModels)) {
                ElasticSearchUtil.indexDocuments(index, childType, sourceModels, parentKey.toUpperCase());
            }

        } catch (Exception e) {
            log.error(LogUtil.format("push es error,error={},index/objid=", index, objid),
                    Throwables.getStackTraceAsString(e));
        }
    }

}
