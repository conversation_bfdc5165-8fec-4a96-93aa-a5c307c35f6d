package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCExpressPackageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface StCExpressPackageMapper extends ExtentionMapper<StCExpressPackageDO> {

    /**
     *  根据物流方案id，查询物流方案包裹明细
     * @param stCExpressId
     * @return List<StCExpressPackage>
     */
    @Select("SELECT * FROM ST_C_EXPRESS_PACKAGE WHERE ST_C_EXPRESS_ID=#{stCExpressId} AND ISACTIVE='Y'")
    List<StCExpressPackageDO> selectStCExpressPackageInfo(@Param("stCExpressId") Long stCExpressId);

    @UpdateProvider(type = StCExpressPackageMapper.StCExpressPackageAttribute.class, method = "updateSql")
    int updateAtrributes(JSONObject entity);

    class  StCExpressPackageAttribute {

        public String updateSql(JSONObject entity) {
            return new SQL() {
                {
                    UPDATE("ST_C_EXPRESS_PACKAGE");
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }
}