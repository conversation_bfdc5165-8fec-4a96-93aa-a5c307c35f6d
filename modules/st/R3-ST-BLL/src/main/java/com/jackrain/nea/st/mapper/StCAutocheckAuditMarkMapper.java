package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCAutocheckAuditMarkDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
public interface StCAutocheckAuditMarkMapper extends ExtentionMapper<StCAutocheckAuditMarkDO> {

    /**
     * 根据订单审核策略ID获取标识审核等待时间列表
     * @param sTAutocheckId
     * @return List<StCAutocheckAuditMarkDO>
     */
    @Select("SELECT ORDER_TAG_ENAME,MARK_WAIT_TIME FROM ST_C_AUTOCHECK_AUDIT_MARK WHERE ST_C_AUTOCHECK_ID=#{sTAutocheckId} AND ISACTIVE='Y'")
    List<StCAutocheckAuditMarkDO> selectCStAutditMarkItemInfo(@Param("sTAutocheckId") Long sTAutocheckId);

}