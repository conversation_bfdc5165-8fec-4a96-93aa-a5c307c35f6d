package com.jackrain.nea.st.services.cycle;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.cycle.StCCyclePurchaseStrategyMapper;
import com.jackrain.nea.st.model.table.cycle.StCCyclePurchaseStrategy;
import com.jackrain.nea.st.utils.StR3ParamUtils;
import com.jackrain.nea.st.utils.StRedisLockUtils;
import com.jackrain.nea.st.utils.UserUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import utils.AssertUtils;

import java.util.Date;
import java.util.Objects;

/**
 * @Auther: chenhao
 * @Date: 2022-09-05 10:48
 * @Description:
 */

@Slf4j
@Component
public class StCCyclePurchaseStrategyForceCompletionService {

    @Autowired
    private StCCyclePurchaseStrategyMapper mapper;

    /**
     * 周期购结案（页面）
     *
     * @param session session
     * @return return
     */
    public ValueHolder forceCompletion(QuerySession session) {
        SgR3BaseRequest request = StR3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        StCCyclePurchaseStrategyForceCompletionService service = ApplicationContextHandle.getBean(StCCyclePurchaseStrategyForceCompletionService.class);
        return StR3ParamUtils.convertV14WithResult(service.forceCompletion(request));
    }

    /**
     * 周期购结案
     *
     * @param request 入参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> forceCompletion(SgR3BaseRequest request) {

        log.info(LogUtil.format("Start StCCyclePurchaseStrategyForceCompletionService.forceCompletion:param={}",
                "StCCyclePurchaseStrategyForceCompletionService"), JSONObject.toJSONString(request));

        String lockKey = StConstant.ST_C_CYCLE_PURCHASE_STRATEGY + ":" + request.getObjId();
        StCCyclePurchaseStrategy stCyclePurchaseStrategy = checkParams(request);
        StRedisLockUtils.lock(lockKey);
        try {

            StCCyclePurchaseStrategy update = new StCCyclePurchaseStrategy();
            UserUtils.setModelDefalutDataByUpdate(update, request.getLoginUser());
            update.setStatus(StConstant.CYCLE_PURCHASE_STRATEGY_STATUS_CLOSER);
            //结案状态 暂时无用，用 status
            update.setCloserStatus(StConstant.CYCLE_PURCHASE_STRATEGY_STATUS_CLOSER);
            User user = request.getLoginUser();
            update.setCloserId(user.getId().longValue());
            update.setCloserTime(new Date());
            update.setId(stCyclePurchaseStrategy.getId());
            mapper.updateById(update);

            String redisKey = "st:cyclePurchaseStrategy:proCode:" + stCyclePurchaseStrategy.getPsCProEcode();

            if (Boolean.TRUE.equals(RedisOpsUtil.getStrRedisTemplate().hasKey(redisKey))) {
                RedisOpsUtil.getStrRedisTemplate().delete(redisKey);
            }
            String redisKeyById = "st:cyclePurchaseStrategy:id:" + stCyclePurchaseStrategy.getId();

            if (Boolean.TRUE.equals(RedisOpsUtil.getStrRedisTemplate().hasKey(redisKeyById))) {
                RedisOpsUtil.getStrRedisTemplate().delete(redisKeyById);
            }

        } catch (Exception e) {
            log.error(LogUtil.format("exception_has_occured:{}", "exception_has_occured"), Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrowException("周期购结案异常", e, request.getLoginUser().getLocale());
        } finally {
            StRedisLockUtils.unlock(lockKey, log, this.getClass().getName());
        }

        SgR3BaseResult baseResult = new SgR3BaseResult();
        baseResult.setDataJo(request.getObjId(), StConstant.ST_C_CYCLE_PURCHASE_STRATEGY);
        return new ValueHolderV14<>(baseResult, ResultCode.SUCCESS, "周期购结案成功！");
    }

    /**
     * 周期购作废前check
     *
     * @param request 入参
     */
    private StCCyclePurchaseStrategy checkParams(SgR3BaseRequest request) {
        AssertUtils.cannot(Objects.isNull(request.getObjId()), "入参为空！！");
        StCCyclePurchaseStrategy stCyclePurchaseStrategy = mapper.selectById(request.getObjId());
        AssertUtils.cannot(Objects.isNull(stCyclePurchaseStrategy), "当前记录已不存在！");
        Integer status = stCyclePurchaseStrategy.getStatus();
        AssertUtils.notNull(status, "策略状态值异常：null");
        if (!StConstant.CYCLE_PURCHASE_STRATEGY_STATUS_SUBMIT.equals(status)) {
            AssertUtils.logAndThrow("当前策略的状态不是已审核，不允许结案！");
        }

        return stCyclePurchaseStrategy;
    }
}
