package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCScalpingLogisticsDO;
import com.jackrain.nea.st.model.table.StCScalpingReplaceProDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface StCScalpingReplaceProMapper extends ExtentionMapper<StCScalpingReplaceProDO> {
    @Select("select * from st_c_scalping_replace_pro where st_c_scalping_id = #{mainid}")
    List<StCScalpingReplaceProDO> listByMainid(@Param("mainid") Long mainid);
}