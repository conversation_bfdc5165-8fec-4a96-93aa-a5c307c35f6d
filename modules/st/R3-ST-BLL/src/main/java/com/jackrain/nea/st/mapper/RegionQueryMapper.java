package com.jackrain.nea.st.mapper;

import com.jackrain.nea.st.model.table.CpCRegion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @ClassName RegionQueryMapper
 * @Description 区域查询
 * <AUTHOR>
 * @Date 2025/2/26 14:52
 * @Version 1.0
 */
@Mapper
public interface RegionQueryMapper {

    @Select("select * from CP_C_REGION where ID=#{id}")
    CpCRegion selectById(@Param("id") Long id);
}
