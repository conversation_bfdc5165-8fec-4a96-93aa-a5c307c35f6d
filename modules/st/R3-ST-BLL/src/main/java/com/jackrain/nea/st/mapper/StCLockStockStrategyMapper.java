package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.result.LockStockStrategyResult;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface StCLockStockStrategyMapper extends ExtentionMapper<com.jackrain.nea.st.model.table.StCLockStockStrategyDO> {
    @Update("UPDATE ST_C_LOCK_STOCK_STRATEGY SET PLAN_ID=#{sequence} WHERE id=#{id}")
    void updateSequence(@Param("sequence") String sequence, @Param("id") Long id);

    @UpdateProvider(type = StCLockStockStrategyMapper.StCLockStockStrategyAttribute.class, method = "updateSql")
    int updateAtrributes(JSONObject entity);

    class StCLockStockStrategyAttribute {

        public String updateSql(JSONObject entity) {
            return new SQL() {
                {
                    UPDATE("st_c_lock_stock_strategy");
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }


    /**
     * 根据状态获取锁库策略和明细
     *
     * @param status
     * @return
     */
    @Select("select id,name from st_c_lock_stock_strategy where status = #{status} and is_active =1 ")
    @Results({
            @Result(property = "items", column = "id",
                    many = @Many(select = "com.jackrain.nea.st.mapper.StCLockStockStrategyItemMapper.listByItemId"))
    })
    List<LockStockStrategyResult> getLockStock(Integer status);
}