package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCLockSkuStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface StCLockSkuStrategyMapper extends ExtentionMapper<StCLockSkuStrategyDO> {

    @Select("SELECT COUNT(*) FROM ST_C_LOCK_SKU_STRATEGY WHERE CP_C_SHOP_ID = #{cpCShopId} " +
            "AND (ESTATUS = 1 OR ESTATUS = 2)")
    int selectStCLockSkuStrategyByCpCShopId(@Param("cpCShopId")Long cpCShopId);

    @UpdateProvider(type = StCLockSkuStrategyAttribute.class, method = "updateSql")
    int updateAtrributes(JSONObject entity);

    class StCLockSkuStrategyAttribute {

        public String insertSql(JSONObject entity) {
            return new SQL() {
                {
                    INSERT_INTO("st_c_lock_sku_strategy");
                    for (String key : entity.keySet()) {
                        if (entity.get(key) != null) {
                            VALUES(key, "#{" + key + "}");
                        }
                    }
                }
            }.toString();
        }

        public String updateSql(JSONObject entity) {
            return new SQL() {
                {
                    UPDATE("st_c_lock_sku_strategy");
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }

    /**
     * <ul>
     *     <li>
     *         查询所有正在生效锁库策略
     *     </li>
     * </ul>
     * @return 返回所有生效的店铺锁库策略
     */
    @Select("SELECT * FROM st_c_lock_sku_strategy where sync_end_mark = 0 and  ESTATUS = 2 and isActive = 'Y' and lock_etime <= current_timestamp")
    List<StCLockSkuStrategyDO>  selectEndActiveStrategy();

    @Update("<script>" +
            "update st_c_lock_sku_strategy set sync_end_mark = #{code},sync_complete_mark = #{code} where ID in " +
            "<foreach collection= 'list'  item= 'item' separator= ',' open= '(' close= ')'> " +
            " #{item}" +
            "</foreach> " +
            "</script>")
    void updateSyncEndCompleteMarkByIds(@Param("code") Integer code, @Param("list") List<Long> idList);

    @Select("SELECT * FROM st_c_lock_sku_strategy where sync_complete_mark = 0 and  ESTATUS = 4 and isActive = 'Y' and finishtime > lock_btime and finishtime <= lock_etime")
    List<StCLockSkuStrategyDO>  selectCompleteActiveStrategy();

    @Update("<script>" +
            "update st_c_lock_sku_strategy set sync_complete_mark = #{code} where ID in " +
            "<foreach collection= 'list'  item= 'item' separator= ',' open= '(' close= ')'> " +
            " #{item}" +
            "</foreach> " +
            "</script>")
    void updateSyncStartCompleteMarkByIds(@Param("code") Integer code, @Param("list") List<Long> idList);
}