package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPriceShopDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;


@Mapper
public interface StCPriceShopMapper extends ExtentionMapper<StCPriceShopDO> {
    @Select("select * from st_c_price_shop where st_c_price_id = #{id}")
    List<StCPriceShopDO> selectShopByPriceId(@Param("id") Long id);

    @Select("select * from st_c_price_shop where cp_c_shop_id = #{shopid} and st_c_price_id = #{priceid}")
    List<StCPriceShopDO> selectShopByShopIdAndPriceid(@Param("shopid") Long shopid, @Param("priceid") Long priceid);

    @Delete("delete from st_c_price_shop where st_c_price_id = #{priceid}")
    int deletePriceShopByPriceidId(@Param("priceid") Long priceid);
}