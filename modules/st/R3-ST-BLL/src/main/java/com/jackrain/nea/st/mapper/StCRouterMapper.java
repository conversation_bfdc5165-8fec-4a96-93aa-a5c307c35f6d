package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCRouterCodeDO;
import com.jackrain.nea.st.model.table.StCRouterDO;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

@Mapper
public interface StCRouterMapper extends ExtentionMapper<StCRouterDO> {
    @Select("SELECT COUNT(*) FROM st_c_router WHERE id <> #{id} AND sys_type = #{sysType} AND isactive='Y'")
    int selectCountBySysTypeAndId(@Param("id") Long id, @Param("sysType") String sysType);

    @Select("SELECT COUNT(*) FROM st_c_router WHERE id <> #{id} AND isactive='Y' AND ENAME = #{ename} ")
    int selectCountByEnameAndId(@Param("id") Long id, @Param("ename") String ename);

    @Update("UPDATE st_c_router_code SET isactive=#{isactive}, " +
            "MODIFIERID = #{modifierid}, " +
            "MODIFIERENAME = #{modifierename}, " +
            "MODIFIEDDATE = #{modifieddate}" +
            "WHERE st_c_router_id = #{masterId}")
    int updateByMasterId(@Param("isactive") String isactive,
                         @Param("modifierid") Long modifierid,
                         @Param("modifierename") String modifierename,
                         @Param("modifieddate") Date modifieddate,
                         @Param("masterId") Long masterId);

    @Delete("DELETE FROM st_c_router_code WHERE st_c_router_id = #{masterId}")
    int deleteByMasterId(@Param("masterId") Long masterId);
}