package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressMapper;
import com.jackrain.nea.st.model.table.StCExpressDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Descroption 物流方案延期
 * @Author: 汪聿森
 * @Date: 2019/3/25 9:54
 */
@Component
@Slf4j
public class ExpressDelayService {
    @Autowired
    private StCExpressMapper mapper;

    /**
     * 延期设置
     *
     * @param obj  入参
     * @param user 用户
     * @return 结果
     */
    public ValueHolderV14 expressDelay(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();

        if (null == obj) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不能为空！");
            return vh;
        }

        //获取ID
        JSONArray idsArray = obj.getJSONArray("ids");
        Date date = obj.getDate("delayDate");

        if (idsArray != null && idsArray.size() > 0) {

            List<StCExpressDO> expressDOList = mapper.
                    selectBatchIds(JSONObject.parseArray(idsArray.toJSONString(), Long.class));

            if (checkStCExpressDO(user, vh, date, expressDOList)) {
                return vh;
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("单据ID为空，传参失败！");
            return vh;
        }

        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("方案延期成功！");
        return vh;
    }

    private boolean checkStCExpressDO(User user, ValueHolderV14 vh, Date date, List<StCExpressDO> expressDOList) {
        for (StCExpressDO expressDO : expressDOList) {

            if (!StConstant.CON_BILL_STATUS_02.equals(expressDO.getBillStatus())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("当前记录不是已审核，不允许延期！");
                return true;
            }

            if (date != null) {
                if (date.before(new Date())) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("结束时间不能早于当前时间！");
                    return true;
                }
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("请选择延期结束日期！");
                return true;
            }

            Date beginDate = expressDO.getBeginTime();
            if (beginDate != null) {
                if (date.before(beginDate)) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("结束时间不能早于开始时间！");
                    return true;
                }
            }
            Date endDate = expressDO.getEndTime();
            if (endDate != null) {
                if (!date.after(endDate)) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("结束时间不能早于原来结束时间！");
                    return true;
                }
            }

            expressDO.setEndTime(date);
            StBeanUtils.makeModifierField(expressDO, user);
            if (mapper.updateById(expressDO) < 0) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("更新结束日期出错！");
                return true;
            }
        }
        return false;
    }
}
