package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyQueryRequest;
import com.jackrain.nea.st.model.result.StCExpressPriceStrategyQueryResult;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/10 10:35
 * @Description
 * @Version 1.0
 */
@Mapper
public interface StCExpressPriceStrategyMapper extends ExtentionMapper<StCExpressPriceStrategyDO> {

    /**
     * 根据省/仓库 获取物流配置信息
     * @param request request
     * @return
     */
    @Select("select a.cp_c_phy_warehouse_id,a.cp_c_logistics_id,b.* " +
                    "from st_c_express_price_strategy a " +
                    "left join st_c_express_price_strategy_item b " +
                    "on a.id = b.st_c_express_price_strategy_id " +
                    "where a.cp_c_phy_warehouse_id =#{request.cpCPhyWarehouseId}  and b.province_id =#{request.cpCProvinceId} " +
                     "and a.start_date < now() and a.end_date > now() " +
                    "and a.status = 2 and a.isactive = 'Y' and b.isactive = 'Y'"
            )
    List<StCExpressPriceStrategyQueryResult> queryStCExpressPriceByParams(@Param("request") StCExpressPriceStrategyQueryRequest request);
}
