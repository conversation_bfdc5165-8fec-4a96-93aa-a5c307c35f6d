package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSendPlanItemMapper;
import com.jackrain.nea.st.mapper.StCSendPlanMapper;
import com.jackrain.nea.st.mapper.StCSendRuleMapper;
import com.jackrain.nea.st.model.table.StCSendPlanDO;
import com.jackrain.nea.st.model.table.StCSendPlanItemDO;
import com.jackrain.nea.st.model.table.StCSendRuleDO;
import com.jackrain.nea.st.utils.*;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Bll层-新增保存业务逻辑
 *
 * <AUTHOR> 黄火县
 * @since : 2019-03-08
 * create at : 2019-03-08 16:30
 */
@Component
@Slf4j
@Transactional
public class SendPlanSaveService extends CommandAdapter {
    @Autowired
    private StCSendPlanMapper stCMainMapper;

    @Autowired
    private StCSendPlanItemMapper stCItemMapper;

    @Autowired
    private StCSendRuleMapper stCSendRuleMapper;
    @Autowired
    private RedisOpsUtil redisUtil;

    private String str_table_main = "ST_C_SEND_PLAN";
    private String str_table_list = "ST_C_SEND_PLAN_ITEM";

    /**
     * 新增和修改
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject sendPlanMap = fixColumn.getJSONObject(str_table_main);
                JSONArray sendPlanItemMap = fixColumn.getJSONArray(str_table_list);
                valueHolder = saveSendPlan(id, sendPlanMap, sendPlanItemMap, querySession);
            }
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    public ValueHolder saveSendPlan(Long id, JSONObject sendPlanMap,
                                    JSONArray sendPlanItemMap,
                                    QuerySession querySession) {
        int iSuc = 0;
        ValueHolder valueHolder = new ValueHolder();
        StCSendPlanDO stCSaveDO = JsonUtils.jsonParseClass(sendPlanMap, StCSendPlanDO.class);
        if (stCSaveDO != null) {
            if (!checkMainData(stCSaveDO, id, valueHolder)) {
                return valueHolder;
            }
            //主表新增
            if (id < 0) {
                StBeanUtils.makeCreateField(stCSaveDO, querySession.getUser());
                stCSaveDO.setEstatus(StConstant.CON_BILL_STATUS_01);  //新增单据状态默认为: 待审核(未审核)
                stCSaveDO.setId(ModelUtil.getSequence(str_table_main));
                iSuc = stCMainMapper.insert(stCSaveDO);
            } else {
                if (stCSaveDO != null) {
                    stCSaveDO.setId(id);
                    StBeanUtils.makeModifierField(stCSaveDO, querySession.getUser());
                    iSuc = stCMainMapper.updateById(stCSaveDO);
                }
            }
            if (iSuc == 0) {
                valueHolder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return valueHolder;
            }
        }

        if (id > 0) {
            stCSaveDO = stCMainMapper.selectById(id);
        }

        if (sendPlanItemMap != null) {
            List<StCSendPlanItemDO> stCSendPlanItemDOList = JSON.parseObject(sendPlanItemMap.toJSONString(),
                    new TypeReference<ArrayList<StCSendPlanItemDO>>() {
                    });
            if (stCSendPlanItemDOList != null) {
                //判断明细同一个派单规则唯一.
                if (!checkListStatus(stCSendPlanItemDOList, id, valueHolder)) {
                    return valueHolder;
                }
                //保存时候，同一类型订单规则类型不允许重复；提示“同一类型订单规则不允许重复”
                if (checkItemListTypeFun(stCSendPlanItemDOList, id)) {
                    throw new NDSException("同一类型订单规则不允许重复！");
                }
                for (StCSendPlanItemDO stCSendPlanItemDO : stCSendPlanItemDOList) {
                    long itemid = stCSendPlanItemDO.getId();
                    if (itemid < 0) {
                        stCSendPlanItemDO.setId(ModelUtil.getSequence(str_table_list));
                        stCSendPlanItemDO.setStCSendPlanId(stCSaveDO.getId());
                        StBeanUtils.makeCreateField(stCSendPlanItemDO, querySession.getUser());
                        iSuc = stCItemMapper.insert(stCSendPlanItemDO);
                    } else {
                        StBeanUtils.makeModifierField(stCSendPlanItemDO, querySession.getUser());
                        iSuc = stCItemMapper.updateById(stCSendPlanItemDO);
                    }
                    if (iSuc == 0) {
                        valueHolder = ValueHolderUtils.getFailValueHolder("保存失败！");
                        return valueHolder;
                    }
                }
            }
        }

        if (id < 0) {
            valueHolder = ValueHolderUtils.getSuccessValueHolder(stCSaveDO.getId(), str_table_main);
        } else {
            valueHolder = ValueHolderUtils.getSuccessValueHolder(id, str_table_main);
        }

        //清除redisKey
       /* Long shopId = stCSaveDO.getCpCShopId();
        if (shopId != null) {
            String redisSendPlanKey = OmsRedisKeyResources.buildLockShopSendPlanRedisKey(shopId);
            if (redisUtil.objRedisTemplate.hasKey(redisSendPlanKey)) {
                redisUtil.objRedisTemplate.delete(redisSendPlanKey);
            }
        }*/
        RedisCacheUtil.delete(stCSaveDO.getCpCShopId(), RedisConstant.SHOP_SEND_PLAN);
        //如果是更新的话，删除redis明细
        if (id > 0) {
            String redisItemKey = OmsRedisKeyResources.buildLockSendPlanRuleRedisKey(id);
            if (redisUtil.objRedisTemplate.hasKey(redisItemKey)) {
                redisUtil.objRedisTemplate.delete(redisItemKey);
            }

            String redisPlanKey = "st:shop:send:plan:and:rule:shopid:" + stCSaveDO.getCpCShopId();
            if (redisUtil.strRedisTemplate.hasKey(redisPlanKey)) {
                redisUtil.strRedisTemplate.delete(redisPlanKey);
            }
        }
        return valueHolder;
    }

    /*
     *校验：同一类型订单规则类型不允许重复
     */
    private boolean checkItemListTypeFun(List<StCSendPlanItemDO> stCSendPlanItemDOList, Long id) {
        Map map = new HashMap<>();//判断本次需要新增或者编辑又没存在同种类型
        //依据主键id查询该主表下的明细
        List<StCSendPlanItemDO> stCSendPlanItemDOByIdList = stCItemMapper.listBySendPlanId(id);
        log.debug(LogUtil.format("订单方案明细待保存的前台参数={}"), stCSendPlanItemDOList);
        for (StCSendPlanItemDO stCSendPlanItemDO : stCSendPlanItemDOList) {
            String eType = "";//规则类型
            boolean flag = true;//判断更新方法是否进行判断数据库的校验；条件：当前台没有修改订单规则名称的时候就不需要校验;true代表要校验
            //1.判断新增或者更新的数据中是否存在重复的规则类型
            if (stCSendPlanItemDO.getId() == null || stCSendPlanItemDO.getId() < 1) {//新增明细
                if (stCSendPlanItemDO.getStCSendRuleId() == null) {
                    throw new NDSException("存在未填写订单规则的明细记录！");
                }
                long ruleId = stCSendPlanItemDO.getStCSendRuleId();//规则ruleId
                StCSendRuleDO stCSendRuleDO = stCSendRuleMapper.selectById(ruleId);//订单规则表，获取订单规则类型
                if (stCSendRuleDO == null || stCSendRuleDO.getEtype() == null) {
                    throw new NDSException("订单规则表无此订单规则,请填写正确的订单规则！");
                }
                eType = stCSendRuleDO.getEtype();//规则类型：类型 1.按收货地址 2.按分仓比例
                //判断新增或者更新的数据中是否存在重复的规则类型
                if (map.get(eType) != null) {
                    return true;
                }
            } else {//更新明细
                if (stCSendPlanItemDO.getStCSendRuleId() == null) {//为空代表，没有修改订单规则，所以不需要去数据库校验这条待修改的规则
                    flag = false;
                } else {
                    long ruleId = stCSendPlanItemDO.getStCSendRuleId();//作为查询规则表的查询主键
                    StCSendRuleDO stCSendRuleDO = stCSendRuleMapper.selectById(ruleId);//订单规则表，获取订单规则类型
                    eType = stCSendRuleDO.getEtype();//这是待保存的:获取规则类型：类型 1.按收货地址 2.按分仓比例
                    //判断新增或者更新的数据中是否存在重复的规则类型
                    if (map.get(eType) != null) {
                        return true;
                    }
                }
            }
            //2.更新判断数据库中是否存在
            if (stCSendPlanItemDOByIdList.size() > 0 && flag) {
                for (StCSendPlanItemDO itemDoTmp : stCSendPlanItemDOByIdList) {
                    if (itemDoTmp.getStCSendRuleId() == null || itemDoTmp.getId().equals(stCSendPlanItemDO.getId())) {
                        continue;
                    }
                    long ruleIdTmp = itemDoTmp.getStCSendRuleId();//规则类型id
                    StCSendRuleDO stCSendRuleDOTmp = stCSendRuleMapper.selectById(ruleIdTmp);//订单规则表，获取订单规则类型
                    String eTypeTmp = stCSendRuleDOTmp.getEtype();//这是数据库查询得到：获取规则类型：类型 1.按收货地址 2.按分仓比例
                    if (eType.equals(eTypeTmp)) {
                        return true;
                    }
                }
            }
            map.put(eType, eType);//存储需要新增的类型；key和value都为类型
        }
        return false;
    }

    /**
     * @param stCMainDO
     * @param valueHolder
     * @return boolean
     * @Descroption 时间判断
     * @Author: 黄火县
     * @Date 2019/3/26
     */
    private boolean checkMainData(StCSendPlanDO stCMainDO, Long mid, ValueHolder valueHolder) {

        if (mid > 0) {
            //修改方案
            StCSendPlanDO stCSendPlanDO = stCMainMapper.selectById(mid);
            if (stCMainDO != null) {
                stCMainDO.setEstatus(stCSendPlanDO.getEstatus());
                if (stCMainDO.getBeginTime() == null) {
                    stCMainDO.setBeginTime(stCSendPlanDO.getBeginTime());
                }
                if (stCMainDO.getEndTime() == null) {
                    stCMainDO.setEndTime(stCSendPlanDO.getEndTime());
                }
            }
        }

        if (stCMainDO != null) {
            if (!StConstant.CON_BILL_STATUS_01.equals(stCMainDO.getEstatus())) {
                if (StConstant.CON_BILL_STATUS_03.equals(stCMainDO.getEstatus())) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "当前记录已作废，不允许编辑！");
                    return false;
                } else if (mid > 0) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "当前记录状态非未审核，不允许编辑!");
                    return false;
                }
            }
            if (stCMainMapper.chenkRepeatByEname(mid, stCMainDO.getEname()) > 0) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "方案名称已存在，不能重复！");
                return false;
            }
        }

        if (stCMainDO != null) {
            if (stCMainDO.getBeginTime() == null) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "方案的开始日期不能为空！");
                return false;
            }
            if (stCMainDO.getEndTime() == null) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "方案的结束日期不能为空！");
                return false;
            }
            if (stCMainDO.getEndTime().before(stCMainDO.getBeginTime())) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "方案的结束日期不能小于开始日期！");
                return false;
            }
        }
        return true;
    }

    private boolean checkListStatus(List<StCSendPlanItemDO> stCSendPlanItemDOList,
                                    Long objId, ValueHolder valueHolder) {
        //新增和修改的内容全部放置在Map类型内，区别判断后面的Value 如果是修改的值存到是id，新增的行id则是-1
        Map<String, String> modifyMap = new HashMap();

        Map<String, String> oldMap = new HashMap<String, String>();

        String strKey = "";
        String strValue = "";
        List<Long> rankList = new ArrayList<>();

        if (stCSendPlanItemDOList != null) {

            for (StCSendPlanItemDO stCSendPlanItemDO : stCSendPlanItemDOList) {
                String strSendRuleId = "";
                StCSendPlanItemDO stIdDo = new StCSendPlanItemDO();
                if (stCSendPlanItemDO.getId() > 0) {
                    stIdDo = stCItemMapper.selectById(stCSendPlanItemDO.getId());
                    if (stIdDo == null) {
                        continue;
                    }
                }
                if (stCSendPlanItemDO.getStCSendRuleId() != null) {
                    strSendRuleId = stCSendPlanItemDO.getStCSendRuleId().toString();
                } else {
                    strSendRuleId = stIdDo.getStCSendRuleId().toString();
                }

                if (stCSendPlanItemDO.getRank() != null) {
                    rankList.add(stCSendPlanItemDO.getRank());
                } else {
                    rankList.add(stIdDo.getRank());
                }

                strKey = strSendRuleId;
                strValue = stCSendPlanItemDO.getId().toString();

                if (modifyMap.isEmpty()) {
                    modifyMap.put(strKey, strValue);
                } else {
                    if (modifyMap.containsKey(strKey)) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "请检查明细，同一经销商、快递公司、到货地址不能重复！");
                        return false;
                    } else {
                        modifyMap.put(strKey, strValue);
                    }
                }
            }

            //根据主表ID 查询出数据库明细所有的记录
            List<StCSendPlanItemDO> stCSendPlanItemDOListOld =
                    stCItemMapper.listBySendPlanId(objId);
            if (stCSendPlanItemDOListOld != null) {
                for (StCSendPlanItemDO stCDO : stCSendPlanItemDOListOld) {
                    if (stCDO.getId() != null && stCDO.getStCSendPlanId() != null) {
                        if (stCDO.getStCSendRuleId() == null) {
                            continue;
                        }
                        if (stCDO.getId() == null) {
                            continue;
                        }
                        strKey = stCDO.getStCSendRuleId().toString();
                        strValue = stCDO.getId().toString();
                        //判断如果是修改的话，则不存入oldMap
                        if (!modifyMap.containsValue(strValue)) {
                            oldMap.put(strKey, strValue);
                            rankList.add(stCDO.getRank());
                        }
                    }
                }
            } else {
                return true;
            }
        }

        Set<Long> ranks = new HashSet<Long>(rankList);
        if (rankList.size() != ranks.size()) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "优先级不能重复！");
            return false;
        }

        if (!oldMap.isEmpty() && !modifyMap.isEmpty()) {
            //判断modifyMap的key值 是否存在于oldMap的key值
            for (String key : modifyMap.keySet()) {
                if (oldMap.containsKey(key)) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "请检查明细，规则名称不能重复！");
                    return false;
                }
            }

        }

        return true;
    }

}

