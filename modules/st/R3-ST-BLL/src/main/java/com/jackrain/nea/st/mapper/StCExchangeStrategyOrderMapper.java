package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Mapper
public interface StCExchangeStrategyOrderMapper extends ExtentionMapper<StCExchangeStrategyOrderDO> {
    /**
     * @return com.jackrain.nea.st.model.table.StCMergeOrderDO
     * <AUTHOR>
     * @Description 根据ID和作废状态查询数据
     * @Date 2019/3/12
     * @Param [id, isDel]
     **/
    @Select("SELECT * FROM st_c_exchange_order_strategy WHERE ID = #{id} AND ISACTIVE = #{isactive} ")
    StCExchangeStrategyOrderDO selectByIdAndIsactive(@Param("id") Long id, @Param("isactive") String isactive);

    /**
     * 根据店铺编码 查询 订单合并策略的 数据集合
     *
     * @param cpCShopId 店铺编码
     * @return  返回订单合并策略的实体集合
     */
    @Select("SELECT * FROM st_c_exchange_order_strategy WHERE CP_C_SHOP_ID = #{cpCShopId} ")
    List<StCExchangeStrategyOrderDO> selectBycpCShopId(@Param("cpCShopId") Long cpCShopId);

    /**
     * 查询 所有订单合并策略的 数据集合
     *
     * @return  返回订单合并策略的实体集合
     */
    @Select("SELECT * FROM st_c_exchange_order_strategy WHERE ISACTIVE ='Y' ")
    List<StCExchangeStrategyOrderDO> queryAllMergeOrder();

    /**
     * 更新订单合并策略的店铺名称
     *
     * @param cpCShopTitle
     * @param cpCShopId
     * @return
     */
    @Update("UPDATE st_c_exchange_order_strategy\n" +
            "SET CP_C_SHOP_TITLE = #{cpCShopTitle}, MODIFIERID = #{modifierid}," +
            "MODIFIERENAME = #{modifierename}, MODIFIEDDATE = #{modifieddate}\n" +
            "WHERE CP_C_SHOP_ID = #{cpCShopId}")
    int updateShopTitleByShopId(@Param("cpCShopTitle") String cpCShopTitle,
                                @Param("modifierid") Long modifierid,
                                @Param("modifierename") String modifierename,
                                @Param("modifieddate") Date modifieddate,
                                @Param("cpCShopId") Long cpCShopId);

}