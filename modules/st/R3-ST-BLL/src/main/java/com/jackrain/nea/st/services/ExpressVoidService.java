package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressMapper;
import com.jackrain.nea.st.model.table.StCExpressDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2019/3/8 10:02
 */
@Component
@Slf4j
public class ExpressVoidService extends CommandAdapter {
    @Autowired
    private StCExpressMapper expressMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //生成Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = Long.valueOf(voidArray.get(i).toString());   // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                try {
                    voidExpress(id, session);
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * 物流方案作废
     *
     * @param id
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private void voidExpress(long id, QuerySession session) {
        //验证
        checkVipcomProject(id);
        //作废
        StCExpressDO express = new StCExpressDO();
        express.setId(id);
        express.setIsactive(StConstant.ISACTIVE_N);//作废状态
        express.setDelname(session.getUser().getName());//作废人用户名
        express.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        express.setDelename(session.getUser().getEname());//作废人姓名
        express.setDelTime(new Date());//作废时间
        StBeanUtils.makeModifierField(express, session.getUser());
        express.setBillStatus(StConstant.CON_BILL_STATUS_03);
        int update = expressMapper.updateById(express);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }
    }

    /**
     * 物流方案
     *
     * @param id
     * @return java.lang.String
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private void checkVipcomProject(long id) {
        StCExpressDO express = expressMapper.selectById(id);
        if (express == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (express.getIsactive().equals(StConstant.ISACTIVE_N)) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
            if (express.getBillStatus().equals(StConstant.CON_BILL_STATUS_02)) {
                throw new NDSException("当前记录已审核，不允许作废！");
            }
            if (express.getBillStatus().equals(StConstant.CON_BILL_STATUS_03)) {
                throw new NDSException("当前记录已作废，不允许作废！");
            }
            if (express.getBillStatus().equals(StConstant.CON_BILL_STATUS_04)) {
                throw new NDSException("当前记录已结案，不允许作废！");
            }
        }
    }

}
