package com.jackrain.nea.st.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;

@Mapper
public interface CommonLogMapper {

    @InsertProvider(type = CommonLogSql.class, method = "insert")
    int insert(JSONObject jsonObject);

    class CommonLogSql {
        public String insert(JSONObject jsonObject){
            return new SQL(){
                {
                    INSERT_INTO(jsonObject.getString("tableName"));
                    for (String key : jsonObject.keySet()){
                        if(!"tableName".equalsIgnoreCase(key)){
                            VALUES(key, "#{" + key + "}");
                        }
                    }
                }
            }.toString();
        }
    }
    @Select("SELECT * FROM st_c_autocheck_log WHERE st_c_autocheck_id = #{autocheckId}")
    List<JSONObject> selectAutocheckLog(@Param("autocheckId") long autocheckId);
}