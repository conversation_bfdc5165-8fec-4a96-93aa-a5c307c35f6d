package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCAutoInvoiceShopDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCAutoInvoiceShopMapper extends ExtentionMapper<StCAutoInvoiceShopDO> {

    @Delete("DELETE FROM ST_C_AUTO_INVOICE_SHOP WHERE ST_C_AUTO_INVOICE_NOTICE_ID=#{mainId}")
    void delShopByMainId(@Param(value = "mainId") Long mainId);
    /**
     * 多店铺查询
     *
     * @param shopIds
     * @return
     */
    @Select("SELECT shop.* FROM st_c_auto_invoice_shop shop " +
            "INNER JOIN st_c_auto_invoice invoice ON shop.st_c_auto_invoice_notice_id = invoice.id " +
            "WHERE invoice.isactive = 'Y' AND shop.cp_c_shop_id in (#{shopIds})")
    List<StCAutoInvoiceShopDO> selectShopListByShopIds(@Param("shopIds") String shopIds);
}