package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCOwnershipWarehouseSetDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCOwnershipWarehouseSetMapper extends ExtentionMapper<StCOwnershipWarehouseSetDO> {
    @Select("SELECT * FROM st_c_ownership_warehouse_set WHERE  ISACTIVE = 'Y' and  cp_c_store_id = #{storeId} and cp_c_store_property_type=#{cpCStorePropertyType}")
    StCOwnershipWarehouseSetDO selectDataByStoreId(@Param("storeId") Long storeId,@Param("cpCStorePropertyType") Long cpCStorePropertyType);

    /**
     * 根据仓库属性查询供货仓库信息
     * @param cpCStorePropertyType
     * @return
     */
    @Select("SELECT * FROM st_c_ownership_warehouse_set WHERE  ISACTIVE = 'Y'  and cp_c_store_property_type=#{cpCStorePropertyType}")
    List<StCOwnershipWarehouseSetDO> selectByChannelType(@Param("cpCStorePropertyType") Long cpCStorePropertyType);
}