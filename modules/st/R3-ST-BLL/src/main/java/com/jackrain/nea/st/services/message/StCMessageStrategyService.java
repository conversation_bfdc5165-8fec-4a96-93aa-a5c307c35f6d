package com.jackrain.nea.st.services.message;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.mapper.message.StCMessageStrategyItemMapper;
import com.jackrain.nea.st.mapper.message.StCMessageStrategyMapper;
import com.jackrain.nea.st.model.request.cycle.StCCyclePurchaseStrategyBillSaveRequest;
import com.jackrain.nea.st.model.request.cycle.StCCyclePurchaseStrategyItemSaveRequest;
import com.jackrain.nea.st.model.request.cycle.StCCyclePurchaseStrategySaveRequest;
import com.jackrain.nea.st.model.table.message.StCMessageStrategy;
import com.jackrain.nea.st.model.table.message.StCMessageStrategyItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: chenhao
 * @Date: 2022-09-12 17:31
 * @Description:
 */
@Slf4j
@Component
public class StCMessageStrategyService {

    @Autowired
    private StCMessageStrategyMapper mainMapper;
    @Autowired
    private StCMessageStrategyItemMapper itemMapper;

    private ValueHolderV14<StCCyclePurchaseStrategyBillSaveRequest> selectMessageStrategy() {

        log.info(LogUtil.format("StCMessageStrategyService.selectMessageStrategy",
                "StCMessageStrategyService.selectMessageStrategy"));

        StCCyclePurchaseStrategyBillSaveRequest billSaveRequest = new StCCyclePurchaseStrategyBillSaveRequest();

        List<StCMessageStrategy> stMessageStrategies = mainMapper.selectList(new LambdaQueryWrapper<StCMessageStrategy>()
                .eq(StCMessageStrategy::getIsactive, SgConstants.IS_ACTIVE_Y));

        ValueHolderV14<StCCyclePurchaseStrategyBillSaveRequest> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "查询成功！");

        if (CollectionUtils.isEmpty(stMessageStrategies)) {
            v14.setMessage("查询成功！无策略");
            return v14;
        }
        //有且只有一个
        StCMessageStrategy stMessageStrategy = stMessageStrategies.get(0);
        StCCyclePurchaseStrategySaveRequest saveRequest = new StCCyclePurchaseStrategySaveRequest();
        BeanUtils.copyProperties(stMessageStrategy, saveRequest);

        List<StCMessageStrategyItem> stMessageStrategyItems = itemMapper.selectList(new LambdaQueryWrapper<StCMessageStrategyItem>()
                .eq(StCMessageStrategyItem::getStCMessageStrategyId, stMessageStrategy.getId())
                .eq(StCMessageStrategyItem::getIsactive, SgConstants.IS_ACTIVE_Y));

        List<StCCyclePurchaseStrategyItemSaveRequest> itemSaveRequestList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stMessageStrategyItems)) {
            for (StCMessageStrategyItem item : stMessageStrategyItems) {
                StCCyclePurchaseStrategyItemSaveRequest itemSaveRequest = new StCCyclePurchaseStrategyItemSaveRequest();
                BeanUtils.copyProperties(item, itemSaveRequest);
                itemSaveRequestList.add(itemSaveRequest);
            }
        }

        billSaveRequest.setItems(itemSaveRequestList);
        billSaveRequest.setSaveRequest(saveRequest);
        v14.setData(billSaveRequest);

        log.info(LogUtil.format("StCMessageStrategyService.selectMessageStrategy ValueHolderV14:{}",
                "StCMessageStrategyService.selectMessageStrategy.ValueHolderV14"),
                JSONObject.toJSONString(v14));

        return v14;
    }
}
