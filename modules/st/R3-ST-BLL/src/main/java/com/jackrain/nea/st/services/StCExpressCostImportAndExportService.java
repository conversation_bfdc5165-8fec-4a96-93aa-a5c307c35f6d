package com.jackrain.nea.st.services;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.ac.api.AcLogisticsFeeCaculationCmd;
import com.jackrain.nea.ac.model.request.AcLogisticsFeeReCaculationRequest;
import com.jackrain.nea.ac.model.result.AcLogisticsFeeCaculationResult;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpLogisticsSelectServiceCmd;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.api.RegionQueryExtCmd;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressCostItemMapper;
import com.jackrain.nea.st.mapper.StCExpressCostMapper;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyMapper;
import com.jackrain.nea.st.mapper.StCUnfullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.ExpressCostTypeEnum;
import com.jackrain.nea.st.model.enums.LogisticsTypeEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.request.StCExpressCostPoi;
import com.jackrain.nea.st.model.result.StImportErrorMsgResult;
import com.jackrain.nea.st.model.table.StCExpressCost;
import com.jackrain.nea.st.model.table.StCExpressCostItem;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.request.ReCaculateLogisticsFeeRequest;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.StCExportUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/8/8 16:51
 * @Description
 */
@Component
@Slf4j
public class StCExpressCostImportAndExportService {


    @Reference(group = "cp-ext",version = "1.0")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;

    @Reference(group = "cp-ext",version = "1.0")
    private CpLogisticsSelectServiceCmd cpLogisticsSelectServiceCmd;

    @Reference(group = "cp-ext",version = "1.0")
    private RegionQueryExtCmd regionQueryExtCmd;

    @Reference(group = "ac",version = "1.0")
    private AcLogisticsFeeCaculationCmd acLogisticsFeeCaculationCmd;

    @Autowired
    private StCExportUtil stCExportUtil;

    @Autowired
    private StCExpressCostMapper stCExpressCostMapper;

    @Autowired
    private StCExpressPriceStrategyMapper stCExpressPriceStrategyMapper;

    @Autowired
    private StCUnfullcarCostMapper stCUnfullcarCostMapper;

    @Autowired
    private StCExpressCostItemMapper stCExpressCostItemMapper;

    private DateFormat format = new SimpleDateFormat("yyyyMMdd");

    @Value("${r3.sg.channel.oss.read.sleep.time:3}")
    private Long sleepTime;

    @Autowired
    private PropertiesConf pconf;

    public ValueHolderV14<List<StImportErrorMsgResult>> importExpressPriceCost(List<StCExpressCostPoi> importList, User user) {
        ValueHolderV14<List<StImportErrorMsgResult>> holderV14 = new ValueHolderV14<>();

        try {
            List<StImportErrorMsgResult> errorList = new ArrayList<>();
            //仓库信息
            Map<String, CpCPhyWarehouse> warehouseMap = new HashMap<>();
            ValueHolderV14<List<CpCPhyWarehouse>> warehouseResult = cpcPhyWareHouseQueryCmd.queryWarehourseIsY();
            if (ResultCode.SUCCESS==warehouseResult.getCode() && !CollectionUtils.isEmpty(warehouseResult.getData())) {
                warehouseMap = warehouseResult.getData().stream().collect(Collectors.toMap(CpCPhyWarehouse::getEname, Function.identity(), (x, y) -> y));
            }
            if (CollectionUtils.isEmpty(warehouseMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护实体仓信息！");
                return holderV14;
            }
            //物流信息
            Map<String, CpLogistics> logisticsMap = new HashMap<>();
            ValueHolderV14<List<CpLogistics>> logisticsResult = cpLogisticsSelectServiceCmd.queryLogisticsByType(LogisticsTypeEnum.TRANSPORTATION.getKey());
            if (ResultCode.SUCCESS == logisticsResult.getCode() && !CollectionUtils.isEmpty(logisticsResult.getData())) {
                logisticsMap = logisticsResult.getData().stream().collect(Collectors.toMap(CpLogistics::getEname, Function.identity(), (x, y) -> y));
            }
            if (CollectionUtils.isEmpty(logisticsMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护物流公司信息！");
                return holderV14;
            }
            //省份信息
            Map<String, CpCRegion> provinceMap = new HashMap<>();
            List<String> regionTypes = Lists.newArrayList("PROV");
            List<CpCRegion> regionList = regionQueryExtCmd.queryAllRegion(regionTypes);
            if (!CollectionUtils.isEmpty(regionList)) {
                provinceMap= regionList.stream().collect(Collectors.toMap(CpCRegion::getEname,Function.identity(),(x,y) -> y));
            }
            if (CollectionUtils.isEmpty(provinceMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护省份信息！");
                return holderV14;
            }
            //构建主子表关系，用于后面对象构建
            Map<String,List<StCExpressCostPoi>> groupByMainData = new HashMap<>();
            this.importDataCheck(importList,errorList,warehouseMap,logisticsMap,provinceMap,groupByMainData);

            if (!CollectionUtils.isEmpty(errorList)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("导入失败，导入数据存在错误！");
                holderV14.setData(errorList);
                return holderV14;
            }

            //构建保存和更新的对象集合
            List<StCExpressCost> expressCostList = new ArrayList<>();
            List<StCExpressCostItem> expressCostItemList = new ArrayList<>();
            this.buildSaveAndUpdateObj(groupByMainData,expressCostList,expressCostItemList,user);

            //数据库更新（带事务）
            if (!CollectionUtils.isEmpty(expressCostList) && !CollectionUtils.isEmpty(expressCostItemList)) {
                StCExpressCostImportAndExportService importService = ApplicationContextHandle.getBean(StCExpressCostImportAndExportService.class);
                importService.saveAll(expressCostList,expressCostItemList);
            }
            holderV14.setCode(ResultCode.SUCCESS);
            holderV14.setMessage("导入成功");
        } catch (Exception e) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("快运报价设置导入处理异常：" + e.getMessage());
            return holderV14;
        }
        return holderV14;
    }

    /**
     * 主子表批量保存
     * @param expressPriceList
     * @param expressPriceItemList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAll(List<StCExpressCost> expressPriceList, List<StCExpressCostItem> expressPriceItemList) {
        if (log.isDebugEnabled()) {
            log.debug("StCExpressCostImportService-saveAll start");
        }

        stCExpressCostMapper.batchInsert(expressPriceList);
        stCExpressCostItemMapper.batchInsert(expressPriceItemList);

    }

    /**
     * 根据import对象封装DTO对象
     * @param groupByMainData
     * @param expressPriceList
     * @param expressPriceItemList
     * @param user
     */
    private void buildSaveAndUpdateObj(Map<String, List<StCExpressCostPoi>> groupByMainData, List<StCExpressCost> expressPriceList, List<StCExpressCostItem> expressPriceItemList,User user) {
        if (!CollectionUtils.isEmpty(groupByMainData)) {
            Set<String> keySet = groupByMainData.keySet();
            for (String key : keySet) {
                List<StCExpressCostPoi> importRequestList = groupByMainData.get(key);
                if (!CollectionUtils.isEmpty(importRequestList)) {
                    Long stCExpressCostId = null;
                    for (int i= 0;i < importRequestList.size();i++) {
                        StCExpressCostPoi importRequest = importRequestList.get(i);
                        if (importRequest != null) {
                            StCExpressCostItem expressCostItem = new StCExpressCostItem();
                            if (i == 0) {
                                stCExpressCostId = ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_COST);
                                StCExpressCost expressCost = new StCExpressCost();
                                expressCost.setId(stCExpressCostId);
                                expressCost.setCpCPhyWarehouseId(importRequest.getCpCPhyWarehouseId());
                                expressCost.setCpCLogisticsId(importRequest.getCpCLogisticsId());
                                expressCost.setStartDate(importRequest.getStartDateForDate());
                                expressCost.setEndDate(importRequest.getEndDateForDate());
                                if (!StringUtils.isEmpty(importRequest.getRemark())) {
                                    expressCost.setRemark(importRequest.getRemark());
                                }
                                expressCost.setStatus(SubmitStatusEnum.NO_SUBMIT.getKey());
                                expressCost.setCloseStatus(CloseStatusEnum.NO_CLOSE.getKey());
                                reSetDateTimeRange(expressCost);

                                StBeanUtils.makeCreateField(expressCost, user);
                                expressPriceList.add(expressCost);
                            }
                            expressCostItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_COST_ITEM));
                            expressCostItem.setStCExpressCostId(stCExpressCostId);
                            expressCostItem.setProvinceId(importRequest.getProvinceId());
                            expressCostItem.setStartWeight(importRequest.getStartWeightNum());
                            expressCostItem.setEndWeight(importRequest.getEndWeightNum());
                            if (importRequest.getPriceExpressNum() != null) {
                                expressCostItem.setPriceExpress(importRequest.getPriceExpressNum());
                            }
                            if (importRequest.getPriceFirstWeightNum() != null) {
                                expressCostItem.setPriceFirstWeight(importRequest.getPriceFirstWeightNum());
                            }
                            StBeanUtils.makeCreateField(expressCostItem, user);
                            expressPriceItemList.add(expressCostItem);
                        }
                    }
                }
            }
        }
    }

    /**
     * 开始日期取当天0点，结束日期取当天23:59:59
     *
     * @param mainObj 主对象
     */
    private void reSetDateTimeRange(StCExpressCost mainObj) {
        if (Objects.isNull(mainObj)) {
            return;
        }
        if (Objects.nonNull(mainObj.getStartDate())) {
            mainObj.setStartDate(DateUtil.beginOfDay(mainObj.getStartDate()));
        }
        if (Objects.nonNull(mainObj.getEndDate())) {
            /*mainObj.setEndDate(DateUtil.endOfDay(mainObj.getEndDate()));*/
            DateTime dateTime = DateUtil.endOfDay(mainObj.getEndDate());
            dateTime.setTime(dateTime.getTime() - 999);
            mainObj.setEndDate(dateTime);
        }
    }
    private void importDataCheck(List<StCExpressCostPoi> importList, List<StImportErrorMsgResult> errorList, Map<String, CpCPhyWarehouse> warehouseMap, Map<String,
            CpLogistics> logisticsMap, Map<String, CpCRegion> provinceMap,Map<String,List<StCExpressCostPoi>> groupByMainData) {

        if (log.isDebugEnabled()) {
            log.debug("StCExpressCostImportService-importDataCheck  start");
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        format.setLenient(false);

        //已主表信息及省份信息作为key分组
        Map<String,List<StCExpressCostPoi>> importByMainDate = new HashMap<>();
        //检查必填是否为空
        //检查基础档案是否能够匹配
        //子表信息是否允许保存（以主表KEY+城市作为key转map）
        for (int i = 0 ; i < importList.size();i++) {
            StringBuffer sb = new StringBuffer();
            StCExpressCostPoi importRequest = importList.get(i);
            importRequest.setRowNum(i+3);
            if (StringUtils.isEmpty(importRequest.getWarehouseName())) {
                sb.append("仓库信息不能为空！");
            }else {
                CpCPhyWarehouse warehouse = warehouseMap.get(importRequest.getWarehouseName());
                if (warehouse == null) {
                    sb.append("仓库信息与实体仓档案未能匹配！");
                }else {
                    importRequest.setCpCPhyWarehouseId(warehouse.getId());
                }
            }
            if (StringUtils.isEmpty(importRequest.getLogisticsName())) {
                sb.append("物流公司信息不能为空！");
            }else {
                CpLogistics logistics = logisticsMap.get(importRequest.getLogisticsName());
                if (logistics == null) {
                    sb.append("物流公司信息与物流公司档案未能匹配！");
                }else {
                    importRequest.setCpCLogisticsId(logistics.getId());
                }
            }
            if (StringUtils.isEmpty(importRequest.getStartDate())) {
                sb.append("开始时间不能为空！");
            }else {
                boolean flag = isValidDate(importRequest.getStartDate());
                if (flag) {
                    try {
                        importRequest.setStartDateForDate(format.parse(importRequest.getStartDate()));
                    } catch (ParseException e) {
                        sb.append("开始时间格式错误！");
                    }
                }else {
                    sb.append("开始时间格式错误！");
                }
            }
            if (StringUtils.isEmpty(importRequest.getEndDate())) {
                sb.append("结束时间不能为空！");
            }else {
                boolean flag = isValidDate(importRequest.getEndDate());
                if (flag) {
                    try {
                        importRequest.setEndDateForDate(format.parse(importRequest.getEndDate()));
                    } catch (ParseException e) {
                        sb.append("结束时间格式错误！");
                    }
                }else {
                    sb.append("结束时间格式错误！");
                }
            }
            if (StringUtils.isEmpty(importRequest.getProvinceName())) {
                sb.append("目的省份不能为空！");
            }else {
                CpCRegion province = provinceMap.get(importRequest.getProvinceName());
                if (province == null) {
                    sb.append("目的省份与省份档案未能匹配！");
                }else {
                    importRequest.setProvinceId(province.getId());
                }
            }

            if (StringUtils.isEmpty(importRequest.getStartWeight())) {
                sb.append("起始重量不能为空！");
            }else {
                boolean flag = isBigDecimal(importRequest.getStartWeight());
                if (flag){
                    importRequest.setStartWeightNum(new BigDecimal(importRequest.getStartWeight()));
                }else {
                    sb.append("起始重量格式错误！");
                }
            }
            if (StringUtils.isEmpty(importRequest.getEndWeight())) {
                sb.append("结束重量不能为空！");
            }else {
                boolean flag = isBigDecimal(importRequest.getEndWeight());
                if (flag){
                    importRequest.setEndWeightNum(new BigDecimal(importRequest.getEndWeight()));
                }else {
                    sb.append("结束重量格式错误！");
                }
            }
            if (!StringUtils.isEmpty(importRequest.getPriceExpress())){
                boolean flag = isBigDecimal(importRequest.getPriceExpress());
                if (flag){
                    importRequest.setPriceExpressNum(new BigDecimal(importRequest.getPriceExpress()));
                }else {
                    sb.append("快递费用格式错误！");
                }
            }
            if (!StringUtils.isEmpty(importRequest.getPriceFirstWeight())){
                boolean flag = isBigDecimal(importRequest.getPriceFirstWeight());
                if (flag){
                    importRequest.setPriceFirstWeightNum(new BigDecimal(importRequest.getPriceFirstWeight()));
                }else {
                    sb.append("起步费格式错误！");
                }
            }

            if (importRequest.getStartDateForDate() != null && importRequest.getEndDateForDate() != null && importRequest.getStartDateForDate().getTime() > importRequest.getEndDateForDate().getTime()) {
                sb.append("开始时间不能大于结束时间！");
            }
            if (importRequest.getStartWeightNum().compareTo(importRequest.getEndWeightNum()) == 1) {
                sb.append("起始重量不能大于结束重量！");
            }
            if (sb.length() > 0) {
                StImportErrorMsgResult errorMsgResult = new StImportErrorMsgResult();
                errorMsgResult.setRowNum(i + 3);
                errorMsgResult.setErrorMsg(sb.toString());
                errorList.add(errorMsgResult);
            }
        }
        if (!CollectionUtils.isEmpty(errorList)) {
            //数据验证有问题
            return;
        }else {
            Integer errRow = 3;
            //数据验证没有问题，开始验证逻辑问题
            for (StCExpressCostPoi importRequest : importList) {
//                //验证是否有且仅有一种计费方式
//                Boolean flag = checkBillingMethodByInsert(importRequest);
//                if (!flag) {
//                    StImportErrorMsgResult errorMsg = new StImportErrorMsgResult();
//                    errorMsg.setRowNum(errRow);
//                    errorMsg.setErrorMsg("【快递费用】和【首重+续重】有且仅有一种计费方式");
//                    errorList.add(errorMsg);
//                }
                //城市维度分组
                String key = importRequest.getWarehouseName() + importRequest.getLogisticsName() + importRequest.getStartDate() + importRequest.getEndDate() + importRequest.getRemark() + importRequest.getProvinceName();
                if (importByMainDate.containsKey(key)){
                    List<StCExpressCostPoi> requestList = importByMainDate.get(key);
                    requestList.add(importRequest);
                    importByMainDate.put(key,requestList);
                }else {
                    List<StCExpressCostPoi> requestList = new ArrayList<>();
                    requestList.add(importRequest);
                    importByMainDate.put(key,requestList);
                }
                //主表维度分组
                String key1 = importRequest.getWarehouseName() + importRequest.getLogisticsName() + importRequest.getStartDate() + importRequest.getEndDate() + importRequest.getRemark();
                if (groupByMainData.containsKey(key1)){
                    List<StCExpressCostPoi> requestList = groupByMainData.get(key1);
                    requestList.add(importRequest);
                    groupByMainData.put(key1,requestList);
                }else {
                    List<StCExpressCostPoi> requestList = new ArrayList<>();
                    requestList.add(importRequest);
                    groupByMainData.put(key1,requestList);
                }

            }

            for (String key : importByMainDate.keySet()) {
                List<StCExpressCostPoi> list = importByMainDate.get(key);
                if (list.size() > 1) {
                    for (int i=0;i < list.size();i++) {
                        for (int j = i+1;j < list.size();j++) {
                            StCExpressCostPoi one = list.get(i);
                            StCExpressCostPoi two = list.get(j);
                            Boolean flag = checkWeight(one.getStartWeightNum(), one.getEndWeightNum(), two.getStartWeightNum(), two.getEndWeightNum());
                            if (!flag) {
                                StImportErrorMsgResult errorMsg = new StImportErrorMsgResult();
                                errorMsg.setRowNum(errRow++);
                                errorMsg.setErrorMsg("第" + one.getRowNum() + "行和第" + two.getRowNum() + "行的起始重量和结束重量存在交叉！");
                                errorList.add(errorMsg);
                            }
                        }
                    }
                }
            }

        }
    }

//    /**
//     * 确保有且只有一种计费方式
//     * @param itemDO
//     * @return
//     */
//    private Boolean checkBillingMethodByInsert(StCExpressCostPoi itemDO) {
//        //新增的验证有且只有一种计费方式
//        //如果快递费用为空，则首重和续重都不能为空，如果快递费用不为空，则首重和续重都得为空
//        if (itemDO.getPriceExpress() == null) {
//            if (!(itemDO.getPriceFirstWeight() != null && itemDO.getPriceContinuedWeight() != null)) {
//                return false;
//            }
//        }else {
//            if (!(itemDO.getPriceFirstWeight() == null && itemDO.getPriceContinuedWeight() == null)) {
//                return false;
//            }
//        }
//        return true;
//    }

    /**
     * 验证重量是否有交叉
     * @param startWeight
     * @param endWeight
     * @param startWeight1
     * @param endWeight1
     * @return
     */
    private Boolean checkWeight(BigDecimal startWeight, BigDecimal endWeight, BigDecimal startWeight1, BigDecimal endWeight1) {
        if (endWeight.compareTo(endWeight1) == 0) {
            return false;
        }else {
            if (endWeight.compareTo(endWeight1) == 1) {
                //endWeight大，当startWeight小于endWeight1时，返回false
                if (startWeight.compareTo(endWeight1) == -1) {
                    return false;
                }
            }else {
                //endWeight1大，当startWeight1小于endWeight时，返回false
                if (startWeight1.compareTo(endWeight) == -1) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 判断字符串是否为合法的日期格式
     * @param dateStr 待判断的字符串
     * @return
     */
    public static boolean isValidDate(String dateStr){
        //判断结果 默认为true
        boolean judgeresult=true;
        if (dateStr == null || dateStr.trim().length() != 8) {
            return false;
        }
        for (int i=0;i<dateStr.length();i++){
            if (!Character.isDigit(dateStr.charAt(i))) {
                return false;
            }
        }
        String yearStr=dateStr.substring(0,3);
        if(yearStr.startsWith("0")){
            judgeresult=false;
        }
        return judgeresult;
    }

    /**
     * 判断字符串是否是4位以内整数
     * @param str
     * @return
     */
    private boolean isInteger(String str){
        if (str == null || str.trim().length() == 0 || str.trim().length() > 4) {
            return false;
        }
        for (int i=0;i<str.length();i++){
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断字符串是否能转成BigDecimal
     * @param str
     * @return
     */
    private boolean isBigDecimal(String str){
        if(str==null || str.trim().length() == 0){
            return false;
        }
        char[] chars = str.toCharArray();
        int sz = chars.length;
        int i = (chars[0] == '-') ? 1 : 0;
        if(i == sz) return false; //第一位是-号，切长度为1则不行

        if(chars[i] == '.') return false;//除了负号，第一位不能为'小数点'

        boolean radixPoint = false;
        for(; i < sz; i++){
            if(chars[i] == '.'){
                if(radixPoint) return false;
                radixPoint = true;
            }else if(!(chars[i] >= '0' && chars[i] <= '9')){
                return false;
            }
        }
        return true;
    }


    /**
     * 下载错误信息
     * @param user
     * @param errMsgList
     * @return
     */
    public String downloadImportErrMsg(User user, List<StImportErrorMsgResult> errMsgList) {
        String columnNames[] = {"数据行", "错误原因"};
        List<Integer> columnWidthList = Lists.newArrayList(10, 120);
        List<String> columnList = Lists.newArrayList(columnNames);
        String keys[] = {"rowNum", "errorMsg"};
        List<String> keyList = Lists.newArrayList(keys);
        stCExportUtil.setEndpoint(pconf.getProperty("r3.oss.endpoint"));
        stCExportUtil.setAccessKeyId(pconf.getProperty("r3.oss.accessKey"));
        stCExportUtil.setAccessKeySecret(pconf.getProperty("r3.oss.secretKey"));
        stCExportUtil.setBucketName(pconf.getProperty("r3.oss.bucketName"));
        String timeout = pconf.getProperty("r3.oss.timeout");
        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000";
        }
        stCExportUtil.setTimeout(timeout);
        // 快运报价设置模板存放地址
        String filePath = "OSS-Bucket/EXPORT/ST_C_EXPRESS_COST/";

        Workbook hssfWorkbook = stCExportUtil.execute("错误信息", "快运报价设置导入-错误信息",
                columnList, keyList, errMsgList, columnWidthList, columnList);
        return stCExportUtil.saveFileAndPutOss(hssfWorkbook, "快运报价设置导入-错误信息", user, filePath);
    }

    /**
     * 导出零担费用设置
     * @param ids
     * @return
     */
    public ValueHolderV14<String> exportExpressPriceCost(String ids,User user) {

        ValueHolderV14<String> holderV14 = new ValueHolderV14<>();
        List<Long> idList = Arrays.stream(ids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());

        //仓库信息
        Map<Long, CpCPhyWarehouse> warehouseMap = new HashMap<>();
        ValueHolderV14<List<CpCPhyWarehouse>> warehourseResult = cpcPhyWareHouseQueryCmd.queryWarehourseIsY();
        if (ResultCode.SUCCESS==warehourseResult.getCode() && !CollectionUtils.isEmpty(warehourseResult.getData())) {
            warehouseMap = warehourseResult.getData().stream().collect(Collectors.toMap(CpCPhyWarehouse::getId, Function.identity(), (x, y) -> y));
        }
        if (CollectionUtils.isEmpty(warehouseMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护实体仓信息！");
            return holderV14;
        }
        //物流信息
        Map<Long, CpLogistics> logisticsMap = new HashMap<>();
        ValueHolderV14<List<CpLogistics>> logisticsResult = cpLogisticsSelectServiceCmd.queryLogisticsByType(LogisticsTypeEnum.TRANSPORTATION.getKey());
        if (ResultCode.SUCCESS == logisticsResult.getCode() && !CollectionUtils.isEmpty(logisticsResult.getData())) {
            logisticsMap = logisticsResult.getData().stream().collect(Collectors.toMap(CpLogistics::getId, Function.identity(), (x, y) -> y));
        }
        if (CollectionUtils.isEmpty(logisticsMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护物流公司信息！");
            return holderV14;
        }
        //省份信息
        Map<Long, CpCRegion> provinceMap = new HashMap<>();
        List<String> regionTypes = Lists.newArrayList("PROV");
        List<CpCRegion> regionList = regionQueryExtCmd.queryAllRegion(regionTypes);
        if (!CollectionUtils.isEmpty(regionList)) {
            provinceMap = regionList.stream().collect(Collectors.toMap(CpCRegion::getId,Function.identity(),(x,y) -> y));
        }
        if (CollectionUtils.isEmpty(provinceMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护省份信息！");
            return holderV14;
        }

        //循环查询主子表数据，转成SXSSFWorkbook
        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("快运报价设置导出");
        List<StCExpressCostPoi> exportPoiList = new ArrayList<>();
        for (int i=0;i < idList.size();i++) {
            exportPoiList = getPoi(idList.get(i),warehouseMap,logisticsMap,provinceMap);
            if (i == 0) {
                //第一个POI创建sheet,和表头
                this.createExcel(workbook,exportPoiList,sheet,true,user);
            }else {
                this.createExcel(workbook,exportPoiList,sheet,false,user);
            }
        }

        //上传OSS，并获得下载地址
        log.info(" Start ExportExpressPrice.createExcel");
        stCExportUtil.setEndpoint(pconf.getProperty("r3.oss.endpoint"));
        stCExportUtil.setAccessKeyId(pconf.getProperty("r3.oss.accessKey"));
        stCExportUtil.setAccessKeySecret(pconf.getProperty("r3.oss.secretKey"));
        stCExportUtil.setBucketName(pconf.getProperty("r3.oss.bucketName"));
        String timeout = pconf.getProperty("r3.oss.timeout");
        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000";
        }
        stCExportUtil.setTimeout(timeout);
        // 零担费用设置模板存放地址
        String filePath1 = "OSS-Bucket/EXPORT/ST_C_EXPRESS_COST/";
        String fileUrl = stCExportUtil.saveFileAndPutOss(workbook, "快运报价设置", user, filePath1);
        if (StringUtils.isEmpty(fileUrl)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("导出失败！获得下载地址失败！");
            return holderV14;
        }
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setMessage("导出成功");
        holderV14.setData(fileUrl);
        return holderV14;
    }

    /**
     * 创建workBOOOK
     * @param workbook
     * @param exportPoiList
     * @param sheet
     * @param flag 是否是第一条数据
     * @param user
     */
    private void createExcel(XSSFWorkbook workbook, List<StCExpressCostPoi> exportPoiList, Sheet sheet, boolean flag, User user) {
        List<String> columnNames = Lists.newArrayList("仓库", "物流公司", "开始日期", "结束日期", "备注", "目的省份", "起始重量（KG/不包含）", "结束重量（KG/包含）", "快递费用（元/KG）", "起步费（元）");
        if (flag) {
            int firstRowNum = 0;
            String titleName = "注意：\r\n" +
                    "1、红色标注项为必填项；\r\n" +
                    "2、日期格式：YYYYMMDD,例20190102； \r\n" +
                    "3、表头（1-2行）不允许修改，否则无法识别；\r\n" +
                    "4、输入时，不允许有空隔，不允许有空行，删除第3行样例数据，否则无法识别；\r\n" +
                    "5、仓库、物流公司、目的省份需与基础档案保持一致，否者无法识别；\r\n" +
                    "6、起始重量（KG/不包含）、结束重量（KG/包含）、快递费用（元/KG）、起步费（元）最大16位整数2位小数\r\n";
            ArrayList<String> mustColumnNames = Lists.newArrayList("仓库", "物流公司", "开始日期", "结束日期", "目的省份", "起始重量（KG/不包含）","结束重量（KG/包含）");
            // 创建第0行 也就是标题
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0,
                    columnNames.size()-1)); // 合并列标题
            Row row = sheet.createRow(firstRowNum);
            row.setHeightInPoints(120);// 设备标题的高度
            // 第三步创建标题的单元格样式style2以及字体样式headerFont1
            XSSFCellStyle style = workbook.createCellStyle();
            style.setAlignment(HorizontalAlignment.LEFT);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
//            style2.setFillForegroundColor((short) 0x29);
            //style2.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_TURQUOISE.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setWrapText(true); //开启自动换行
            XSSFFont headerFont1 = workbook.createFont(); // 创建字体样式
//            headerFont1.setBold(Boolean.TRUE); // 字体加粗
//            headerFont1.setFontName("黑体"); // 设置字体类型
            headerFont1.setFontHeightInPoints((short) 10); // 设置字体大小
            style.setFont(headerFont1); // 为标题样式设置字体样式
            Cell cell = row.createCell(0);// 创建标题第一列
            cell.setCellValue(titleName); // 设置值标题
            cell.setCellStyle(style); // 设置标题样式
            firstRowNum++;
            // 创建第1行 也就是表头
            Row row1 = sheet.createRow(1);
            row1.setHeightInPoints(15);// 设置表头高度
            for (int i=0;i<columnNames.size();i++) {
                Cell cell1 = row1.createCell(i);
                cell1.setCellValue(columnNames.get(i));
                XSSFCellStyle style1 = workbook.createCellStyle();
                XSSFFont font = workbook.createFont();
                if (mustColumnNames.contains(columnNames.get(i))) {
                    font.setColor(HSSFColor.RED.index);
                }else {
                    font.setColor(HSSFColor.BLACK.index);
                }
                style1.setFont(font);
                cell1.setCellStyle(style1);
//                sheet.autoSizeColumn(i);
//                sheet.setColumnWidth(i,sheet.getColumnWidth(i)*17/10);
            }
        }

        int lastRowNum = sheet.getLastRowNum() + 1;
        for (StCExpressCostPoi exportPoi : exportPoiList) {
            Row row = sheet.createRow(lastRowNum);
            row.setHeightInPoints(15);
            Cell cell1 = row.createCell(0);
            cell1.setCellValue(exportPoi.getWarehouseName());
            Cell cell2 = row.createCell(1);
            cell2.setCellValue(exportPoi.getLogisticsName());
            Cell cell3 = row.createCell(2);
            cell3.setCellValue(exportPoi.getStartDate());
            Cell cell4 = row.createCell(3);
            cell4.setCellValue(exportPoi.getEndDate());
            Cell cell6 = row.createCell(4);
            cell6.setCellValue(exportPoi.getRemark());
            Cell cell7 = row.createCell(5);
            cell7.setCellValue(exportPoi.getProvinceName());
            Cell cell8 = row.createCell(6);
            cell8.setCellValue(exportPoi.getStartWeight());
            Cell cell9 = row.createCell(7);
            cell9.setCellValue(exportPoi.getEndWeight());
            Cell cell10 = row.createCell(8);
            cell10.setCellValue(exportPoi.getPriceExpress());
            Cell cell11 = row.createCell(9);
            cell11.setCellValue(exportPoi.getPriceFirstWeight());
            lastRowNum++;
        }
    }

    /**
     * 转换为POI
     * @param id
     * @param warehouseMap
     * @param logisticsMap
     * @param provinceMap
     * @return
     */
    private List<StCExpressCostPoi> getPoi(Long id, Map<Long, CpCPhyWarehouse> warehouseMap, Map<Long, CpLogistics> logisticsMap, Map<Long, CpCRegion> provinceMap) {
        List<StCExpressCostPoi> exportPoiList = new ArrayList<>();
        //查询主表信息
        StCExpressCost expressCost = stCExpressCostMapper.selectById(id);
        if (expressCost == null) {
            throw new NDSException("主数据不存在或已删除！");
        }
        String warehouseName = null;
        String logisticsName = null;
        String startDate = null;
        String endtDate = null;
        String priceHomeDelivery = null;
        String remark = null;
        if (expressCost.getCpCPhyWarehouseId() != null && warehouseMap.get(expressCost.getCpCPhyWarehouseId()) != null) {
            warehouseName = warehouseMap.get(expressCost.getCpCPhyWarehouseId()).getEname();
        }
        if (expressCost.getCpCLogisticsId() != null && logisticsMap.get(expressCost.getCpCLogisticsId()) != null) {
            logisticsName = logisticsMap.get(expressCost.getCpCLogisticsId()).getEname();
        }
        if (expressCost.getStartDate() != null) {
            startDate = format.format(expressCost.getStartDate());
        }
        if (expressCost.getEndDate() != null) {
            endtDate = format.format(expressCost.getEndDate());
        }
        if (expressCost.getRemark() != null) {
            remark = expressCost.getRemark();
        }
        List<StCExpressCostItem> itemList = stCExpressCostItemMapper.selectList(new LambdaQueryWrapper<StCExpressCostItem>()
                .eq(StCExpressCostItem::getStCExpressCostId, id));
        if (CollectionUtils.isEmpty(itemList)) {
            StCExpressCostPoi exportPoi = new StCExpressCostPoi();
            exportPoi.setWarehouseName(warehouseName);
            exportPoi.setLogisticsName(logisticsName);
            exportPoi.setStartDate(startDate);
            exportPoi.setEndDate(endtDate);
            exportPoi.setRemark(remark);
            exportPoiList.add(exportPoi);
            return exportPoiList;
        }else {
            for (StCExpressCostItem item : itemList) {
                StCExpressCostPoi exportPoi = new StCExpressCostPoi();
                exportPoi.setWarehouseName(warehouseName);
                exportPoi.setLogisticsName(logisticsName);
                exportPoi.setStartDate(startDate);
                exportPoi.setEndDate(endtDate);
                exportPoi.setRemark(remark);
                if (item.getProvinceId() != null && provinceMap.get(item.getProvinceId()) != null) {
                    exportPoi.setProvinceName(provinceMap.get(item.getProvinceId()).getEname());
                }
                if (item.getStartWeight() != null) {
                    exportPoi.setStartWeight(item.getStartWeight().stripTrailingZeros().toPlainString());
                }
                if (item.getEndWeight() != null) {
                    exportPoi.setEndWeight(item.getEndWeight().stripTrailingZeros().toPlainString());
                }
                if (item.getPriceExpress() != null) {
                    exportPoi.setPriceExpress(item.getPriceExpress().stripTrailingZeros().toPlainString());
                }
                if (item.getPriceFirstWeight() != null) {
                    exportPoi.setPriceFirstWeight(item.getPriceFirstWeight().stripTrailingZeros().toPlainString());
                }
                exportPoiList.add(exportPoi);
            }
        }
        return exportPoiList;
    }

    public ValueHolderV14<AcLogisticsFeeReCaculationRequest> queryFeeChecklistCount(ReCaculateLogisticsFeeRequest request, User user) {
        ValueHolderV14<AcLogisticsFeeReCaculationRequest> v14 = new ValueHolderV14<>(ResultCode.SUCCESS,"success");

        AcLogisticsFeeReCaculationRequest reCaculationRequest = new AcLogisticsFeeReCaculationRequest();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        if (ExpressCostTypeEnum.UNFULLCAR.getval().equals(request.getType())) {
            StCUnfullcarCost unfullcarCost = stCUnfullcarCostMapper.selectById(request.getId());
            if (unfullcarCost == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("报价设置不存在或已删除");
                return v14;
            }
            if (!(SubmitStatusEnum.SUBMIT.getKey().equals(unfullcarCost.getStatus()) && CloseStatusEnum.NO_CLOSE.getKey().equals(unfullcarCost.getCloseStatus()))) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("您勾选了无效的报价!");
                return v14;
            }
            reCaculationRequest.setCpCPhyWarehouseId(unfullcarCost.getCpCPhyWarehouseId());
            reCaculationRequest.setCpCLogisticsId(unfullcarCost.getCpCLogisticsId());
            reCaculationRequest.setSelPriceListId(unfullcarCost.getId());
        }else if (ExpressCostTypeEnum.EXPRESS_PRICE.getval().equals(request.getType())) {
            StCExpressPriceStrategyDO expressPrice = stCExpressPriceStrategyMapper.selectById(request.getId());
            if (expressPrice == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("报价设置不存在或已删除");
                return v14;
            }
            if (!(SubmitStatusEnum.SUBMIT.getKey().equals(expressPrice.getStatus()) && CloseStatusEnum.NO_CLOSE.getKey().equals(expressPrice.getCloseStatus()))) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("您勾选了无效的报价!");
                return v14;
            }
            reCaculationRequest.setCpCPhyWarehouseId(expressPrice.getCpCPhyWarehouseId());
            reCaculationRequest.setCpCLogisticsId(expressPrice.getCpCLogisticsId());
            reCaculationRequest.setSelPriceListId(expressPrice.getId());
        }else if (ExpressCostTypeEnum.EXPRESS_COST.getval().equals(request.getType())) {
            StCExpressCost expressCost = stCExpressCostMapper.selectById(request.getId());
            if (expressCost == null) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("报价设置不存在或已删除");
                return v14;
            }
            if (!(SubmitStatusEnum.SUBMIT.getKey().equals(expressCost.getStatus()) && CloseStatusEnum.NO_CLOSE.getKey().equals(expressCost.getCloseStatus()))) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("您勾选了无效的报价!");
                return v14;
            }
            reCaculationRequest.setCpCPhyWarehouseId(expressCost.getCpCPhyWarehouseId());
            reCaculationRequest.setCpCLogisticsId(expressCost.getCpCLogisticsId());
            reCaculationRequest.setSelPriceListId(expressCost.getId());
        }else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("报价设置类型错误");
            return v14;
        }

        try {
            reCaculationRequest.setBeginConfirmDate(format.parse(request.getBeginConfirmDate()));
            reCaculationRequest.setEndConfirmDate(format.parse(request.getEndConfirmDate()));
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("对账日期格式错误");
            return v14;
        }
        reCaculationRequest.setLoginUser(user);

        ValueHolderV14<Integer> checklistCount = acLogisticsFeeCaculationCmd.queryFeeChecklistCount(reCaculationRequest);
        if (!checklistCount.isOK()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(checklistCount.getMessage());
            return v14;
        }
        if (checklistCount.getData() <= 0) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("没有符合条件的物流对账单!");
            return v14;
        }
        v14.setData(reCaculationRequest);
        return v14;
    }

    public ValueHolderV14<AcLogisticsFeeCaculationResult> reCaculateLogisticsFee(AcLogisticsFeeReCaculationRequest request) {
        return acLogisticsFeeCaculationCmd.reCaculateLogisticsFee(request);
    }

}
