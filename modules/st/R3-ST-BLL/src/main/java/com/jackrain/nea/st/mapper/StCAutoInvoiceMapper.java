package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCAutoInvoiceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface StCAutoInvoiceMapper extends ExtentionMapper<StCAutoInvoiceDO> {

    /**
     * 查询店铺自动开票策略
     *
     * @param shopId 店铺Id
     * @return StCAutoInvoiceDO
     */
    @Select("SELECT invoice.* FROM st_c_auto_invoice invoice " +
            "INNER JOIN st_c_auto_invoice_shop shop ON invoice.id = shop.st_c_auto_invoice_notice_id " +
            "WHERE shop.cp_c_shop_id=#{shopId} AND invoice.isactive='Y'")
    StCAutoInvoiceDO queryStCAutoInvoice(Long shopId);
}