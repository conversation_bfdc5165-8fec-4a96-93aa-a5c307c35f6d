package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCExchangeRefuseReasonItemDO;
import com.jackrain.nea.st.model.table.StCPriceItemDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


@Mapper
public interface StCExchangeRefuseReasonItemMapper extends ExtentionMapper<StCExchangeRefuseReasonItemDO> {

    @Select("select * from st_c_exchange_refuse_reason_item where exchange_order_strategy_id = #{id}")
    List<StCPriceItemDO> selectItemByPriceId(@Param("id") Long id);

    @Delete("delete from st_c_exchange_refuse_reason_item where exchange_order_strategy_id = #{id}")
    int deleteItemByExchangeId(@Param("id") Long id);
}