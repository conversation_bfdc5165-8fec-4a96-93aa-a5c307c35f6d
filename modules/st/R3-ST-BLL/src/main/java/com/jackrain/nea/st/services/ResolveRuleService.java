package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCResolveRuleMapper;
import com.jackrain.nea.st.model.table.StCResolveRuleDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption XXX
 * <AUTHOR>
 * @Date 2019-04-09 20:50
 */
@Component
@Slf4j
public class ResolveRuleService {
    @Autowired
    private StCResolveRuleMapper stCResolveRuleMapper;

    public List<StCResolveRuleDO> selectByPlatformId(Long platformId,JSONArray idsArray) throws NDSException{
        if(idsArray.size()<=0) {
            return stCResolveRuleMapper.selectByPlatformId(platformId);
        }else{
            StringBuilder sb=new StringBuilder();
            for (int i=0;i<idsArray.size();i++){
                sb.append(idsArray.getString(i)).append(",");
            }
            sb.deleteCharAt(sb.length()-1);
            return stCResolveRuleMapper.selectAnalysisDataByPlatformId(platformId,sb.toString());
        }
    }

    public Long selectPlatformIdById(Long id) throws NDSException{
        return  stCResolveRuleMapper.selectPlatformIdById(id);
    }
}
