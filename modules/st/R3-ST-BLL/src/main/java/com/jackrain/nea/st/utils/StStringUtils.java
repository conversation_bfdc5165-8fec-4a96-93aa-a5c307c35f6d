package com.jackrain.nea.st.utils;

import org.apache.commons.lang.StringUtils;

/**
 * String工具类
 *
 * @Auther: 黄志优
 * @Date: 2020/10/29 19:50
 * @Description:
 */
public class StStringUtils {

    // 判断是否完全一致
    public static boolean equalsWithNull(Object str1, Object str2) {
        String var1 = str1 == null ? null : (str1 instanceof String ? (String) str1 : str1.toString());
        String var2 = str2 == null ? null : (str2 instanceof String ? (String) str2 : str2.toString());
        return StringUtils.equals(var1, var2);
    }
}
