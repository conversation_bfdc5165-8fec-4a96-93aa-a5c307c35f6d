package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCBnProblemConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName StCBnProblemConfigMapper
 * @Description 班牛问题清单
 * <AUTHOR>
 * @Date 2024/11/11 14:53
 * @Version 1.0
 */
@Mapper
public interface StCBnProblemConfigMapper extends ExtentionMapper<StCBnProblemConfigDO> {

    @Select("SELECT * FROM st_c_bn_problem_config WHERE problem_text = #{problemText}")
    List<StCBnProblemConfigDO> selectByProblemText(String problemText);

    @Select("SELECT * FROM st_c_bn_problem_config")
    List<StCBnProblemConfigDO> selectAll();
}
