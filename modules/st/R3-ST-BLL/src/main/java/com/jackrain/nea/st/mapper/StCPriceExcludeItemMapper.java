package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCPriceExcludeItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCPriceExcludeItemMapper extends ExtentionMapper<StCPriceExcludeItemDO> {

    @Select("select * from st_c_price_exclude_item where st_c_price_id = #{id}")
    List<StCPriceExcludeItemDO> selectExcludeItemByPriceId(@Param("id") Long id);
}