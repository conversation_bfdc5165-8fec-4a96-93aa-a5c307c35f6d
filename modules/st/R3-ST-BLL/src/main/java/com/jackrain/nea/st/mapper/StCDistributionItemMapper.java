package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCDistributionItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCDistributionItemMapper extends ExtentionMapper<StCDistributionItemDO> {
    @Select("SELECT * FROM ST_C_DISTRIBUTION_ITEM WHERE ST_C_DISTRIBUTION_ID = #{mainId}")
    List<StCDistributionItemDO> selectItemByMainId(@Param("mainId") Long mainId);
}