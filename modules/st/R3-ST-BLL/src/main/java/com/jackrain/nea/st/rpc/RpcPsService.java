package com.jackrain.nea.st.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CbrandQueryByIdCmd;
import com.jackrain.nea.ps.api.CproDimItemQueryCmd;
import com.jackrain.nea.ps.api.ProSkuListCmd;
import com.jackrain.nea.ps.api.SkuListByProIdCmd;
import com.jackrain.nea.ps.api.request.BrandQueryRequest;
import com.jackrain.nea.ps.api.request.ProSkuListCmdRequest;
import com.jackrain.nea.ps.api.result.ProSkuResult;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.request.SkuListCmdRequest;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.PsCBrand;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.api.ProCategoryQueryCmd;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCProCategory;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.psext.request.SkuQueryRequest;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: ps中心服务调用层
 * @author: 郑小龙
 * @date: 2019-04-08 11:22
 */
@Component
@Slf4j
public class RpcPsService {
    @Reference(group = "ps-ext", version = "1.0")
    SkuLikeQueryCmd skuLikeQueryCmd;

    @DubboReference(group = "ps-ext", version = "1.0")
    ProCategoryQueryCmd proCategoryQueryCmd;

    @Autowired
    BasicPsQueryService basicPsQueryService;

    @Reference(group = "ps", version = "1.0")
    ProSkuListCmd proSkuListCmd;

    @Reference(group = "ps", version = "1.0")
    private SkuListByProIdCmd skuListByProIdCmd;

    @Reference(group = "ps", version = "1.0")
    private CproDimItemQueryCmd cproDimItemQueryCmd;

    @Reference(group = "ps", version = "1.0")
    private CbrandQueryByIdCmd cbrandQueryByIdCmd;

    public final static Integer CMD_QUERY_BATCH_SIZE = 300;

    public PsCProCategory queryProCategoryById(Long id) {
        return proCategoryQueryCmd.queryById(id);
    }

    /**
     * @param list
     * @return java.util.List<com.jackrain.nea.psext.model.table.PsCPro>
     * @Description 根据多个商品id批量查询商品信息
     * <AUTHOR>
     * @date 2019-04-08 13:21
     */
    public List<PsCPro> queryProByListID(List<Integer> list) {
        List<PsCPro> proList = skuLikeQueryCmd.queryByIds(list);
        if (!CollectionUtils.isEmpty(proList) && proList.size() > 0) {
            return proList;
        }
        return null;
    }

    /**
     * @param proId
     * @return com.jackrain.nea.psext.model.table.PsCPro
     * @Description 根据商品id查询商品信息
     * <AUTHOR>
     * @date 2019-04-08 13:26
     */
    public PsCPro queryProByID(Long proId) {
        List<Integer> list = new ArrayList<Integer>();
        list.add(proId.intValue());
        List<PsCPro> proList = this.queryProByListID(list);
        if (proList != null) {
            return proList.get(0);
        }
        return null;
    }

    /**
     * @return PsCSku
     * <AUTHOR>
     * @Description 通过id获取sku编码
     * @Date 2019-4-8
     * @Param [id]
     **/
    public PsCSku getSkuById(Long id) {
        //组装请求bean
        SkuQueryRequest skuQueryRequest = new SkuQueryRequest();
        PsCSku psCSku = new PsCSku();
        psCSku.setId(id);
        skuQueryRequest.setIsBlur("N");
        skuQueryRequest.setPsCSku(psCSku);

        Map<String, Object> map = skuLikeQueryCmd.querySku(skuQueryRequest);
        if (null != map) {
            String data = JSON.toJSONString(map.get("data"));
            JSONArray jsonArray = JSONObject.parseArray(data);
            if (!CollectionUtils.isEmpty(jsonArray)) {
                JSONObject jsonObject = (JSONObject) jsonArray.get(0);
                return JsonUtils.jsonParseClass(jsonObject, PsCSku.class);
            }
        }
        return null;
    }

    /**
     * @param proId
     * @return com.jackrain.nea.psext.model.table.PsCPro
     * @Description 根据商品id查询商品信息新接口
     * <AUTHOR>
     * @date 2019-05-08 9:26
     */
    public PsCPro queryProByIds(Long proId) {
        List<Integer> list = new ArrayList<Integer>();
        list.add(proId.intValue());
        List<PsCPro> proList = this.queryProByListIds(list);
        if (proList != null) {
            return proList.get(0);
        }
        return null;
    }
    /**
     * @param list
     * @return java.util.List<com.jackrain.nea.psext.model.table.PsCPro>
     * @Description 根据多个商品id批量查询商品信息(new)
     * <AUTHOR>
     * @date 2019-05-08 9:26
     */
    public List<PsCPro> queryProByListIds(List<Integer> list) {
        List<PsCPro> proList = skuLikeQueryCmd.queryProByIds(list);
        if (!CollectionUtils.isEmpty(proList) && proList.size() > 0) {
            return proList;
        }
        return null;
    }

    /**
     * @return PsCSku
     * <AUTHOR>
     * @Description 通过条码id获取条码信息
     * @Date 2019-5-8
     * @Param [id]
     **/
    public SkuQueryListRequest querySkuByIds(Long skuId) {
        List<Integer> list = new ArrayList<Integer>();
        list.add(skuId.intValue());
        List<SkuQueryListRequest> pskuList = this.querySkuByListIds(list);
        if (pskuList != null) {
            return pskuList.get(0);
        }
        return null;
    }
    /**
     * @param list
     * @return java.util.List<com.jackrain.nea.psext.model.table.PsCSku>
     * @Description 根据多个条码id批量查询条码信息(new)
     * <AUTHOR>
     * @date 2019-05-08 9:26
     */
    public List<SkuQueryListRequest> querySkuByListIds(List<Integer> list) {
        List<SkuQueryListRequest> pskuList = skuLikeQueryCmd.querySkuByIds(list);
        if (!CollectionUtils.isEmpty(pskuList) && pskuList.size() > 0) {
            return pskuList;
        }
        return null;
    }

    /**
     * @param list
     * @return java.util.List<com.jackrain.nea.psext.model.table.PsCPro>
     * @Description 根据多个商品code批量查询商品信息
     * <AUTHOR>
     * @date 2019-05-08
     */
    public List<PsCPro> queryProByEcodes(List<String> list) {
        List<PsCPro> psCProList = skuLikeQueryCmd.queryProByEcode(list);
        if (!CollectionUtils.isEmpty(psCProList) && psCProList.size() > 0) {
            return psCProList;
        }
        return null;
    }

    /**
     * @param list
     * @return java.util.List<com.jackrain.nea.psext.model.table.PsCSku>
     * @Description 根据多个条码code批量查询条码信息
     * <AUTHOR>
     * @date 2019-05-08
     */
    public List<PsCSku> querySkuByEcodes(List<String> list) {
        List<PsCSku> psCSkuList = skuLikeQueryCmd.querySkuByEcode(list);
        if (!CollectionUtils.isEmpty(psCSkuList) && psCSkuList.size() > 0) {
            return psCSkuList;
        }
        return null;
    }

    /**
     * @param id
     * @return com.jackrain.nea.ps.api.result.PsSkuResult
     * @Descroption 商品ID查询条码档案信息
     * @Author: 洪艺安
     * @Date 2019/5/14
     */
    public PsSkuResult getSkuResultByProId(long id) {
        return basicPsQueryService.getSkuInfoByProId(id);
    }

    /**
     * 调用查询商品标签
     *
     * @param id 商品标签id
     * @return 查询结果
     */
    public PsCProdimItem queryPsCProDimItem(Long id) {
        return cproDimItemQueryCmd.queryPsCProDimItem(id);
    }

    /**
     * 调用查询品牌名称
     *
     * @param brandIds 品牌ids
     * @return 查询结果
     */
    public List<PsCBrand> queryBrandByIds(List<Long> brandIds) {
        BrandQueryRequest request = new BrandQueryRequest();
        request.setBrandIdList(brandIds);
        ValueHolderV14<List<PsCBrand>> hold = cbrandQueryByIdCmd.queryBrandById(request);
        if (hold != null) {
            return hold.getData();
        }
        return null;
    }

    /**
     * 根据商品ID查询 proSkuMap
     *
     * @param proIds 商品ID集合
     * @return proSkuMap
     */
    public Map<String, List<PsCSku>> querySkuByProIds(List<Long> proIds) {
        SkuListCmdRequest skuListCmdRequest = new SkuListCmdRequest();
        skuListCmdRequest.setProIdList(proIds);
        ValueHolderV14<List<com.jackrain.nea.ps.api.table.PsCSku>> vh = skuListByProIdCmd.querySkuListByProInfoList(
                skuListCmdRequest);
        if (!vh.isOK()) {
            log.debug(LogUtil.multiFormat("调用PS接口查询sku信息失败，返回结果：{},查询请求参数=", proIds),
                    JSONObject.toJSONString(vh));
            return null;
        }
        List<com.jackrain.nea.ps.api.table.PsCSku> psCSkuList = vh.getData();
        if (CollectionUtils.isNotEmpty(psCSkuList)) {
            Map<String, List<PsCSku>> skuMap = new HashMap<>();
            psCSkuList.forEach(p -> {
                PsCSku sku = new PsCSku();
                BeanUtils.copyProperties(p, sku);
                List<PsCSku> extSkuList;
                if (skuMap.containsKey(p.getPsCProEcode())) {
                    extSkuList = skuMap.get(p.getPsCProEcode());
                } else {
                    extSkuList = new ArrayList<>();
                }
                extSkuList.add(sku);
                skuMap.put(p.getPsCProEcode(), extSkuList);
            });
            return skuMap;
        }
        return null;
    }

    public List<String> querySkuCodeByProId(Long proId){
        SkuListCmdRequest request = new SkuListCmdRequest();
        List<Long> proIdList = new ArrayList<>();
        proIdList.add(proId);
        request.setProIdList(proIdList);
        ValueHolderV14<List<com.jackrain.nea.ps.api.table.PsCSku>> v = skuListByProIdCmd.querySkuListByProInfoList(request);
        if(v.isOK()){
            List<com.jackrain.nea.ps.api.table.PsCSku> resultList = v.getData();
            if(CollectionUtils.isNotEmpty(resultList)){
               return resultList.stream().map(com.jackrain.nea.ps.api.table.PsCSku::getEcode).collect(Collectors.toList());
            }
            return new ArrayList<>();
        } else {
            throw new NDSException("商品对应的商品档案有问题");
        }
    }

    public Long queryProIdBySkuId(Long skuId){
        ProSkuListCmdRequest request = new ProSkuListCmdRequest();
        List<Long> skuIds = new ArrayList<>();
        skuIds.add(skuId);
        request.setSkuids(skuIds);
        ValueHolder v = proSkuListCmd.execute(request);
        if(v.isOK()){
            ProSkuResult result = (ProSkuResult)v.get("data");
            if(result!=null && CollectionUtils.isNotEmpty(result.getProSkuList())){
                return result.getProSkuList().get(0).getPsCProId();
            }
            return null;
        } else {
            throw new NDSException("商品对应的商品档案有问题");
        }
    }

    public List<PsCProSkuResult> querySkuInfoByProEcode(String proECode){
        ValueHolder v = proSkuListCmd.querySkuInfoByProEcode(proECode);
        if(v.isOK()){
            ProSkuResult result = (ProSkuResult)v.get("data");

            return result.getProSkuList();
        } else {
            throw new NDSException("商品对应的商品档案有问题");
        }
    }

    public List<PsCProSkuResult> querySkuInfoBySkuEcode(String skuECode){
        ProSkuListCmdRequest request = new ProSkuListCmdRequest();
        List<String> ecodes = new ArrayList<>();
        ecodes.add(skuECode);
        request.setEcodes(ecodes);
        ValueHolder v = proSkuListCmd.execute(request);
        if(v.isOK()){
            ProSkuResult result = (ProSkuResult)v.get("data");
            return result.getProSkuList();
        } else {
            throw new NDSException("商品对应的商品档案有问题");
        }
    }
}
