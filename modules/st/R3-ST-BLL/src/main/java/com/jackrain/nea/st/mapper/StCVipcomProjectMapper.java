package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Mapper
public interface StCVipcomProjectMapper extends ExtentionMapper<StCVipcomProjectDO> {

    @Select("SELECT * FROM ST_C_VIPCOM_PROJECT WHERE unix_timestamp(begin_time)<=unix_timestamp(#{curDate})" +
            "and unix_timestamp(end_time)>=unix_timestamp(#{curDate})  and isactive ='Y' ")
    List<StCVipcomProjectDO> selectByCurrentDate(@Param("curDate") String curDate);

    @Select("SELECT * FROM st_c_vipcom_project a " +
            "WHERE a.cp_c_shop_id = #{cpCShopId} and isactive ='Y' " +
            "AND #{effectiveDate} between a.begin_time and a.end_time " +
            "order by a.RANK, a.modifieddate desc")
    List<StCVipcomProjectDO> selectProjectByShopId(@Param("cpCShopId") Long cpCShopId, @Param("effectiveDate") Date effectiveDate);
}