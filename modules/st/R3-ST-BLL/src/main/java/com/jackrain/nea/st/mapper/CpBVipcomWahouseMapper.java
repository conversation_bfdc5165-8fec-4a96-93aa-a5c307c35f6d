package com.jackrain.nea.st.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.st.model.table.CpBVipcomWahouseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CpBVipcomWahouseMapper extends ExtentionMapper<CpBVipcomWahouseDO> {
    @Select("Select group_concat(warehouse_code) FROM CP_B_VIPCOM_WAHOUSE WHERE id in (#{ids})")
    String selectJitWareHouseCodesByIds(String ids);
}