package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCOrderLabelQueryCmd;
import com.jackrain.nea.st.model.request.StCOrderLabelRequest;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName : StCOrderLabelQueryCmdImpl  
 * @Description : 订单打标解析，提供外部rpc
 * <AUTHOR>  YCH
 * @Date: 2021-11-30 10:55  
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCOrderLabelQueryCmdImpl extends CommandAdapter implements StCOrderLabelQueryCmd {
    @Autowired
    private StCOrderLabelQueryService stCOrderLabelQueryService;

    @Override
    public List<StCOrderLabelRequest> queryDepositPreSaleSink(Long cpShopId) {
        return stCOrderLabelQueryService.queryOrderLabelk(cpShopId);
    }

    @Override
    public ValueHolderV14<List<StCOrderLabelRequest>> selectOrderLabel(Long cpShopId) {
        return stCOrderLabelQueryService.selectOrderLabel(cpShopId);
    }
}
