package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.st.api.CheckCpStoreCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 孙继东
 * @since: 2019-03-14
 * create at : 2019-03-14 19:00
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class CheckCpStoreCmdImpl implements CheckCpStoreCmd {
    @Autowired
    private CheckCpStoreService checkCpStoreService;
    @Override
    public Boolean checkCpStore(Long cpStoreId){
        return checkCpStoreService.checkCpStore(cpStoreId);
    }

}
