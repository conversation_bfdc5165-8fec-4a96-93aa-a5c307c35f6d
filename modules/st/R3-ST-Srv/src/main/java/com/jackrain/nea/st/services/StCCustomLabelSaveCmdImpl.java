package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCCustomLabelSaveCmd;
import com.jackrain.nea.st.api.StCOrderLabelSaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName : StCCustomLabelSaveCmdImpl  
 * @Description : 自定义标签档案
 * <AUTHOR>  YCH
 * @Date: 2021-11-29 10:39  
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCCustomLabelSaveCmdImpl extends CommandAdapter implements StCCustomLabelSaveCmd {
    @Autowired
    private StCCustomLabelSaveService saveService;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return saveService.execute(querySession);
    }
}
