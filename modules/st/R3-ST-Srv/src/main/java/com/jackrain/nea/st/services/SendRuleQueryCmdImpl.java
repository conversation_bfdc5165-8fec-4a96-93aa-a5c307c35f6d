package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.SendRuleQueryCmd;
import com.jackrain.nea.st.model.table.StCSendRuleAddressRankDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 订单派单规则查询
 * <AUTHOR>
 * @Date 2019/9/4 14:32
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class SendRuleQueryCmdImpl implements SendRuleQueryCmd {
    @Autowired
    private SendRuleQueryService service;

    /**
     * 查询派单规则
     *
     * @param obj 查询条件
     * @return
     */
    @Override
    public ValueHolderV14 querySendRule(JSONObject obj) {
        return service.querySendRule(obj);
    }

    /**
     * 查询省市区树
     *
     * @param obj 查询条件
     * @return
     */
    @Override
    public ValueHolderV14 querySendRuleTree(JSONObject obj) {
        return service.querySendRuleTree(obj);
    }

    /**
     * 获取订单派单规则唯品会仓库树
     *
     * @param obj 查询条件
     * @return
     */
    @Override
    public ValueHolderV14 querySendRuleVipWarehouseTree(JSONObject obj) {
        return service.querySendRuleVipWarehouseTree(obj);
    }

    /**
     * 获取唯品会仓库优先级明细数据
     *
     * @param obj 查询条件
     * @return
     */
    @Override
    public ValueHolderV14 queryVipWarehouseRankResultTable(JSONObject obj) {
        return service.queryVipWarehouseRankResultTable(obj);
    }

    /**
     * 查询仓库优先级明细表
     *
     * @param obj 查询条件
     * @return
     */
    @Override
    public ValueHolderV14 queryRankResultTable(JSONObject obj) {
        return service.queryRankResultTable(obj);
    }

    /**
     * 查询仓库信息
     *
     * @param obj 查询条件
     * @return
     */
    @Override
    public ValueHolderV14 queryWarehouseInfo(JSONObject obj) {
        return service.queryWarehouseInfo(obj);
    }

    /**
     * 查询分仓比例明细表
     *
     * @param obj 查询条件
     * @return
     */
    @Override
    public ValueHolderV14 queryRateResultTable(JSONObject obj) {
        return service.queryRateResultTable(obj);
    }

    /**
     * 模糊地区查询仓库优先级明细表
     *
     * @param obj 查询条件
     * @return
     */
    @Override
    public ValueHolderV14 queryLikeRankResultTable(JSONObject obj) {
        return service.queryLikeRankResultTable(obj);
    }

    /**
     * 根据省查询仓库优先级
     *
     * @param cpCRegionProvinceId 省份ID
     * @return
     */
    @Override
    public ValueHolderV14<StCSendRuleAddressRankDO> queryWarehouseRankInfo(Long cpCRegionProvinceId) {
        return service.queryWarehouseRankInfo(cpCRegionProvinceId);
    }
}
