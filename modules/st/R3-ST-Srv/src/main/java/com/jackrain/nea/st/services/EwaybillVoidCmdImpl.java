package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.EwaybillVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 分销代销策略作废
 * <AUTHOR>
 * @Date 2019/3/12 14:20
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class EwaybillVoidCmdImpl extends CommandAdapter implements EwaybillVoidCmd {
    @Autowired
    private EwaybillVoidService ewaybillVoidService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return ewaybillVoidService.execute(querySession);
    }
}
