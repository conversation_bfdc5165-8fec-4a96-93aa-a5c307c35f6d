package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.BnProblemConfigDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName BnProblemConfigDelCmdImpl
 * @Description 班牛
 * <AUTHOR>
 * @Date 2024/11/11 16:46
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class BnProblemConfigDelCmdImpl extends CommandAdapter implements BnProblemConfigDelCmd {

    @Autowired
    private BnProblemConfigDelService bnProblemConfigDelService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return bnProblemConfigDelService.del(querySession);
    }
}
