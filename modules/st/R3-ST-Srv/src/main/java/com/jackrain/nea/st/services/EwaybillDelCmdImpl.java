package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.DistributionDelCmd;
import com.jackrain.nea.st.api.EwaybillDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 电子面单策略删除
 * <AUTHOR>
 * @Date 2019/3/11 21:32
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class EwaybillDelCmdImpl extends CommandAdapter implements EwaybillDelCmd {
    @Autowired
    private EwaybillDelService ewaybillDelService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return ewaybillDelService.execute(querySession);
    }
}
