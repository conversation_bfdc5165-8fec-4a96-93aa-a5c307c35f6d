package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.VipcomCooperationNoVoidCmd;
import com.jackrain.nea.st.api.VipcomProjectVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description：常态合作编码作废
 *
 * <AUTHOR>
 * @date 2021/6/30
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VipcomCooperationNoVoidCmdImpl extends CommandAdapter implements VipcomCooperationNoVoidCmd {
    @Autowired
    private VipcomCooperationNoVoidService vipcomCooperationNoVoidService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return vipcomCooperationNoVoidService.execute(querySession);
    }
}
