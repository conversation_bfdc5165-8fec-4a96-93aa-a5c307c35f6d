package com.jackrain.nea.st.services.message;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.message.StCMessageStrategyDeleteR3Cmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @Auther: chenhao
 * @Date: 2022-09-12 15:29
 * @Description:
 */


@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCMessageStrategyDeleteR3CmdImpl implements StCMessageStrategyDeleteR3Cmd {

    @Autowired
    private StCMessageStrategyDeleteService saveService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return saveService.delete(session);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
