package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.ExpressCancleAuditCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * @Descroption 物流方案反审核
 * @Author: 汪聿森
 * @Date: 2019/3/22 16:26
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ExpressCancleAuditCmdImpl extends CommandAdapter implements ExpressCancleAuditCmd {

    @Autowired
    private ExpressCancleAuditService expressCancleAuditService;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return expressCancleAuditService.execute(querySession);
    }
}
