package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.SplitOrderReasonCmd;
import com.jackrain.nea.st.model.table.StCOrderSplitMergeType;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 查询拆单类型
 * @author: 江家雷
 * @since: 2020/11/12
 * create at : 2020/11/12 13:34
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class SplitOrderReasonCmdImpl extends CommandAdapter implements SplitOrderReasonCmd {

    @Autowired
    private SplitOrderReasonService splitOrderReasonService;

    @Override
    public Integer getSplitReason(String code) {
        return this.splitOrderReasonService.getSplitReason(code);
    }

    @Override
    public List<StCOrderSplitMergeType> getStCOrderSplitMergeType(List<Long> ids) {
        return this.splitOrderReasonService.getStCOrderSplitMergeType(ids);
    }
}