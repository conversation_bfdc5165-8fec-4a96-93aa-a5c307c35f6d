package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.WarehouseLogisticsQueryCmd;
import com.jackrain.nea.st.model.request.WarehouseLogisticsRankRequest;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsRankDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 仓库物流规则查询
 * <AUTHOR>
 * @Date 2019/8/14 14:32
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class WarehouseLogisticsQueryCmdImpl implements WarehouseLogisticsQueryCmd {
    @Autowired
    private WarehouseLogisticsQueryService service;

    @Override
    public ValueHolderV14 queryWarehouseLogisticsTree(JSONObject obj) {
        return service.queryWarehouseLogisticsTree(obj);
    }

    @Override
    public ValueHolderV14 queryRankResultTable(JSONObject obj) {
        return service.queryRankResultTable(obj);
    }

    @Override
    public ValueHolderV14 queryLogisticsInfo(JSONObject obj) {
        return service.queryLogisticsInfo(obj);
    }

    @Override
    public ValueHolderV14 queryLikeRankResultTable(JSONObject obj) {
        return service.queryLikeRankResultTable(obj);
    }

    @Override
    public ValueHolderV14<StCWarehouseLogisticsRankDO> queryLogisticsRankInfo(WarehouseLogisticsRankRequest rankRequest) {
        return service.queryLogisticsRankInfo(rankRequest);
    }
}
