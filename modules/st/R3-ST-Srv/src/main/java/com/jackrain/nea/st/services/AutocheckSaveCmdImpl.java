package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.AutocheckSaveCmd;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.model.enums.AutoCheckProLevelEnum;
import com.jackrain.nea.st.model.table.StCAutoCheckAutoTimeDO;
import com.jackrain.nea.st.model.table.StCAutoCheckExcludeProductDO;
import com.jackrain.nea.st.model.vo.StCAutocheckExcludeProductImpVo;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Descroption 订单自动审核-保存
 * <AUTHOR>
 * @Date 2019/3/12 18:28
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class AutocheckSaveCmdImpl extends CommandAdapter implements AutocheckSaveCmd {
    @Autowired
    private AutoCheckSaveService service;
    @Autowired
    private ExportUtil exportUtil;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        return service.execute(querySession);
    }

    @Override
    public ValueHolder getAutoCheck(Long id) {
        return service.getAutoCheck(id);
    }

    @Override
    public ValueHolderV14<JSONArray>  batchUpdateAutoCheck(JSONObject fixColumn, List<Long> idList, User user) {
        ValueHolderV14<JSONArray>  v14 = new ValueHolderV14();
        try{
            JSONArray productJsonList = fixColumn.getJSONArray(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT);
            if(CollectionUtils.isNotEmpty(productJsonList)){
                for(Object obj:productJsonList){
                    JSONObject jsonObject = (JSONObject)obj;
                    if(!AutoCheckProLevelEnum.CATEGORY.getKey().equals(jsonObject.getInteger("PRO_LEVEL"))
                            &&!AutoCheckProLevelEnum.PRO_CODE.getKey().equals(jsonObject.getInteger("PRO_LEVEL"))
                            &&!AutoCheckProLevelEnum.SKU_CODE.getKey().equals(jsonObject.getInteger("PRO_LEVEL"))){
                        throw new NDSException("批量修改只能选择品类、商品编码、商品条码三个级别");
                    }
                }
            }
            int failTimes = 0;
            JSONArray jsonArray = new JSONArray();
            for(Long id : idList){
                try{
                    //复制一个bean
                    JSONObject jsonObject1 = JSONObject.parseObject(fixColumn.toJSONString());
                    ValueHolder vh = service.updateAutoCheck(user, jsonObject1, id, true);
                    if (!vh.isOK()) {
                        failTimes ++;
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("id",id);
                        jsonObject.put("message",vh.get("message"));
                        jsonArray.add(jsonObject);
                    }


                } catch (Exception e){
                    log.error(LogUtil.format("修改策略失败","自动审核策略"),e);
                    failTimes ++;
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id",id);
                    jsonObject.put("message",e.getMessage());
                    jsonArray.add(jsonObject);
                }
            }
            if (failTimes == 0) {
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("保存成功");
            }
            if (failTimes > 0 && failTimes != idList.size()) {
                v14.setCode(ResultCode.FAIL);
                v14.setData(jsonArray);
                v14.setMessage("成功" + (idList.size() - failTimes) + "条失败:" + failTimes + "条");
            }
            if(failTimes == idList.size()){
                v14.setCode(ResultCode.FAIL);
                v14.setData(jsonArray);
                v14.setMessage("保存失败");
            }
        } catch (Exception e){
            log.error(LogUtil.format("修改策略失败","自动审核策略"),e);
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 保存自动审核时间
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14 saveAutoCheckTime(StCAutoCheckAutoTimeDO param, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        try{
            service.saveAutoCheckTime(param, user);
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("保存成功！");
        }catch (Exception e){
            log.error(LogUtil.format("保存审核时间：{}","订单自动审核策略"), Throwables.getStackTraceAsString(e));
            valueHolderV14.setMessage(e.getMessage());
            valueHolderV14.setCode(ResultCode.FAIL);
        }
        return valueHolderV14;
    }

    /**
     * 批量修改策略时间
     * @param fixColumn
     * @param idList
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<JSONArray> batchUpdateAutoCheckTime(JSONObject fixColumn, List<Long> idList, User user) {
        ValueHolderV14<JSONArray>  v14 = new ValueHolderV14();
        try{
            int failTimes = 0;
            JSONArray jsonArray = new JSONArray();
            for(Long id : idList){
                try{
                    service.updateAutoCheckTime(user, fixColumn, id);
                } catch (Exception e){
                    failTimes ++;
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id",id);
                    jsonObject.put("message",e.getMessage());
                    jsonArray.add(jsonObject);
                }
            }
            if (failTimes == 0) {
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("保存成功");
            }
            if (failTimes > 0 && failTimes != idList.size()) {
                v14.setCode(ResultCode.FAIL);
                v14.setData(jsonArray);
                v14.setMessage("成功" + (idList.size() - failTimes) + "条失败:" + failTimes + "条");
            }
            if(failTimes == idList.size()){
                v14.setCode(ResultCode.FAIL);
                v14.setData(jsonArray);
                v14.setMessage("保存失败");
            }
        } catch (Exception e){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 导入排除商品
     * @param dataImpVos
     * @param user
     * @return
     */
    public ValueHolderV14 importDataList(String objid,List<StCAutocheckExcludeProductImpVo> dataImpVos, User user) {
        ValueHolderV14<Object> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "导入成功！");
        List<StCAutocheckExcludeProductImpVo> checkedDataImpVos = checkValid(dataImpVos);
        int successNum = 0;
        try {
            List<StCAutoCheckExcludeProductDO> stCAutoCheckExcludeProductDOS = service.batchSaveData(objid, checkedDataImpVos, user);
            holderV14.setData(stCAutoCheckExcludeProductDOS);
            successNum = stCAutoCheckExcludeProductDOS.size();
        }catch (Exception e){
            log.error("自动审核排除商品导入保存入库失败！",e);
        }
        int failNum = dataImpVos.size()-successNum;
        if (failNum>0) {
            holderV14 = new ValueHolderV14<>();
            if(successNum==0){
                holderV14.setCode(ResultCode.FAIL); //
            }else{
                holderV14.setCode(ResultCode.SUCCESS); //
            }
            holderV14.setMessage("自动审核排除商品导入失败条数:["+failNum+"]，成功条数:["+successNum+"]，详情见文件内容");
        }else{
            holderV14.setMessage("自动审核排除商品导入成功条数:["+successNum+"]");
        }

        log.info("自动审核排除商品导入结果 {}", JSON.toJSONString(holderV14));
        return holderV14;
    }

    /**
     *
     * @param invoiceImpVos
     * @param user
     * @return
     */
    public String exportResut(List<StCAutocheckExcludeProductImpVo> invoiceImpVos, User user) {
        log.info("上传异常文件："+JSONObject.toJSONString(invoiceImpVos));
        List<StCAutocheckExcludeProductImpVo> errorList = invoiceImpVos.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
        // 列名
        String[] columnNames = {"主表行号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "desc"};
        List<String> k = Lists.newArrayList(keys);
        log.info("上传开始");
        Workbook hssfWorkbook = exportUtil.execute("自动审核策略排除商品导入", "自动审核策略排除商品导入", c, k, errorList);
        String url = exportUtil.saveFileAndPutOss(hssfWorkbook, "自动审核策略排除商品导入错误信息", user, "OSS-Bucket/EXPORT/ST_C_AUTOCHECK_STRATEGY/");
        log.info("上传结束");
        return url;
    }

    /**
     * 参数有效性校验
     * @param dataImpVos
     * @return
     */
    private List<StCAutocheckExcludeProductImpVo> checkValid(List<StCAutocheckExcludeProductImpVo> dataImpVos) {
        List<StCAutocheckExcludeProductImpVo> checkedDataImpVos = new ArrayList<>();
        // 数据缺失校验
        StringBuilder checkMessage = new StringBuilder();
        for(StCAutocheckExcludeProductImpVo vo:dataImpVos) {
            if (Objects.isNull(vo)) {
                checkMessage.append("[排除商品入参不能为空]");
            }else {
                if (Objects.isNull(vo.getProLevel())) {
                    checkMessage.append("[商品等级不能为空]");
                }
                switch(vo.getProLevel()){
                    case 1:
                        if(StringUtils.isBlank(vo.getPsCProdimItemEname())){
                           checkMessage.append("[品类不能为空]");
                        }
                       break;
                    case 2:
                        if(StringUtils.isBlank(vo.getPsCProEcode())){
                            checkMessage.append("[商品编码不能为空]");
                        }
                        break;
                    case 3:
                        if(StringUtils.isBlank(vo.getPsCSkuEcode())){
                            checkMessage.append("[商品条码不能为空]");
                        }
                        break;
                    case 4:
                        if(StringUtils.isBlank(vo.getPlatformNumiid())){
                            checkMessage.append("[平台商品ID不能为空]");
                        }
                        break;
                    case 5:
                        if(StringUtils.isBlank(vo.getPlatformSkuId())){
                            checkMessage.append("[平台条码ID不能为空]");
                        }
                    default:
                        checkMessage.append("[商品级别输入有误]");
                }
            }
            if (StringUtils.isNotEmpty(checkMessage.toString())) {
                vo.setDesc(checkMessage.toString());
                checkMessage.setLength(0);
            }else{
                checkedDataImpVos.add(vo);
            }
        }
        return checkedDataImpVos;
    }
}