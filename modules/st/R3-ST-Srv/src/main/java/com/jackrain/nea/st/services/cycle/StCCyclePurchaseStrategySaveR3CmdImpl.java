package com.jackrain.nea.st.services.cycle;


import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.api.cycle.StCCyclePurchaseStrategySaveR3Cmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @Auther: chenhao
 * @Date: 2022-09-03 15:44
 * @Description:
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCCyclePurchaseStrategySaveR3CmdImpl implements StCCyclePurchaseStrategySaveR3Cmd {

    @Autowired
    private StCCyclePurchaseStrategySaveService service;

    @Override
    @StOperationLog(mainTableName = "ST_C_CYCLE_PURCHASE_STRATEGY", itemsTableName = "ST_C_CYCLE_PURCHASE_STRATEGY_ITEM")
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.execute(session);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
