package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCExpressCostSaveCmd;
import com.jackrain.nea.st.api.StCExpressPriceStrategySaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/8/8 9:42
 * @Description
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCExpressCostSaveCmdImpl extends CommandAdapter implements StCExpressCostSaveCmd {

    @Autowired
    private StCExpressCostSaveService stCExpressCostSaveService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCExpressCostSaveService.execute(session);
    }
}
