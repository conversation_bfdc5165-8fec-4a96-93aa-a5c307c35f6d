package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.OMSRpcCmd;
import com.jackrain.nea.st.rpc.OMSRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2020/3/3
 * create at : 2020/3/3 16:10
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class OMSRpcCmdImpl implements OMSRpcCmd {

    @Autowired
    OMSRpcService omsRpcService;


    @Override
    public Boolean selectByLogisticsForWarehouseRulesIsExit(Long cpCPhyWarehouseId, Long LogisticsId) {
        return omsRpcService.selectByLogisticsForWarehouseRulesIsExit(cpCPhyWarehouseId, LogisticsId);
    }
}
