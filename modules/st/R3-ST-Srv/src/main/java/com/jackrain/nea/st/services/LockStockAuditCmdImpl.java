package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.LockStockAuditCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 店铺策略锁库设置审核
 * @Author: 汪聿森
 * @Date: 2019/3/26 19:15
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class LockStockAuditCmdImpl extends CommandAdapter implements LockStockAuditCmd {
    @Autowired
    private LockStockAuditService lockStockAuditService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return lockStockAuditService.execute(session);
    }
}
