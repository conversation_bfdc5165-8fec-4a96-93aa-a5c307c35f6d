package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCSplitReasonQueryCmd;
import com.jackrain.nea.st.model.request.StCSplitReasonRequest;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: r3-st
 * @description: 查询自定义拆单原因策略
 * @author: liuwj
 * @create: 2021-06-02 10:50
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCSplitReasonQueryCmdImpl extends CommandAdapter implements StCSplitReasonQueryCmd {

    @Autowired
    StCSplitReasonQueryService stCSplitReasonQueryService;

    @Override
    public List<StCSplitReasonRequest> queryStCSplitReasonBySplitReason(String systemSplitReason) {
        return stCSplitReasonQueryService.queryStCSplitReasonBySplitReason(systemSplitReason);
    }

    @Override
    public List<StCSplitReasonRequest> queryStCSplitReasonAll() {
        return stCSplitReasonQueryService.queryStCSplitReasonAll();
    }
}
