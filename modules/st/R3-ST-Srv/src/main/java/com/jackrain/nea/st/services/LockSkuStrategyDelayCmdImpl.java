package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.LockSkuStrategyDelayCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 陈俊明
 * @since: 2019-04-08
 * @create at : 2019-04-08 21:09
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class LockSkuStrategyDelayCmdImpl implements LockSkuStrategyDelayCmd {

    @Autowired
    LockSkuStrategyDelayService lockSkuStrategyDelayService;

    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        return lockSkuStrategyDelayService.execute(obj, user);
    }
}
