package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.LockSkuStrategyQueryCmd;
import com.jackrain.nea.st.model.request.StCLockSkuStrategyItemRequest;
import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2020/12/18 1:31 下午
 * @Desc:
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class LockSkuStrategyQueyCmdImpl extends CommandAdapter implements LockSkuStrategyQueryCmd {

    @Autowired
    private LockSkuStrategyQueryService lockSkuStrategyQueryService;


    @Override
    public ValueHolderV14<List<StCLockSkuStrategyItemDO>> queryLockSkuStrategyItemValid(StCLockSkuStrategyItemRequest request) {
        return lockSkuStrategyQueryService.queryLockSkuStrategyItemValid(request);
    }
}
