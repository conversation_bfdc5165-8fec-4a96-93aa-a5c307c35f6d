package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.InventoryOwnershipVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2019/8/6 13:33
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class InventoryOwnershipVoidCmdImpl extends CommandAdapter implements InventoryOwnershipVoidCmd {
    @Autowired
    private InventoryOwnershipVoidService service;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return service.execute(querySession);
    }
}

