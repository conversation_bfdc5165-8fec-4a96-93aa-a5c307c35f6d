package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.VipcomMailDelCmd;
import com.jackrain.nea.st.api.VipcomMailSaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2019/3/11 15:42
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VipcomMailDelCmdImpl extends CommandAdapter implements VipcomMailDelCmd {

    @Autowired
    private VipcomMailDelService vipcomMailDelService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return vipcomMailDelService.execute(querySession);
    }
}
