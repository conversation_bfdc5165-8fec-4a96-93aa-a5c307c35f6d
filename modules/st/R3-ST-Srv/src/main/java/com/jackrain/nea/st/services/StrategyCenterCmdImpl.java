package com.jackrain.nea.st.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.api.StrategyCenterCmd;
import com.jackrain.nea.st.model.result.EwayBillResult;
import com.jackrain.nea.st.model.result.InventorySkuOwnership;
import com.jackrain.nea.st.model.result.StCAutoCheckResult;
import com.jackrain.nea.st.model.result.StCLiveCastStrategyAllResult;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @program: r3-st
 * @author: Lijp
 * @create: 2019-07-16 13:58
 * @Descroption 外部中心调用策略中心的rpc
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StrategyCenterCmdImpl implements StrategyCenterCmd {

    @Autowired
    private StrategyCenterService strategyCenterService;

    @Autowired
    private InventoryOwnershipQueryService inventoryOwnershipQueryService;

    @Autowired
    private LiveCastStrategyService liveCastStrategyService;

    @Override
    public StCScalpingDO queryScalpingList(Long shopId, String keyWord, Date payTime) {
        return strategyCenterService.queryScalpingList(shopId, keyWord, payTime);
    }

    @Override
    public ValueHolderV14<EwayBillResult> queryEwayBillByshopId(Long shopId, Long logiscId) {
        return strategyCenterService.queryEwayBillByshopId(shopId, logiscId);
    }

    @Override
    public ValueHolderV14<List<StCShopStrategyDO>> querySendShopById(Long shopId) {
        return strategyCenterService.querySendShopById(shopId);
    }

    @Override
    public ValueHolderV14<List<StCAutoCheckDO>> queryAUtoCheckAllList() {
        return strategyCenterService.queryAllList();
    }

    @Override
    public ValueHolderV14<List<StCAutoCheckResult>> queryAUtoCheckAllListAndItem() {
        return strategyCenterService.queryAUtoCheckAllListAndItem();
    }

    //@Override
    //public ValueHolderV14<Map<Long, StCAutoCheckDO>> queryAUtoCheckAll() {
    //    return strategyCenterService.queryAutoCheckAll();
    //}

    //@Override
    //public ValueHolderV14<StCAutoCheckDO> queryAUtoCheckByShopId(Long shopId) {
    //    return strategyCenterService.queryAUtoCheckByShopId(shopId);
    //}

    //@Override
    //public ValueHolderV14<Set<Long>> queryAUtoCheckAllShopIds() {
    //    return strategyCenterService.queryAutoCheckAllShopIds();
    //}

    @Override
    public ValueHolderV14<InventorySkuOwnership> queryInventoryOwnershipV14(InventorySkuOwnership request) {
        return inventoryOwnershipQueryService.queryInventoryOwnershipV14(request);
    }

    /**
     * 查询直播解析策略
     *
     * @param cpCShopId
     * @param orderDate
     * @param payTime
     * @return
     */
    @Override
    public ValueHolderV14<List<StCLiveCastStrategyAllResult>> queryLiveCastStrategy(Long cpCShopId, Date orderDate, Date payTime) {
        ValueHolderV14<List<StCLiveCastStrategyAllResult>> vh = new ValueHolderV14<>();
        List<StCLiveCastStrategyAllResult> results = new ArrayList<>();

        List<StCLiveCastStrategyDO> liveCastStrategyDOs = liveCastStrategyService.findStrategyByShopIdAndDate(cpCShopId, orderDate, payTime);

        if (!CollectionUtils.isEmpty(liveCastStrategyDOs)) {
            liveCastStrategyDOs.forEach(live -> {
                Long strategyId = live.getId();
                List<StCLiveCastStrategyItemDO> liveCastStrategyItemDOList = liveCastStrategyService.findStrategyItemsByStrateById(strategyId);

                StCLiveCastStrategyAllResult result = new StCLiveCastStrategyAllResult();
                result.setLiveCastStrategyDO(live);
                result.setLiveCastStrategyItemDOList(liveCastStrategyItemDOList);

                // 加入列表
                results.add(result);
            });
        }

        vh.setData(results);
        vh.setCode(ResultCode.SUCCESS);

        return vh;
    }
}
