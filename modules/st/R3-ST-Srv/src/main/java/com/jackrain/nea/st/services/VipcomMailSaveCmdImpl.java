package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.VipcomMailSaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2019/3/11 13:46
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VipcomMailSaveCmdImpl extends CommandAdapter implements VipcomMailSaveCmd {

    @Autowired
    private VipcomMailSaveService vipcomMailSaveService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return vipcomMailSaveService.execute(querySession);
    }
}
