package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.DistributionDelayCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 分销代销策略延期
 * <AUTHOR>
 * @Date 2019/3/13 18:00
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class DistributionDelayCmdImpl implements DistributionDelayCmd {
    @Autowired
    private DistributionDelayService distributionDelayService;

    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        return distributionDelayService.distributionDelay(obj, user);
    }
}
