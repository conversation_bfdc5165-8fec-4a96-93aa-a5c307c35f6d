package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.StCAllocationCostQueryCmd;
import com.jackrain.nea.st.model.request.StCAllocationCostQueryRequest;
import com.jackrain.nea.st.model.result.StCAllocationCostQueryResult;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/6/9 17:50
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCAllocationCostQueryCmdImpl implements StCAllocationCostQueryCmd {

    @Autowired
    private StCAllocationCostService stCAllocationCostService;


    @Override
    public StCAllocationCostQueryResult queryAllocationCost(StCAllocationCostQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" StCAllocationCostSaveCmdImpl param：{}","调拨报价设置") , JSONObject.toJSONString(request));
        }
        return stCAllocationCostService.queryAllocationCostByCondition(request);
    }
}
