package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCCustomLabelQueryCmd;
import com.jackrain.nea.st.mapper.StCCustomLabelMapper;
import com.jackrain.nea.st.model.table.StCCustomLabelDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCCustomLabelQueryCmdImpl
 * @Description 自定义打标档案查询接口
 * <AUTHOR>
 * @Date 2025/3/10 16:39
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCCustomLabelQueryCmdImpl implements StCCustomLabelQueryCmd {

    @Autowired
    private StCCustomLabelMapper stCCustomLabelMapper;

    @Override
    public StCCustomLabelDO queryByEname(String ename) {
        return stCCustomLabelMapper.selectByEname(ename);
    }
}
