package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.VirtualHighStockQueryCmd;
import com.jackrain.nea.st.model.request.VirtualHighStockRequest;
import com.jackrain.nea.st.model.result.VirtualHighStockItemResult;
import com.jackrain.nea.st.model.result.VirtualHighStockResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Date: 2020/7/7 1:31 下午
 * @Desc:
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VirtualHighStockQueryCmdImpl implements VirtualHighStockQueryCmd {

    @Autowired
    private VirtualHighStockQueryService virtualHighStockQueryService;

    @Override
    public ValueHolderV14<VirtualHighStockItemResult> selectCurrentStock(VirtualHighStockRequest virtualHighStockRequest) {
        return virtualHighStockQueryService.selectCurrentStock(virtualHighStockRequest);
    }
}
