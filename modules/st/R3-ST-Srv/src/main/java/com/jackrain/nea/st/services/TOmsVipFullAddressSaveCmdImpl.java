package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.TOmsVipFullAddressSaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description：vip4级地址保存服务
 *
 * <AUTHOR>
 * @date 2021/08/24
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class TOmsVipFullAddressSaveCmdImpl extends CommandAdapter implements TOmsVipFullAddressSaveCmd {
    @Autowired
    private TOmsVipFullAddressSaveService tOmsVipFullAddressSaveService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return tOmsVipFullAddressSaveService.execute(session);
    }
}

