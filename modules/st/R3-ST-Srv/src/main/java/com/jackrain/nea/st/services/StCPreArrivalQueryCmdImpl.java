package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCPreArrivalQueryCmd;
import com.jackrain.nea.st.model.result.StCPreArrivalResult;
import com.jackrain.nea.st.model.table.StCPreArrivalDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2020/06/10 21:36
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreArrivalQueryCmdImpl extends CommandAdapter implements StCPreArrivalQueryCmd {
    @Autowired
    private StCPreArrivalQueryService stCPreArrivalQueryService;

    @Override
    public ValueHolderV14<List<StCPreArrivalDO>> queryStCPreArrival() {
        return stCPreArrivalQueryService.queryStCPreArrival();
    }

    @Override
    public ValueHolderV14<StCPreArrivalResult> queryStCPreArrivalItems(Long preArrivalId) {
        return stCPreArrivalQueryService.queryStCPreArrivalItems(preArrivalId);
    }

}

