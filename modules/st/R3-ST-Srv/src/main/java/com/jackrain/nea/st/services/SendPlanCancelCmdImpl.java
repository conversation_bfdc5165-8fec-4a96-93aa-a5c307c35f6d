package com.jackrain.nea.st.services;


import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.SendPlanCancelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调用BLL
 *
 * <AUTHOR> 黄火县
 * @since : 2019-03-11
 * create at : 2019-03-11 11:00
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class SendPlanCancelCmdImpl extends CommandAdapter implements SendPlanCancelCmd {
    @Autowired
    private SendPlanCancelService cancelService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return cancelService.execute(querySession);
    }
}
