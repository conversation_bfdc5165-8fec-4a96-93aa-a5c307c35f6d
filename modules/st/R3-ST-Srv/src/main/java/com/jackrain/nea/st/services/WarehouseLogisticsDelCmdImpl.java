package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.WarehouseLogisticsDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调用BLL
 *
 * <AUTHOR> 黄超
 * @since : 2019-08-15
 * create at : 2019-08-15 11:00
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class WarehouseLogisticsDelCmdImpl extends CommandAdapter implements WarehouseLogisticsDelCmd {
    @Autowired
    private WarehouseLogisticsDelService delService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return delService.execute(querySession);
    }
}
