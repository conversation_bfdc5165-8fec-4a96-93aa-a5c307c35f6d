package com.jackrain.nea.st.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StBnQueryCmd;
import com.jackrain.nea.st.mapper.StCBnProblemConfigMapper;
import com.jackrain.nea.st.mapper.StCBnWarehouseLogisticsConfigMapper;
import com.jackrain.nea.st.model.table.StCBnProblemConfigDO;
import com.jackrain.nea.st.model.table.StCBnWarehouseLogisticsConfigDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName StBnQueryCmdImpl
 * @Description 班牛查询
 * <AUTHOR>
 * @Date 2024/11/15 14:09
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StBnQueryCmdImpl extends CommandAdapter implements StBnQueryCmd {
    @Autowired
    private StCBnWarehouseLogisticsConfigMapper stCBnWarehouseLogisticsConfigMapper;
    @Autowired
    private StCBnProblemConfigMapper problemConfigMapper;

    @Override
    public ValueHolderV14<StCBnWarehouseLogisticsConfigDO> queryByWarehouseCodeAndLogisticsCode(String warehouseCode, String logisticsCode) throws NDSException {
        ValueHolderV14<StCBnWarehouseLogisticsConfigDO> valueHolderV14 = new ValueHolderV14<>();
        List<StCBnWarehouseLogisticsConfigDO> warehouseLogisticsConfigDOList = stCBnWarehouseLogisticsConfigMapper.queryByWarehouseCodeAndLogisticsCode(warehouseCode, logisticsCode);
        if (CollectionUtils.isEmpty(warehouseLogisticsConfigDOList)) {
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("success");
            return valueHolderV14;
        }
        StCBnWarehouseLogisticsConfigDO result = warehouseLogisticsConfigDOList.get(0);
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        valueHolderV14.setData(result);
        return valueHolderV14;
    }

    @Override
    public ValueHolderV14<StCBnProblemConfigDO> queryBnProblem() {
        List<StCBnProblemConfigDO> result = problemConfigMapper.selectAll();
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        valueHolderV14.setData(result);
        return valueHolderV14;
    }

    @Override
    public ValueHolderV14<StCBnProblemConfigDO> queryBnProblemByText(String problemText) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        List<StCBnProblemConfigDO> result = problemConfigMapper.selectByProblemText(problemText);
        if (CollectionUtils.isEmpty(result)) {
            return valueHolderV14;
        }
        valueHolderV14.setData(result.get(0));
        return valueHolderV14;
    }
}
