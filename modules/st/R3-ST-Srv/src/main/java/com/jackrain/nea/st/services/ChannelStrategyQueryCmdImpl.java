package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.ChannelStrategyQueryCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2019/12/10 10:29
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
@Deprecated
public class ChannelStrategyQueryCmdImpl extends CommandAdapter implements ChannelStrategyQueryCmd {
    @Autowired
    private ChannelStrategyQueryService service;

    @Override
    public ValueHolder getChannelStrategy(Long id){
        return service.getChannelStrategy(id);
    }
}
