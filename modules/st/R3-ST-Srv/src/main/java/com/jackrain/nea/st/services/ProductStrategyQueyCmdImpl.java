package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.ProductStrategyQueyCmd;
import com.jackrain.nea.st.model.request.ProductStrategyItemRequest;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2020/12/18 1:31 下午
 * @Desc: 查询店铺商品明细特殊设置
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ProductStrategyQueyCmdImpl extends CommandAdapter implements ProductStrategyQueyCmd {

    @Autowired
    private ProductStrategyItemService productStrategyItemService;


    @Override
    public ValueHolderV14<List<StCProductStrategyItemDO>> queryProductStrategyItemValid(ProductStrategyItemRequest request) {
        return productStrategyItemService.queryProductStrategyItemValid(request);
    }
}
