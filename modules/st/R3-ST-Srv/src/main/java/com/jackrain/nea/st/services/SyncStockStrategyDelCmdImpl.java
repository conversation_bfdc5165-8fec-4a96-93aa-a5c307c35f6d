//package com.jackrain.nea.st.services;
//
//import org.apache.dubbo.config.annotation.Service;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.st.api.DistributionDelCmd;
//import com.jackrain.nea.st.api.SyncStockStrategyDelCmd;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * @Descroption 店铺同步库存策略删除
// * <AUTHOR>
// * @Date 2019/3/10 10:52
// */
//@Slf4j
//@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
//public class SyncStockStrategyDelCmdImpl extends CommandAdapter implements SyncStockStrategyDelCmd {
//    @Autowired
//    private SyncStockStrategyDelService syncStockStrategyDelService;
//
//    @Override
//    public ValueHolder execute(QuerySession querySession) throws NDSException {
//        return syncStockStrategyDelService.execute(querySession);
//    }
//
//    @Override
//    public ValueHolder deleteSyncStockStrategyRedis(Long channelId) {
//        return syncStockStrategyDelService.deleteSyncStockStrategyRedis(channelId);
//    }
//}
