package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.ExpressAreaItemImportCmd;
import com.jackrain.nea.st.model.table.StCExpressAreaItemDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author:huang.zizai
 * @since: 2019/8/9
 * @create at : 2019/8/9 14:12
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ExpressAreaItemImportCmdImpl extends CommandAdapter implements ExpressAreaItemImportCmd {
    @Autowired
    private ExpressAreaItemImportService importService;

    @Override
    public ValueHolderV14 downloadTemp() {
        return importService.downloadTemp();
    }

    @Override
    public ValueHolderV14 importExpressAreaItem(Long objid, List<StCExpressAreaItemDO> expressAreaItemList, User user) {
        return importService.importExpressAreaItem(objid, expressAreaItemList, user);
    }
}
