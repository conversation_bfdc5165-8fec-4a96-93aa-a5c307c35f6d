package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.api.LiveCastStrategyApproveCmd;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 策略审核
 */
@Component
@Slf4j
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class LiveCastStrategyApproveCmdImpl implements LiveCastStrategyApproveCmd {

    @Autowired
    LiveCastStrategyService liveCastStrategyService;

    @StOperationLog(operationType = "AUDIT", mainTableName = "ST_C_LIVE_CAST_STRATEGY", itemsTableName = "ST_C_LIVE_CAST_STRATEGY_ITEM")
    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
            List<Long> ids =  itemArray.stream().map(id -> Long.valueOf(id.toString())).collect(Collectors.toList());
            liveCastStrategyService.approve(session, ids);
        } else {
            throw new NDSException("参数为空！");
        }

        return ValueHolderUtils.getSuccessValueHolder("审核成功");
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }

}
