package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.VipcomProjectPoCmd;
import com.jackrain.nea.st.model.request.VipcomProjectAndItemRequest;
import com.jackrain.nea.st.model.request.VipcomProjectNewRequest;
import com.jackrain.nea.st.model.request.VipcomProjectRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VipcomProjectPoCmdImpl implements VipcomProjectPoCmd {
    @Autowired
    private VipcomProjectPoService vipcomProjectPoService;

    @Override
    public ValueHolder execute(Long shopId) throws NDSException {
        return vipcomProjectPoService.getPoInfo(shopId);
    }

    @Override
    public ValueHolder selectVipcomProjectByShopId(Long shopId) throws NDSException {
        return vipcomProjectPoService.selectVipcomProjectByShopId(shopId);
    }

    @Override
    public VipcomProjectAndItemRequest executeOne(Long shopId, String warehouseCode, Long cpCPhyWarehouseId) {
        return vipcomProjectPoService.getOneShopVipProjectInfo(shopId, warehouseCode, cpCPhyWarehouseId);
    }
    /**
     * 根据JIT配货单的“店铺”、“发货实体仓”在档期日程规划中查找出距离当前时间往后最近的一条规划明细
     */
    @Override
    public VipcomProjectNewRequest executeNewOne(Long shopId, String warehouseCode, Long cpCPhyWarehouseId) {
        return vipcomProjectPoService.getVipcomProjectAndItemRequest(shopId,cpCPhyWarehouseId);
    }
    @Override
    public ValueHolder listVipProjectAscription(Long shopId, String warehouseCode
            , Long cpCPhyWarehouseId, String downTimeStr) {
        return vipcomProjectPoService.listVipProjectAscription(shopId, warehouseCode, cpCPhyWarehouseId, downTimeStr);
    }

    @Override
    public ValueHolder listVipProjectAscriptionWithNoDefault(Long shopId, String warehouseCode
            , Long cpCPhyWarehouseId, String downTimeStr) {
        return vipcomProjectPoService.listVipProjectAscriptionWithNoDefault(shopId, warehouseCode
                , cpCPhyWarehouseId, downTimeStr);
    }

    @Override
    public ValueHolderV14<VipcomProjectRequest> getVipcomProjectAndItem(Long shopId,
                                                                        Long ascriptionId,
                                                                        String downTimeStr) {
        return vipcomProjectPoService.getVipcomProjectAndItem(shopId, ascriptionId, downTimeStr);
    }
}
