package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.MergeOrderDeleteCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description：合单策略删除
 *
 * <AUTHOR>
 * @date 2021/5/13
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class MergeOrderDeleteCmdImpl extends CommandAdapter implements MergeOrderDeleteCmd {
    @Autowired
    private MergeOrderDeleteService service;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.execute(session);
    }
}
