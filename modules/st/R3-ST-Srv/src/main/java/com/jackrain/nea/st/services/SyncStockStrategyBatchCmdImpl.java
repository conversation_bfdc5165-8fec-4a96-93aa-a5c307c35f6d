package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.SyncStockStrategyBatchCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 陈俊明
 * @since: 2019-07-01
 * @create at : 2019-07-01 19:46
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class SyncStockStrategyBatchCmdImpl implements SyncStockStrategyBatchCmd {

    @Autowired
    SyncStockStrategyBatchService syncStockStrategyBatchService;

    @Override
    public ValueHolderV14 stockStrategyBatchChangeFun(JSONObject object, User loginUser) throws NDSException {
        return syncStockStrategyBatchService.stockStrategyBatchChangeFun(object, loginUser);
    }
}
