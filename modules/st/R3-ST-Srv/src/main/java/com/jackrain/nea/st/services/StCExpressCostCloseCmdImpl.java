package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCExpressCostCloseCmd;
import com.jackrain.nea.st.api.StCUnfullcarCostCloseCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/8/8 16:16
 * @Description
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCExpressCostCloseCmdImpl extends CommandAdapter implements StCExpressCostCloseCmd {

    @Autowired
    private StCExpressCostCloseService stCExpressCostCloseService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCExpressCostCloseService.execute(session);
    }
}
