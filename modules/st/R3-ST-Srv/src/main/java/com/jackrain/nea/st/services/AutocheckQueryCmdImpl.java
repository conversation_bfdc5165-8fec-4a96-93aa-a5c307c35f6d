package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.st.api.AutocheckQueryCmd;
import com.jackrain.nea.st.config.R3OssConfig;
import com.jackrain.nea.st.mapper.CommonLogMapper;
import com.jackrain.nea.st.mapper.StCAutoCheckAutoTimeMapper;
import com.jackrain.nea.st.model.table.StCAutoCheckAutoTimeDO;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName : AutocheckQueryLogCmdImpl  
 * @Description : 订单自动审核策略
 * <AUTHOR>  YCH
 * @Date: 2021-09-15 15:24  
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class AutocheckQueryCmdImpl implements AutocheckQueryCmd {
    @Autowired
    private CommonLogMapper commonLogMapper;
    @Autowired
    private StCAutoCheckAutoTimeMapper stCAutoCheckAutoTimeMapper;
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private R3OssConfig r3OssConfig;

    @Override
    public ValueHolderV14 getAutoCheckLog(JSONObject jsonObject) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("订单自动审核策略操作日志查询：{}"), JSON.toJSONString(jsonObject));
        }
        Long autocheckId = jsonObject.getLong("ST_C_AUTOCHECK_ID");
        List<JSONObject> jsonObjectList = commonLogMapper.selectAutocheckLog(autocheckId);
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS,"success");
        holderV14.setData(jsonObjectList);
        return holderV14;
    }

    @Override
    public ValueHolderV14 getAutoCheckTime(JSONObject param) {
        Long autocheckId;
        try {
            autocheckId = param.getLong("ST_C_AUTOCHECK_ID");
        }catch (Exception e){
            JSONArray autoCheckList = param.getJSONArray("ST_C_AUTOCHECK_ID");
            autocheckId = Long.valueOf((String)autoCheckList.get(0));
        }

        List<StCAutoCheckAutoTimeDO> stCAutoCheckAutoTimeDOS =
                stCAutoCheckAutoTimeMapper.selectList(new QueryWrapper<StCAutoCheckAutoTimeDO>()
                .lambda().eq(StCAutoCheckAutoTimeDO::getStCAutocheckId, autocheckId)
                .eq(StCAutoCheckAutoTimeDO::getIsactive, YesNoEnum.Y.getKey()));
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS,"success");
        holderV14.setData(stCAutoCheckAutoTimeDOS);
        return holderV14;
    }

    /**
     * 生成下载模板
     * @param user
     * @return
     */
    public ValueHolderV14 downloadTemp(UserImpl user) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "自动审核策略排除商品导入模板下载成功！");
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String mainNames[] = {"商品级别", "品类","商品编码","商品条码","平台商品ID","平台条码ID","备注"};
        String mustNames[] = {"商品级别"};
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);

        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "自动审核策略排除商品", "", mainList, mustList,
                Lists.newArrayList(), Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "自动审核策略排除商品导入模板",
                user, "OSS-Bucket/EXPORT/ST_C_AUTOCHECK_EXCLUDE_PRODUCT/");
        vh.setData(putMsg);
        return vh;
    }
}
