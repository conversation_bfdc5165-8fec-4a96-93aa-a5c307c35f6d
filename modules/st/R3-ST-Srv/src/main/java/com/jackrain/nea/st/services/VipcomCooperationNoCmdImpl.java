package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.VipcomCooperationNoCmd;
import com.jackrain.nea.st.model.table.StCVipcomCooperationNo;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description：常态合作编码查询
 * <AUTHOR>
 * @date 2021/7/7
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VipcomCooperationNoCmdImpl extends CommandAdapter implements VipcomCooperationNoCmd {

    @Autowired
    private VipcomCooperationNoService vipcomCooperationNoService;

    @Override
    public StCVipcomCooperationNo queryCooperationNoInfo(String ecode) {
        return vipcomCooperationNoService.queryCooperationNoInfo(ecode);
    }
}
