package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.RouterCodeQueryServiceCmd;
import com.jackrain.nea.st.mapper.StCRouterCodeMapper;
import com.jackrain.nea.st.model.table.StCRouterCodeDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption 路由操作码策略查询
 * <AUTHOR>
 * @Date 2020/5/7 15:45
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class RouterCodeQueryServiceCmdImpl implements RouterCodeQueryServiceCmd {

    @Autowired
    private StCRouterCodeMapper stCRouterCodeMapper;

    @Override
    public List<StCRouterCodeDO> selectBySysType(String sysType) {
        log.debug(LogUtil.format("RouterCodeQueryServiceCmd.selectBySysType入参：sysType=", sysType));
        List<StCRouterCodeDO> itemList = stCRouterCodeMapper.selectBySysType(sysType);
        log.debug(LogUtil.format("RouterCodeQueryServiceCmd.selectBySysType返回：") + JSONObject.toJSONString(itemList));
        return itemList;
    }

}
