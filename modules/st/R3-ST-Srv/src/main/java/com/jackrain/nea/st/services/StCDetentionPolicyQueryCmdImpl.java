package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCDetentionPolicyQueryCmd;
import com.jackrain.nea.st.model.request.StDetentionPolicyRequest;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-st
 * @description: 预售卡单查询
 * @author: liuwj
 * @create: 2021-06-19 15:39
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCDetentionPolicyQueryCmdImpl extends CommandAdapter implements StCDetentionPolicyQueryCmd {

    @Autowired
    private StCDetentionPolicyQueryService stCDetentionPolicyQueryService;

    @Override
    public StDetentionPolicyRequest queryStCDetentionPolicyByShopId(Long shopId) {
        return stCDetentionPolicyQueryService.queryStCDetentionPolicyByShopId(shopId);
    }
}
