package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.ExpressAreaDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 物流区域设置删除
 * <AUTHOR>
 * @Date 2019/3/13 14:32
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ExpressAreaDelCmdImpl extends CommandAdapter implements ExpressAreaDelCmd {
    @Autowired
    private ExpressAreaDelService expressAreaDelService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return expressAreaDelService.execute(querySession);
    }
}
