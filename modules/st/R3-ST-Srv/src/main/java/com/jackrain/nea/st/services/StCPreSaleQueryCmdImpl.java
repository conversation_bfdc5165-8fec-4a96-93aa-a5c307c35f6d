package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCPreSaleQueryCmd;
import com.jackrain.nea.st.model.result.StCPreSaleResult;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2020/06/10 21:36
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreSaleQueryCmdImpl extends CommandAdapter implements StCPreSaleQueryCmd {
    @Autowired
    private StCPreSaleQueryService stCPreSaleQueryService;

    @Override
    public ValueHolderV14<List<StCPreSaleResult>> queryStCPreSale(Long shopId, Integer preSaleWay) {
        return stCPreSaleQueryService.queryStCPreSale(shopId, preSaleWay);
    }
}

