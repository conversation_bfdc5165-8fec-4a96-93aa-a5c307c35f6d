package com.jackrain.nea.st.services;


import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.ExpressAllocationCheckCmd;
import com.jackrain.nea.st.model.request.ExpressAllocationRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * 调用BLL
 *
 * <AUTHOR> 黄火县
 * @since : 2019-03-11
 * create at : 2019-03-11 11:00
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ExpressAllocationCheckCmdImpl implements ExpressAllocationCheckCmd {
    @Autowired
    private ExpressAllocationCheckService checkService;

    @Override
    public List<Long> getLogisticInfoByWarehouseId(Long warehouseId) throws NDSException {
        return checkService.getLogisticInfoByWarehouseId(warehouseId);
    }

    @Override
    public Boolean checkLogisticRules(Long warehouseId, Long logisticId) throws NDSException {
        return checkService.checkLogisticRules(warehouseId, logisticId);
    }

    @Override
    public HashMap<String, Object>  selectExpressByPhyWarehouse(ExpressAllocationRequest skuQueryRequest) {
        return checkService.selectExpressByPhyWarehouse(skuQueryRequest);
    }
}
