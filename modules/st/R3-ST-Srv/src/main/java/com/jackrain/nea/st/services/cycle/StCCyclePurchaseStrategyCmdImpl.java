package com.jackrain.nea.st.services.cycle;


import com.jackrain.nea.st.api.cycle.StCCyclePurchaseStrategyCmd;
import com.jackrain.nea.st.model.request.cycle.StCCyclePurchaseStrategyQueryRequest;
import com.jackrain.nea.st.model.result.cycle.StCCyclePurchaseStrategyResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Auther: chenhao
 * @Date: 2022-09-05 10:17
 * @Description:
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCCyclePurchaseStrategyCmdImpl implements StCCyclePurchaseStrategyCmd {

    @Autowired
    private StCCyclePurchaseStrategyService strategyService;

    @Override
    public ValueHolderV14<List<StCCyclePurchaseStrategyResult>> queryCyclePurchaseStrategyByProCode(StCCyclePurchaseStrategyQueryRequest request) {
        return strategyService.queryCyclePurchaseStrategyByProCode(request);
    }

    @Override
    public ValueHolderV14<StCCyclePurchaseStrategyResult> queryCyclePurchaseStrategyById(StCCyclePurchaseStrategyQueryRequest request) {
        return strategyService.queryCyclePurchaseStrategyById(request);
    }
}
