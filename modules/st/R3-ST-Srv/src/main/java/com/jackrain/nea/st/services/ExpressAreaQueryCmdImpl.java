package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.ExpressAreaQueryCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 物流区域查询
 * <AUTHOR>
 * @Date 2019/8/8 14:32
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ExpressAreaQueryCmdImpl implements ExpressAreaQueryCmd {
    @Autowired
    private ExpressAreaQueryService service;

    @Override
    public ValueHolderV14 queryExpressAreaTree(JSONObject obj) {
        return service.queryExpressAreaTree(obj);
    }

    @Override
    public ValueHolderV14 queryExpressAreaItemTable(JSONObject obj) {
        return service.queryExpressAreaItemTable(obj);
    }

    @Override
    public ValueHolderV14 queryExpressAreaItemLikeTable(JSONObject obj) {
        return service.queryExpressAreaItemLikeTable(obj);
    }
}
