package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.PriceFinishCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 商品价格策略-结案接口
 * <AUTHOR>
 * @Date 2019/3/21 17:29
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class PriceFinishCmdImpl extends CommandAdapter implements PriceFinishCmd {
    @Autowired
    private PricefinishService service;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.execute(session);
    }
}
