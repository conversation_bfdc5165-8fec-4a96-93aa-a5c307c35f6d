package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StAppointBatchSaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2022/6/9 下午1:30
 * @Version 1.0
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StAppointBatchSaveCmdImpl extends CommandAdapter implements StAppointBatchSaveCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return null;
    }
}
