package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.PickUpTimeSaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 上门取件时间策略新增保存
 * <AUTHOR>
 * @Date 2019/3/11 20:52
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class PickUpTimeSaveCmdImpl extends CommandAdapter implements PickUpTimeSaveCmd {
    @Autowired
    private PickUpTimeSaveService pickUpTimeSaveService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return pickUpTimeSaveService.execute(querySession);
    }
}
