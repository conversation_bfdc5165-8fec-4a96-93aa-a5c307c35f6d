package com.jackrain.nea.st.services;


import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.StAllLogCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 查找策略日志
 * <AUTHOR>
 * @Date 2010/05/18
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StAllLogCmdImpl implements StAllLogCmd {
    @Autowired
    private StAllLogService stAllLogService;

    @Override
    public ValueHolder selectLog(JSONObject json, User user) {
        return stAllLogService.selectLog(json, user);
    }
}
