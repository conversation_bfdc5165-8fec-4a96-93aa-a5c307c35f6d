package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.PickUpTimeVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 上门取件时间策略作废
 * <AUTHOR>
 * @Date 2019/3/12 14:20
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class PickUpTimeVoidCmdImpl extends CommandAdapter implements PickUpTimeVoidCmd {
    @Autowired
    private PickUpTimeVoidService pickUpTimeVoidService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return pickUpTimeVoidService.execute(querySession);
    }
}
