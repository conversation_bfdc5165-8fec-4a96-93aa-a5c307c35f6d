//package com.jackrain.nea.st.services;
//
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.st.api.StCShopLockSyncStockCmd;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Service;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.HashMap;
//
///**
// * <AUTHOR>
// * @Date: 2020/7/8 10:34 下午
// * @Desc: 店铺锁库策略修改比例同步库存
// */
//@Slf4j
//@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
//public class StCShopLockSyncStockCmdImpl implements StCShopLockSyncStockCmd {
//    @Autowired
//    private ShopLockStockSyncStockService lockService;
//
//    @Override
//    public ValueHolder execute(QuerySession session) throws NDSException {
//        return lockService.execute(session);
//    }
//
//    @Override
//    public ValueHolder execute(HashMap map) throws NDSException {
//        return null;
//    }
//}
