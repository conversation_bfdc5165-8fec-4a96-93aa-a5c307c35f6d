package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.JdDistributionLogisticsCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName JdDistributionCmdImpl
 * @Description 京东分销商
 * <AUTHOR>
 * @Date 2022/12/12 13:47
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class JdDistributionLogisticsCmdImpl extends CommandAdapter implements JdDistributionLogisticsCmd {

    @Autowired
    private JdDistributionLogisticsService logisticsService;

    @Override
    public ValueHolderV14<List<Long>> getLogisticsIdsByDistributionId(Long shopId, String jdDistributionId) {
        return logisticsService.getLogisticsIdsByDistributionId(shopId, jdDistributionId);
    }
}
