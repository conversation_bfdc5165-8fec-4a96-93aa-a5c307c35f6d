package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.PickUpTimeQueryCmd;
import com.jackrain.nea.st.model.request.PickUpTimeQueryRequest;
import com.jackrain.nea.st.model.table.StCPickUpTimeDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调用BLL
 *
 * <AUTHOR> 黄超
 * @since : 2019-08-02
 * create at : 2019-08-02 14:00
 */

@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class PickUpTimeQueryCmdImpl implements PickUpTimeQueryCmd {
    @Autowired
    private PickUpTimeQueryService service;

    @Override
    public ValueHolderV14<StCPickUpTimeDO> selectTimeByWarehouseLogistics(PickUpTimeQueryRequest request) throws NDSException {
        return service.selectTimeByWarehouseLogistics(request);
    }
}
