package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.AuditMarkQueryCmd;
import com.jackrain.nea.st.model.table.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * @description: 订单自动审核-标识审核等待时间列表操作
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2020-06-12
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class AuditMarkQueryCmdImpl implements AuditMarkQueryCmd {

    @Autowired
    private AuditMarkQueryService auditMarkQueryService;

    @Override
    public List<StCAutocheckAuditMarkDO> selectCStAutditMarkItemInfo(Long sTAutocheckId) {
        return auditMarkQueryService.selectCStAutditMarkItemInfo(sTAutocheckId);
    }
}
