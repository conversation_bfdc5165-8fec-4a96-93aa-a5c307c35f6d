package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.SellOwnGoodsDelayCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 经销商自有商品延期
 * @Author: 黄超
 * @Date: 2019/4/29
 */

@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class SellOwnGoodsDelayCmdImpl implements SellOwnGoodsDelayCmd {
    @Autowired
    private SellOwnGoodsDelayService sellOwnGoodsDelayService;

    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        return sellOwnGoodsDelayService.sellOwnGoodsDelay(obj, user);
    }
}
