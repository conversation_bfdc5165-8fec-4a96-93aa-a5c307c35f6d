package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.VipcomAscriptionProvideCmd;
import com.jackrain.nea.st.model.table.StCVipcomAscriptionDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 日程归属接口
 * <AUTHOR>
 * @Date 2019/3/27 19:04
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VipcomAscriptionProvideCmdImpl implements VipcomAscriptionProvideCmd {
    @Autowired
    private VipcomAscriptionProvideService service;

    @Override
    public ValueHolderV14<StCVipcomAscriptionDO> selectVipcomById(Long id) throws NDSException {
        return service.selectVipcomById(id);
    }

}
