package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.ResolveRuleCheckCmd;
import com.jackrain.nea.st.model.table.StCResolveRuleDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption XXX
 * <AUTHOR>
 * @Date 2019-04-09 20:45
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ResolveRuleCheckCmdImpl implements ResolveRuleCheckCmd {

    @Autowired
    private ResolveRuleService resolveRuleService;
    @Override
    public List<StCResolveRuleDO> selectByPlatformId(Long platformId,JSONArray idsArray) throws NDSException {

        List<StCResolveRuleDO> stCResolveRuleDO = resolveRuleService.selectByPlatformId(platformId,idsArray);

        return stCResolveRuleDO;
    }

    @Override
    public Long selectPlatformIdById(Long id) throws NDSException {
        return resolveRuleService.selectPlatformIdById(id);
    }
}
