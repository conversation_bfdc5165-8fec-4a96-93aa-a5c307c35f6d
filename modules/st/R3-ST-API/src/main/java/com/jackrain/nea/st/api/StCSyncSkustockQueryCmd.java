package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.request.SyncSkuStockItemRequest;
import com.jackrain.nea.st.model.result.SyncSkuStockItemResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/26
 * create at : 2020/8/26 1:48
 */
public interface StCSyncSkustockQueryCmd {

    /**
     * 根据店铺，skuID,有效时间查询是否存在
     * @param syncSkuStockItemRequest
     * @return
     */
    ValueHolderV14<List<SyncSkuStockItemResult>> selectCurrentStock(SyncSkuStockItemRequest syncSkuStockItemRequest);
}
