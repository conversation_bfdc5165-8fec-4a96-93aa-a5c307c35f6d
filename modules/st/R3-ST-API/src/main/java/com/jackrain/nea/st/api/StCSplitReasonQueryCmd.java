package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.request.StCSplitReasonRequest;
import com.jackrain.nea.sys.Command;

import java.util.List;

public interface StCSplitReasonQueryCmd extends Command {
   /**
    * <AUTHOR>
    * @Date 14:22 2021/6/2
    * @Description 根据系统拆单原因来查询
    */
   List<StCSplitReasonRequest>  queryStCSplitReasonBySplitReason(String systemSplitReason);
   /**
    * <AUTHOR>
    * @Date 14:23 2021/6/2
    * @Description 查询所有的拆单原因
    */
   List<StCSplitReasonRequest>  queryStCSplitReasonAll();
}
