package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.request.StCProLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.table.StCProLogisticStrategy;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/6/21 14:46
 * @Description:  商品物流设置查询
 */
public interface StCProLogisticStrategyQueryCmd extends Command {
    ValueHolderV14<List<StCProLogisticStrategy>> queryStCProLogisticStrategy(StCProLogisticStrategyQueryRequest request);
}
