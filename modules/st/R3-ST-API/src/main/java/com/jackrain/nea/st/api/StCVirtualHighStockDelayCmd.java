package com.jackrain.nea.st.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;


/**
 * <AUTHOR>
 * @Date: 2020/6/23 3:15 下午
 * @Desc: 店铺虚高库存设置反延期
 */
public interface StCVirtualHighStockDelayCmd extends Command {
    ValueHolder batchDelayEndTime(QuerySession querySession) throws NDSException;
}
