package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 派单策略服务接口
 * @author: 汪聿森
 * @date: 2019-05-21
 */
public interface SendPlanRuleQueryServiceCmd {
    /**
     * 查找派单方案
     *
     * @param shopId      店鋪ID
     * @param currentDate 当前日期
     * @return
     */
    List<Long> selectSendPlanList(Long shopId, Date currentDate);

    /**
     * 在AD_PARAM表中先取出 指定系统参数key所对应的所有的值
     *
     * @param name
     * @return
     */
    AdParam selectSysParamValue(String name);

    /**
     * 根据方案查找派单规则明细
     *
     * @param planId 方案Id
     * @return
     */
    List<Long> selectSendPlanItemList(Long planId);

    /**
     * 根据方案查找派单规则明细
     *
     * @param planId 方案Id
     * @return
     */
    List<StCSendPlanItemDO> selectSendPlanItemListByPlanId(Long planId);

    /**
     * 根据实体仓库去匹配仓库发货比例
     *
     * @param warehouseList 实体发货仓库id集合
     * @param sendRuleId    派单规则id
     * @return
     */
    List<StCSendRuleWarehouseRateDO> selectWarehouseRateMapper(List<Long> warehouseList, Long sendRuleId);

    /**
     * 统计发货仓库发货数量
     *
     * @param warehouseList 实体发货仓库id集合
     * @param sendRuleId    派单规则id
     * @return
     */
    BigDecimal selectQtySendTotal(List<Long> warehouseList, Long sendRuleId);

    /**
     * 更新发货仓库数量
     *
     * @param warehouseId 发货仓库Id
     * @return
     */
    Integer updateRuleWarehouseRate(Long warehouseId);

    /**
     * 根据优先级和创建时间排序
     *
     * @param sendPlanList 方案Id集合
     * @return
     */
    List<Long> querySendPlanByActiveList(List<Long> sendPlanList);

    /**
     * 根据规则ID查找派单规则
     *
     * @param sendRuleId 规则Id
     * @return
     */
    StCSendRuleDO selectSendRuleType(Long sendRuleId);

    /**
     * 查找分仓比例的规则
     *
     * @param sendRuleIds 规则Id集合
     * @param type        规则类型
     * @return
     */
    Long selectSendRuleIds(List<Long> sendRuleIds, String type);

    /**
     * 查找分仓比例的规则
     *
     * @param sendRuleIds 规则Id集合
     * @return
     */
    List<StCSendRuleDO> selectSendRuleByIdList(List<Long> sendRuleIds);

    /**
     * 查找地址就近的返回仓库 根据rank和创建时间排序
     *
     * @param cpPhyWarehouseList 实体仓list
     * @param cRegionProvinceId  省份Id
     * @param sendRuleId         规则ID
     * @return List<OcStCSendRuleAddressRent>
     */
    List<Long> selectAddressWarehouseList(List<Long> cpPhyWarehouseList,
                                          Long cRegionProvinceId, Long sendRuleId);

    /**
     * 查找地址就近的返回仓库优先级
     *
     * @param cRegionProvinceId 省份Id
     * @param sendRuleId        规则ID
     * @return List<StCSendRuleAddressRankDO>
     */
    List<StCSendRuleAddressRankDO> selectSendRuleAddressRankList(Long cRegionProvinceId, Long sendRuleId);

    /**
     * 查找按唯品会的返回仓库优先级
     *
     * @param sendRuleId         规则ID
     * @param cpCVipcomWahouseId 唯品会仓库ID
     * @return
     */
    List<StCSendRuleAddressVipDo> selectSendRuleAddressVoList(Long sendRuleId, Long cpCVipcomWahouseId);

    /**
     * 查找派单方案
     *
     * @param shopId 店鋪ID
     * @return
     */
    List<StCSendPlanDO> selectSendPlanListByShopId(Long shopId);


    /**
     * 根据店铺id查询唯品会派单规则
     *
     * @param shopId 店铺id
     * @return list
     */
    List<StCSendRuleAddressVipDo> findRuleAddressVipByShopId(Long shopId, String jitCode);
}
