/*
package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.request.SyncStockStrategyImportRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.io.IOException;
import java.util.List;

*/
/**
 * 同步库存策略导入cmd
 *
 * <AUTHOR>
 * @since 2020-08-18
 * create at : 2020-08-18 17:32
 *//*

public interface SyncStockStrategyImportCmd {

    */
/**
     * 同步库存库存策略模版下载
     *
     * @param user 当前操作用户
     * @return 结果对象
     * @throws IOException IO异常
     *//*

    ValueHolderV14 downloadTemp(User user) throws IOException;

    */
/**
     * 导入同步库存策略
     *
     * @param importList 导入列表
     * @param user       当前操作用户
     * @return 结果对象
     *//*

    ValueHolderV14 importSyncStockStrategy(List<SyncStockStrategyImportRequest> importList, User user);
}*/
