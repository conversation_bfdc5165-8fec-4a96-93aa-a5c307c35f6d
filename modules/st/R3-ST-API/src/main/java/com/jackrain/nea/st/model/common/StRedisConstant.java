package com.jackrain.nea.st.model.common;

/**
 * Redis 常量类
 * 
 * <AUTHOR>
 * @since 2023/5/13
 */
public class StRedisConstant {
    
    /**
     * 根据实体仓id和物流公司id查询快递报价关系
     */
    public static final String ST_C_EXPRESS_PRICE_RELATION_KEY = "st:express:price:relation:warehouse:";

    /**
     * 构建快递报价关系缓存key
     * @param warehouseId 实体仓id
     * @param logisticsId 物流公司id
     * @return Redis缓存key
     */
    public static String buildExpressPriceRelationKey(Long warehouseId, Long logisticsId) {
        return ST_C_EXPRESS_PRICE_RELATION_KEY + warehouseId + ":logistics:" + logisticsId;
    }
}
