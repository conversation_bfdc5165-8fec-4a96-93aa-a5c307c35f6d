package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.result.DistributionQueryResult;
import com.jackrain.nea.st.model.table.StCOperationcostDO;
import com.jackrain.nea.st.model.table.StCPostfeeDO;
import com.jackrain.nea.st.model.table.StCPostfeeItemDO;
import com.jackrain.nea.st.model.table.StCPostfeeWarehouseDO;

import java.util.Date;
import java.util.List;

public interface OtherFeesQueryServiceCmd {

    List<StCOperationcostDO> selectStCOperationcostInfo(Integer planType, Long cpCPhyWarehouseId );
    List<StCPostfeeDO> searchEffectiveFreightPlan(Date currentDate);
    List<StCPostfeeItemDO> searchStCPostfeeItem(Long stCPostfeeId, Long logisticsId,Long provinceId, Long cityId, Long areaId);
    List<StCPostfeeWarehouseDO> searchStCPostfeeWarehouse(Long stCPostfeeId);
    DistributionQueryResult searchDistributionByManage(Long manageIdSeller, Long manageId, Date currentDate);
}
