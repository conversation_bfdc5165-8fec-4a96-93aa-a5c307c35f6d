package com.jackrain.nea.st.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.model.result.MergeOrderStrategyResult;
import com.jackrain.nea.st.model.result.StCPriceResult;
import com.jackrain.nea.st.model.result.StCShopStrategyLogisticsItemResult;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.model.result.StCAutoCheckResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.math.BigDecimal;
import java.util.List;

public interface ShopStrategyQueryServiceCmd {
    ValueHolderV14<StCShopStrategyDO> queryShopStrategyById(Long id)throws NDSException;

    StCShopStrategyDO selectOcStCShopStrategyByCpCshopId(Long id);

    List<String> selectDiffpriceskuList(Long shopId, List<String> skuEcodeList);

    List<StCShopStrategyItemDO> queryShopStrategyItem(Long shopId);

    List<Long> queryPriceList(Long shopId);

    List<BigDecimal> queryPriceTotal(List<Long> stCPriceList, Long proId);

    StCAutoCheckDO queryOcStCAutocheck(Long shopId);

    StCAutoCheckResult queryOcStCAutoCheckWithItems(Long shopId);

    StCAutoInvoiceDO queryStCAutoInvoice(Long shopId);

    List<StCAutoInvoiceDO> queryAllAutoInvoice();

    boolean isExitsRefundOrderStrategy(Long shopId);

    boolean isDifferenPriceSku(Long shopId, String skuEcode);

    Long queryDefaultWarehouse(Long shopId);

    List<StCMergeOrderDO> queryAllMergeOrder();

    /**
     * description：斯凯奇项目合单策略-新增品类限制需求 增加品类限制明细查询返回
     *
     * <AUTHOR>
     * @date 2021/5/13
     */
    List<MergeOrderStrategyResult> queryAllMergeOrderInfo();

    //Set<Long> queryMergeShopAllShopIds();

    //Map<Long, StCMergeOrderDO> queryAllMergeShop();

    //StCMergeOrderDO queryMergeShopByShopId(Long shopId);

    List<StCShopStrategyDO> queryAllShopStrategy();

    List<StCShopStrategyDO> queryOrderLoadShopStrategy();

    List<StCExchangeStrategyOrderDO> queryAllExchangeShopStrategy();

    StCOrderPriceItemDO queryStCOrderPriceItemByPriceId(Long priceId,String policyType);

    ValueHolderV14<StCPriceResult> queryPricesByShopId(Long shopId);

    /**
     * 根据店铺id 查询物流公司集合
     * @param shopId
     * @return
     */
    StCShopStrategyLogisticsItemResult queryShopStrategyLogisticsList(Long shopId);
}
