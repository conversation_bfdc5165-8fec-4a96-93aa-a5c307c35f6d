package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.request.StCExpressPriceStrategyQueryRequest;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyRelationQueryRequest;
import com.jackrain.nea.st.model.result.StCExpressPriceStrategyQueryResult;
import com.jackrain.nea.st.model.result.StCExpressPriceStrategyRelation;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/9 16:20
 * @Description 快递报价设置保存
 * @Version 1.0
 */
public interface StCExpressPriceStrategyQueryCmd {

    /**
     * 根据 省 仓库找到配置了 快递报价 的物流信息
     *
     * @param request request
     * @return
     */
    List<StCExpressPriceStrategyQueryResult> queryStCExpressPriceByParams(StCExpressPriceStrategyQueryRequest request);

    /**
     * 根据
     *
     * @param request
     * @return
     */
    List<StCExpressPriceStrategyRelation> queryExpressPriceRelation(StCExpressPriceStrategyRelationQueryRequest request);
}
