package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCOwnershipWarehouseSetDO;
import com.jackrain.nea.sys.Command;

import java.util.List;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-02-19 11:46
 * @Description : 库存归属仓库属性设置同步
 */
public interface OwnershipWarehouseSetSyncCmd   extends Command {

    /**
     * 根据仓库属性查询供货仓信息
     * @param channelType
     * @return
     */
    List<StCOwnershipWarehouseSetDO> selectByChannelType(Long channelType);
}
