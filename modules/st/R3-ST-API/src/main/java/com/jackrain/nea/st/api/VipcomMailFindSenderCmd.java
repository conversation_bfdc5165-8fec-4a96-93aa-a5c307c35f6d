package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.result.MailInfoResult;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR>
 * @Description 查询邮件模板信息接口
 * @Date  2019-5-13
**/
public interface VipcomMailFindSenderCmd extends Command {
    ValueHolderV14<MailInfoResult> querySenderByNodeAndShop(Integer taskNode, Long shopId, Long wareHouseId);
}
