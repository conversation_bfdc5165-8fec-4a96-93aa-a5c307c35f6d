package com.jackrain.nea.st.api;

import com.jackrain.nea.st.request.StCUnfullcarCostArrivalDaysRequest;
import com.jackrain.nea.st.result.StCUnfullcarCostArrivalDaysResponse;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * 零担报价明细查询
 *
 * <AUTHOR>
 * @since 2024-02-27 10:20
 */

public interface StCUnfullcarCostItemQueryCmd {
    ValueHolderV14<StCUnfullcarCostArrivalDaysResponse> queryArrivalDays(StCUnfullcarCostArrivalDaysRequest request);
}
