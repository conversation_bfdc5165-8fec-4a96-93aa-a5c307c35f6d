package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCVipcomCooperationNo;
import com.jackrain.nea.sys.Command;

/**
 * description：常态合作编码查询接口
 * <AUTHOR>
 * @date 2021/7/7
 */
public interface VipcomCooperationNoCmd extends Command {

    /**
     /**
     * 根据常态合作编码查询常态合作编码信息
     * @param ecode 常态合作编码
     * @return stCVipcomCooperationNo
     * @Date 19:43 2021/7/7
     * @return cStCVipcomCooperationNo
     **/
    StCVipcomCooperationNo queryCooperationNoInfo(String ecode);

}
