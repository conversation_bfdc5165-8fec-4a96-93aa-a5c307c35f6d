package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCLiveCastStrategyDO;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 17:00 2021/4/6
 * @Description  直播策略列表查询
 */
public interface LiveCastStrategyQueryListCmd extends Command {

    ValueHolderV14 queryListById(Long objId);

    List<StCLiveCastStrategyDO> queryLiveCastStrategyByAnchorArchivesId(Long anchorArchivesId);
}
