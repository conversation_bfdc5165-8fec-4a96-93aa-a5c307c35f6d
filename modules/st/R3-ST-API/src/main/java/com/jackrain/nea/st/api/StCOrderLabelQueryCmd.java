package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.request.StCDepositPreSaleSinkRequest;
import com.jackrain.nea.st.model.request.StCOrderLabelRequest;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/11/30 10:48
 */
public interface StCOrderLabelQueryCmd {

    List<StCOrderLabelRequest> queryDepositPreSaleSink(Long cpShopId);


    ValueHolderV14<List<StCOrderLabelRequest>> selectOrderLabel(Long cpShopId);
}
