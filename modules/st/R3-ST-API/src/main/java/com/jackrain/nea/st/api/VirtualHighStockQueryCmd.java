package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.request.VirtualHighStockRequest;
import com.jackrain.nea.st.model.result.VirtualHighStockItemResult;
import com.jackrain.nea.st.model.result.VirtualHighStockResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;


/**
 * <AUTHOR>
 * @Date: 2020/7/7 1:30 下午
 * @Desc:
 */
public interface VirtualHighStockQueryCmd {
    /**
     * 根据店铺，skuID,有效时间查询是否存在
     * @param virtualHighStockRequest
     * @return
     */
    ValueHolderV14<VirtualHighStockItemResult> selectCurrentStock(VirtualHighStockRequest virtualHighStockRequest);
}
