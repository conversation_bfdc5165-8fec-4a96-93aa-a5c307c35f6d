package com.jackrain.nea.st.model.enums;

/**
 * <AUTHOR>
 * @Date 2022/9/8 10:09
 * @Description
 */
public enum ExpressCostTypeEnum {

    UNFULLCAR(0, "零担"),

    EXPRESS_PRICE(1, "快递"),

    EXPRESS_COST(2,"快运");

    public Integer getval() {
        return this.val;
    }

    public String getTxt() {
        return this.txt;
    }

    Integer val;

    String txt;

    ExpressCostTypeEnum(Integer val, String desc) {
        this.val = val;
        this.txt = desc;
    }
}
