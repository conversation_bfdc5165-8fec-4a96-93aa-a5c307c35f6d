package com.jackrain.nea.st.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.query.QuerySessionImpl;

import java.util.List;

/**
 * @Descroption 分销代销方案复制
 * <AUTHOR>
 * @Date 2019/5/7 14:01
 */
public interface DistributionCopyCmd{
    ValueHolderV14<List<StCDistributionDO>> execute(QuerySessionImpl querySession) throws NDSException;
}
