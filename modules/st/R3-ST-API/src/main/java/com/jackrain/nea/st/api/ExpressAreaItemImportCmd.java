package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCExpressAreaItemDO;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author:huang.z<PERSON><PERSON>
 * @since: 2019/8/9
 * @create at : 2019/8/9 21:42
 */
public interface ExpressAreaItemImportCmd extends Command {
    /**
     * 下载导入模板接口
     * @return
     */
    public ValueHolderV14 downloadTemp();

    /**
     * 导入
     * @return
     */
    public ValueHolderV14 importExpressAreaItem(Long objid, List<StCExpressAreaItemDO> express<PERSON>reaItemList, User user);

}
