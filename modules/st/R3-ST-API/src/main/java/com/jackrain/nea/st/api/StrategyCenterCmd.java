package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.result.EwayBillResult;
import com.jackrain.nea.st.model.result.InventorySkuOwnership;
import com.jackrain.nea.st.model.result.StCAutoCheckResult;
import com.jackrain.nea.st.model.result.StCLiveCastStrategyAllResult;
import com.jackrain.nea.st.model.table.StCAutoCheckDO;
import com.jackrain.nea.st.model.table.StCScalpingDO;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @program: r3-st
 * @author: Lijp
 * @create: 2019-07-16 13:50
 * @Descroption 外部中心调用策略中心的rpc
 */

public interface StrategyCenterCmd {

    /**
     * 根据店铺id查询该店铺下的所有刷单策略
     *
     * @param shopId
     * @return
     */
    StCScalpingDO queryScalpingList(Long shopId, String keyWord, Date payTime);

    /**
     * 根据店铺id 物流公司id 查询电子面单信息
     *
     * @param shopId
     * @param logiscId
     * @return
     */
    ValueHolderV14<EwayBillResult> queryEwayBillByshopId(Long shopId, Long logiscId);

    /**
     * 根据店铺id 查询店铺是否可以进行强制平台发货
     * @param shopId
     * @return
     */
    ValueHolderV14<List<StCShopStrategyDO>>  querySendShopById(Long shopId);

    /**
     * 查询所有自动审单策略
     * @return
     */
    ValueHolderV14<List<StCAutoCheckDO>> queryAUtoCheckAllList();

    /**
     * 查询所有自动审单策略
     * @return
     */
    ValueHolderV14<List<StCAutoCheckResult>> queryAUtoCheckAllListAndItem();

    //ValueHolderV14<Map<Long,StCAutoCheckDO>> queryAUtoCheckAll();

    //ValueHolderV14<StCAutoCheckDO> queryAUtoCheckByShopId(Long shopId);
    /**
     * 查询所有自动审单策略
     * @return
     */
    //ValueHolderV14<Set<Long>> queryAUtoCheckAllShopIds();

    /**
     * 查询库存归属策略
     * @return
     */
    ValueHolderV14<InventorySkuOwnership> queryInventoryOwnershipV14(InventorySkuOwnership request);

    /**
     * 查询直播解析策略
     * @param cpCShopId
     * @param orderDate
     * @param payTime
     * @return
     */
    ValueHolderV14<List<StCLiveCastStrategyAllResult>> queryLiveCastStrategy(Long cpCShopId, Date orderDate, Date payTime);
}
