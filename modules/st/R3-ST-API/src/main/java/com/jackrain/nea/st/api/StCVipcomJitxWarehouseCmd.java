package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.util.ValueHolder;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 唯品会jitx仓库对照表
 * @Date 2020-08-12
 **/
public interface StCVipcomJitxWarehouseCmd {
    /**
     * 获取指定JITX仓库的产能信息
     *
     * @param shopId
     * @param cpCPhyWarehouseId
     * @param vipcomWarehouseEcode
     * @return
     */
    StCVipcomJitxWarehouse execute(Long shopId, Long cpCPhyWarehouseId, String vipcomWarehouseEcode);

    /**
     * 根据店铺查询唯品会仓库对照表
     *
     * @param shopId
     * @return
     */
    List<StCVipcomJitxWarehouse> selectVipWarehouseListByShopId(Long shopId);

    List<StCVipcomJitxWarehouse> selectByShopIdAndVipcomUnshopWarehouseEcode(Long shopId, String vipcomUnshopWarehouseEcode);


    StCVipcomJitxWarehouse selectVipcomWarehouse(StCVipcomJitxWarehouse stCVipcomJitxWarehouse);

    /**
     * 更新指定JITX仓库产能
     *
     * @param jitxWarehouse
     * @param calculationType
     * @return
     */
    public ValueHolder updateJitxCapacity(StCVipcomJitxWarehouse jitxWarehouse, Integer calculationType);
}
