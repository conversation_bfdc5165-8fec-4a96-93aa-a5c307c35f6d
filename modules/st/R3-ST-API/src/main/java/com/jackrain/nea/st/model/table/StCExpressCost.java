package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import java.util.Date;
import lombok.Data;

@TableName(value = "st_c_express_cost")
@Data
public class StCExpressCost extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "START_DATE")
    private Date startDate;

    @JSONField(name = "END_DATE")
    private Date endDate;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "SUBMITTERID")
    private Long submitterid;

    @JSONField(name = "SUBMIT_TIME")
    private Date submitTime;

    @JSONField(name = "STATUS")
    private Integer status;

    @JSONField(name = "CLOSERID")
    private Long closerid;

    @JSONField(name = "CLOSE_TIME")
    private Date closeTime;

    @JSONField(name = "CLOSE_STATUS")
    private Integer closeStatus;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}