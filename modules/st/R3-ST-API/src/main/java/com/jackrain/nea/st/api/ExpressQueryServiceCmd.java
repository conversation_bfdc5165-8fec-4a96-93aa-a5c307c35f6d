package com.jackrain.nea.st.api;

import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.st.model.request.ExpressAreaRequest;
import com.jackrain.nea.st.model.table.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public interface ExpressQueryServiceCmd {

    boolean updateLogisticsNum(Long phyWarehouseId, Long logisticsId);

    int queryLogisticsRule(Long cpClogisticsId, Long cpCphyWarehouseId);

    Long countProLimitNumber(Long cpCphyWarehouseId, Long cpCogisticsId);

    List<StCExpressDO> selectStCExpressByCpCShopId(Date orderDate, Date currentDate);

    List<StCExpressAllocationItemDO> selectLogisticsIdBycpCPhyWarehouseId(Long cpCPhyWarehouseId, BigDecimal qtyAll);

    List<StCExpressPackageDO> selectStCExpressPackageInfo(Long stCExpressId);

    List<StCExpressPlanAreaItemDO> selectStCExpressPlanAreaItem(Long stCExpressId);

    List<StCExpressProItemDO> selectStCExpressProItem(Long stCExpressId);

    List<StCExpressAllocationItemDO> selectStCExpressAllocationItemByLogisticsId(List<Long> stCExpressAreaList);

    List<StCExpressWarehouseItemDO> selectStCExpressWarehouseItemInfo(Long stCExpressId);

    List<ExpressAreaRequest> selectLogisticsIdInfoByProvinceCityArea(Long provinceId, Long cityId, Long areaId);

    BigDecimal countSendsum(Long logisticsId);

    List<HashMap> selectExpressResult();

    /**
     * 根据店铺ID获取物流信息
     * @param cpCShopId
     * @return
     */
    List<Long> selectLogisticsIds(Long cpCShopId);

    List<CpLogistics> selectLogisticsByWarehousId(Long WarehousId);

    List<Long> getLogisticInfoByWarehouseIdAndShopId(Long warehouse,Long cpShopId);
}
