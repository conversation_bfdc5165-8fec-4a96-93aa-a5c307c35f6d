package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.request.StCUnfullcarCostDetailQueryRequest;
import com.jackrain.nea.st.model.request.StCUnfullcarCostQueryRequest;
import com.jackrain.nea.st.model.result.StCUnfullcarCostDetailQueryResult;
import com.jackrain.nea.st.model.result.StCUnfullcarCostQueryResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/2 下午12:55
 * @describe :
 */
public interface StCUnfullcarCostQueryCmd {

    /**
     * 查询零担报价设置
     *
     * @param request 查询请求
     * @return 查询结果
     */
    ValueHolderV14<StCUnfullcarCostQueryResult> queryUnfullcarCost(StCUnfullcarCostQueryRequest request);

    /**
     * 根据报价ID和重量查询零担报价明细
     *
     * @param request 查询请求
     * @return 查询结果
     */
    ValueHolderV14<StCUnfullcarCostDetailQueryResult> queryUnfullcarCostDetail(StCUnfullcarCostDetailQueryRequest request);
}
