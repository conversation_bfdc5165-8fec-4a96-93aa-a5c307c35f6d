package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/6/9 20:53
 * @Description 快递报价设置明细表DO
 * @Version 1.0
 */
@TableName(value = "ST_C_EXPRESS_PRICE_STRATEGY_ITEM")
@Data
public class StCExpressPriceStrategyItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_EXPRESS_PRICE_STRATEGY_ID")
    @Field(type = FieldType.Long)
    private Long stCExpressPriceStrategyId;

    @JSONField(name = "PROVINCE_ID")
    @Field(type = FieldType.Long)
    private Long provinceId;

    @JSONField(name = "CITY_ID")
    @Field(type = FieldType.Long)
    private Long cityId;

    @JSONField(name = "START_WEIGHT")
    @Field(type = FieldType.Float)
    private BigDecimal startWeight;

    @JSONField(name = "END_WEIGHT")
    @Field(type = FieldType.Float)
    private BigDecimal endWeight;

    @JSONField(name = "PRICE_EXPRESS")
    @Field(type = FieldType.Float)
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal priceExpress;

    @JSONField(name = "PRICE_FIRST_WEIGHT")
    @Field(type = FieldType.Float)
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal priceFirstWeight;

    @JSONField(name = "PRICE_CONTINUED_WEIGHT")
    @Field(type = FieldType.Float)
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal priceContinuedWeight;


    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Date)
    private Date modifieddate;

    @JSONField(name = "PRICE_HOME_DELIVERY")
    @TableField(exist = false)
    private BigDecimal priceHomeDelivery;

}
