package com.jackrain.nea.st.api.cycle;

import com.jackrain.nea.st.model.request.cycle.StCCyclePurchaseStrategyQueryRequest;
import com.jackrain.nea.st.model.result.cycle.StCCyclePurchaseStrategyResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @Auther: chenhao
 * @Date: 2022-09-05 10:16
 * @Description: 周期购服务
 */
public interface StCCyclePurchaseStrategyCmd {

    /**
     * 根据商品编码查已审核数据（主表+明细）
     *
     * @param request 入参
     * @return StCCyclePurchaseStrategyResult
     */
    ValueHolderV14<List<StCCyclePurchaseStrategyResult>> queryCyclePurchaseStrategyByProCode(StCCyclePurchaseStrategyQueryRequest request);

    /**
     * 根据主表ID查询数据（主表+明细）
     *
     * @param request 入参
     * @return StCCyclePurchaseStrategyResult
     */
    ValueHolderV14<StCCyclePurchaseStrategyResult> queryCyclePurchaseStrategyById(StCCyclePurchaseStrategyQueryRequest request);
}
