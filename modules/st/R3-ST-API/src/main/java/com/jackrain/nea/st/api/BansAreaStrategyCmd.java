package com.jackrain.nea.st.api;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @Descroption 物流禁发区域
 * <AUTHOR>
 * @Date 2022/6/10 18:26
 */
public interface BansAreaStrategyCmd {

    /**
     * 导入模板下载
     *
     * @return
     */
    ValueHolderV14<String> downLoadImprotTemp(User user);

    /**
     * import
     *
     * @return
     */
    ValueHolderV14 importBansArea(User user, List<Map<String, String>> excelList, Long logisticsId);

}
