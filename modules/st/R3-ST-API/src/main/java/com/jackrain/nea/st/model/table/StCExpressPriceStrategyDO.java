package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @Date 2022/6/9 19:36
 * @Description 快递报价设置主表DO
 * @Version 1.0
 */
@TableName(value = "ST_C_EXPRESS_PRICE_STRATEGY")
@Data
public class StCExpressPriceStrategyDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    @Field(type = FieldType.Long)
    private Long cpCLogisticsId;

    @JSONField(name = "PRICE_HOME_DELIVERY")
    private BigDecimal priceHomeDelivery;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "START_DATE")
    @Field(type = FieldType.Date)
    private Date startDate;

    @JSONField(name = "END_DATE")
    @Field(type = FieldType.Date)
    private Date endDate;

    @JSONField(name = "SUBMITTERID")
    @Field(type = FieldType.Long)
    private Long submitterid;

    @JSONField(name = "SUBMIT_TIME")
    @Field(type = FieldType.Date)
    private Date submitTime;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;

    @JSONField(name = "CLOSERID")
    @Field(type = FieldType.Long)
    private Long closerid;

    @JSONField(name = "CLOSE_TIME")
    @Field(type = FieldType.Date)
    private Date closeTime;

    @JSONField(name = "CLOSE_STATUS")
    @Field(type = FieldType.Integer)
    private Integer closeStatus;


    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Date)
    private Date modifieddate;

}
