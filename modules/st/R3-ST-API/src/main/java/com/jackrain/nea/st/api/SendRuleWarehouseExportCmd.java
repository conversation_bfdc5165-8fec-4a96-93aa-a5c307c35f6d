package com.jackrain.nea.st.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author:huang.<PERSON><PERSON><PERSON>
 * @since: 2019/9/5
 * @create at : 2019/9/5 11:42
 */
public interface SendRuleWarehouseExportCmd extends Command {
    /**
     * 导出
     * @return
     */
    public ValueHolderV14 exportSendRuleWarehouseRank(JSONObject obj, User user);

    /**
     * 导出(比例)
     * @return
     */
    public ValueHolderV14 exportSendRuleWarehouseRate(JSONObject obj, User user);
}
