package com.jackrain.nea.st.api;

import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @ClassName JdDistributionCmd
 * @Description 京东分销商dubbo接口
 * <AUTHOR>
 * @Date 2022/12/12 13:46
 * @Version 1.0
 */
public interface JdDistributionLogisticsCmd extends Command {

    /**
     * 根据京东分销商ID获取物流公司数据
     *
     * @param jdDistributionId
     * @param shopId
     * @return
     */
    ValueHolderV14<List<Long>> getLogisticsIdsByDistributionId(Long shopId, String jdDistributionId);
}
