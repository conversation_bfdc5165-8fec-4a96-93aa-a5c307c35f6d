package com.jackrain.nea.st.model.request;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/6/21 22:22
 */
@Data
public class StCExpressPriceStrategyPoi implements Serializable {

    /**
     * 仓库ID
     */
    private Long cpCPhyWarehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 物流公司ID
     */
    private Long cpCLogisticsId;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 送货上门费,数值
     */
    private BigDecimal priceHomeDeliveryNum;

    /**
     * 送货上门费
     */
    private String priceHomeDelivery;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开始日期，日期格式
     */
    private Date startDateForDate;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期，日期格式
     */
    private Date endDateForDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 省份ID
     */
    private Long provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 起始重量，数值
     */
    private BigDecimal startWeightNum;

    /**
     * 起始重量
     */
    private String startWeight;

    /**
     * 结束重量，数值
     */
    private BigDecimal endWeightNum;

    /**
     * 结束重量
     */
    private String endWeight;

    /**
     * 快递费用，数值
     */
    private BigDecimal priceExpressNum;

    /**
     * 快递费用
     */
    private String priceExpress;

    /**
     * 首重，数值
     */
    private BigDecimal priceFirstWeightNum;

    /**
     * 首重
     */
    private String priceFirstWeight;

    /**
     * 续重，数值
     */
    private BigDecimal priceContinuedWeightNum;

    /**
     * 续重
     */
    private String priceContinuedWeight;

    /**
     * 行数
     */
    private Integer rowNum;
}
