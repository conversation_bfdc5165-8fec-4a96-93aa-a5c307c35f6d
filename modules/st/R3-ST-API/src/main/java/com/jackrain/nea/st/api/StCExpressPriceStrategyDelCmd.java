package com.jackrain.nea.st.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/6/9 16:39
 * @Description 快递报价设置删除策略
 * @Version 1.0
 */
public interface StCExpressPriceStrategyDelCmd extends Command {
}
