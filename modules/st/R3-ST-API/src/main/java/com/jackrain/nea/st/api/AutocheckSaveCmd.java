package com.jackrain.nea.st.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.model.table.StCAutoCheckDO;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @Descroption 订单自动审核-保存
 * <AUTHOR>
 * @Date 2019/3/12 18:26
 */
public interface AutocheckSaveCmd extends Command {
    /**
     * 根据id查询
     *
     * @param id id
     * @return
     */
    ValueHolder getAutoCheck(Long id);

    /**
     * 批量修改自动审核策略
     * @param fixColumn
     * @param idList
     * @param user
     * @return
     */
    ValueHolderV14<JSONArray> batchUpdateAutoCheck(JSONObject fixColumn, List<Long> idList, User user);
}
