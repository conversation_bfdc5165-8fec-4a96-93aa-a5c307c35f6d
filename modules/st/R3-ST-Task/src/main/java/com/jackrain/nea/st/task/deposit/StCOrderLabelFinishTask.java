package com.jackrain.nea.st.task.deposit;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.st.services.StCOrderLabelCloseService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName : StCOrderLabelTask  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-11-25 10:28  
 */
@Slf4j
@Component
public class StCOrderLabelFinishTask implements IR3Task {
    @Autowired
    private StCOrderLabelCloseService stCOrderLabelCloseService;

    @Override
    @XxlJob(value = "StCOrderLabelFinishTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        log.info(LogUtil.format("Start StCOrderLabelCloseService.execute. ReceiveParams:params:{};"),
                JSONObject.toJSONString(params));

        RunTaskResult runTaskResult = new RunTaskResult();
        runTaskResult.setSuccess(Boolean.TRUE);
        stCOrderLabelCloseService.closeStCOrderLabel();
        return runTaskResult;
    }
}
