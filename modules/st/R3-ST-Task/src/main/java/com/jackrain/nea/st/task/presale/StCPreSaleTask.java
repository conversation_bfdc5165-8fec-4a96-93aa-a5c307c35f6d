package com.jackrain.nea.st.task.presale;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.st.services.StCPreSaleCloseService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title
 * @Description  预到货策略自动结案
 * @Date 2020-06-20
 * @Copyright 2019-2020
 */
@Slf4j
@Component
public class StCPreSaleTask implements IR3Task {

    @Autowired
    private StCPreSaleCloseService stCPreSaleCloseService;

    @Override
    @XxlJob(value = "StCPreSaleTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        log.info(LogUtil.format("Start StCPreSaleTask.execute. ReceiveParams:params:{};"),
                JSONObject.toJSONString(params));

        RunTaskResult runTaskResult = new RunTaskResult();
        runTaskResult.setSuccess(Boolean.TRUE);
        stCPreSaleCloseService.closeStCPreSale();
        return runTaskResult;
    }
}
