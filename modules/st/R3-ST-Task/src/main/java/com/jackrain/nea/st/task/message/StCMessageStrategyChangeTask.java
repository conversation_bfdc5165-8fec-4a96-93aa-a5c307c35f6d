package com.jackrain.nea.st.task.message;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.services.message.StCMessageStrategyChangeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName : SyncErpdwProductStandplat
 * @Description :
 * <AUTHOR>  YCH
 * @Date: 2021-12-07 16:06
 */
@Slf4j
@Component
public class StCMessageStrategyChangeTask implements IR3Task {

    @Autowired
    private StCMessageStrategyChangeService messageService;

    @Override
    @XxlJob(value = "StCMessageStrategyChangeTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();

        log.info("StCMessageStrategyChangeTask.execute");

        ValueHolderV14 v14 = messageService.execute();

        if (v14.getCode() == ResultCode.FAIL) {
            result.setSuccess(false);
            result.setMessage(v14.getMessage());
        } else {
            result.setSuccess(true);
        }
        return result;
    }
}
