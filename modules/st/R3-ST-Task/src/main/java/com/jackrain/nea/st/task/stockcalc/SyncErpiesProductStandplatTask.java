package com.jackrain.nea.st.task.stockcalc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName : SyncErpiesProductStandplatTask  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2022-03-09 10:24  
 */
@Slf4j
@Component
public class SyncErpiesProductStandplatTask implements IR3Task {

    //@Reference(group = "ps-ext", version = "1.0")
    //private PsErpiesProductStandplatCmd psErpiesProductStandplatCmd;
    @Autowired
    private PropertiesConf propertiesConf;

    @Override
    @XxlJob(value = "SyncErpiesProductStandplatTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        String shopId = propertiesConf.getProperty("r3.ps.erpiesproductstandplat");
        log.info(" SyncErpiesProductStandplatTask.shopId:{}",shopId);
        if (StringUtils.isBlank(shopId)){
            result.setSuccess(false);
            result.setMessage("没有配置代发店铺！");
            return result;
        }
        String[] split = shopId.split(",");
        List<String> list = Arrays.asList(split);
        ValueHolderV14 valueHolderV14 = null;//psErpiesProductStandplatCmd.syncErpdwProductStandplat(list);
        log.info(" SyncErpiesProductStandplatTask.valueHolderV14:{}", JSON.toJSONString(valueHolderV14));
        if (valueHolderV14.getCode() == ResultCode.FAIL) {
            result.setSuccess(false);
            result.setMessage(valueHolderV14.getMessage());
        } else {
            result.setSuccess(true);
        }
        return result;
    }
}
