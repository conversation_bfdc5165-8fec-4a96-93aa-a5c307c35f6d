package com.jackrain.nea.st.task.jitx;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.st.services.StCVipcomJitxWarehouseService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title
 * @Description  jitx仓库产能重置
 * @Date 2020-08-12
 * @Copyright 2019-2020
 */
@Slf4j
@Component
public class StJitxWarehouseCapacityTask implements IR3Task {

    @Autowired
    private StCVipcomJitxWarehouseService jitxWarehouseService;

    @Override
    @XxlJob(value = "StJitxWarehouseCapacityTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        log.info(LogUtil.format("Start StJitxWarehouseCapacityTask.execute. ReceiveParams:params:{};"),
                JSONObject.toJSONString(params));

        RunTaskResult runTaskResult = new RunTaskResult();
        runTaskResult.setSuccess(Boolean.TRUE);
        jitxWarehouseService.resetOrderQty();
        return runTaskResult;
    }
}
