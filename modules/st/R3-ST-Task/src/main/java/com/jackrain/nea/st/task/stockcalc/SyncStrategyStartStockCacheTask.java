//package com.jackrain.nea.st.task.stockcalc;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.st.services.StrategiesScanLayerService;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//
///**
// * <ul>
// *     <li>
// *         add comment here
// *     </li>
// * </ul>
// * 2020/11/5 11:02
// * r3-st - com.jackrain.nea.st.task.stockcalc
// *
// * <AUTHOR>
// */
//@Slf4j
//@Component
//public class SyncStrategyStartStockCacheTask implements IR3Task {
//
//    private static final Logger logger = LoggerFactory.getLogger(SyncStrategyStartStockCacheTask.class);
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        log.info(LogUtil.format("定时任务扫描开始标策略执行开始");
//        RunTaskResult runTaskResult = new RunTaskResult();
//        try {
//            StrategiesScanLayerService scanLayerService = ApplicationContextHandle.getBean(StrategiesScanLayerService.class);
//            scanLayerService.strategyBegin();
//            runTaskResult.setSuccess(true);
//        } catch (Exception e) {
//            logger.error("定时任务扫描开始标策略执行失败", e);
//            e.printStackTrace();
//            runTaskResult.setSuccess(false);
//            runTaskResult.setMessage("策略执行失败,请检查日志,线程ID:" + Thread.currentThread().getId());
//        }
//        return runTaskResult;
//    }
//}
