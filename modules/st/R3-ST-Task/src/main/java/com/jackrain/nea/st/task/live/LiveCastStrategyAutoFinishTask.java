package com.jackrain.nea.st.task.live;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.st.services.LiveCastStrategyService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description： 直播解析自动结案task
 * Author: RESET
 * Date: Created in 2020/7/3 14:50
 * Modified By:
 */
@Slf4j
@Component
public class LiveCastStrategyAutoFinishTask implements IR3Task {

    @Autowired
    LiveCastStrategyService liveCastStrategyService;

    @Override
    @XxlJob(value = "LiveCastStrategyAutoFinishTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug(LogUtil.format("LiveCastStrategyAutoFinishTask.start"));
        Long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();

        try {
            liveCastStrategyService.updateStrategyStatusByAuto(null);
            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms");
        } catch (Exception e) {
            log.error(LogUtil.format("LiveCastStrategyAutoFinishTask.Error：{}"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }

        return result;
    }

}
