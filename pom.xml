<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.0.6.RELEASE</version>
		<relativePath/>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.burgeon.r3</groupId>
	<artifactId>r3-st</artifactId>
	<version>3.0.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<modules>
		<module>modules/st/R3-ST-API</module>
		<module>modules/st/R3-ST-BLL</module>
		<module>modules/st/R3-ST-Ctrl</module>
		<module>modules/st/R3-ST-Srv</module>
		<module>modules/st/R3-ST-Task</module>
		<module>shell/R3-ST-Srv-Shell-Dubbo</module>
	</modules>

	<properties>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- raincloud -->
			<dependency>
				<groupId>org.syman</groupId>
				<artifactId>raincloud-es</artifactId>
				<version>1.4.0-SNAPSHOT</version>
			</dependency>

			<!-- self -->
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-st-srv</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-st-ctrl</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-st-task</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-st-bll</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>

			<!-- r3-project -->
			<!-- WARN！！！这个包包含的客户端太多了，理论上不能直接依赖 -->
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-data-basic-bll</artifactId>
				<version>3.0.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<artifactId>r3-sg-basic-api</artifactId>
						<groupId>com.burgeon.r3</groupId>
					</exclusion>
					<exclusion>
						<artifactId>r3-sg-store-api</artifactId>
						<groupId>com.burgeon.r3</groupId>
					</exclusion>
					<exclusion>
						<groupId>org.syman</groupId>
						<artifactId>raincloud-dubbo</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-oc-basic-srv</artifactId>
				<version>3.0.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>org.syman</groupId>
						<artifactId>raincloud-dubbo</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.syman</groupId>
						<artifactId>raincloud-es</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-ad-util</artifactId>
				<version>3.0.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>com.burgeon.r3</groupId>
						<artifactId>r3-model-query</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.syman</groupId>
						<artifactId>raincloud-dubbo</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.alibaba</groupId>
						<artifactId>fastjson</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-zuul-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>org.syman</groupId>
						<artifactId>raincloud-dubbo</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.google.guava</groupId>
						<artifactId>guava</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-cp-ext-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-cp-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>ac-finance-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-ps-ext-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-ip-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-sg-interface-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-sg-stocksync-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-oc-oms-model</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>oms-vip-frontinterface-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-sg-basic-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-st-api</artifactId>
				<version>3.0.0-SNAPSHOT</version>
				<scope>compile</scope>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-oc-basic-bll</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>

			<!-- r3-service -->
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-model-query</artifactId>
				<version>3.0.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>org.syman</groupId>
						<artifactId>raincloud-dubbo</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-storage-file</artifactId>
				<version>3.0.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>com.burgeon.r3</groupId>
						<artifactId>r3-service-util</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-common-util</artifactId>
				<version>3.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-service-core</artifactId>
				<version>3.1.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>org.syman</groupId>
						<artifactId>raincloud-model-base</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.aliyun.openservices</groupId>
						<artifactId>ons-client</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.syman</groupId>
						<artifactId>raincloud-dubbo</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>r3-service-impl-dubbo</artifactId>
				<version>3.0.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>com.alibaba</groupId>
						<artifactId>fastjson</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--			20231011-lts切xxl-->
			<!--			<dependency>-->
			<!--				<groupId>com.burgeon.r3</groupId>-->
			<!--				<artifactId>r3-service-task</artifactId>-->
			<!--				<version>3.1.0-SNAPSHOT</version>-->
			<!--			</dependency>-->
			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>R3-Starter-Dubbo</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>

			<!-- nacos -->
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
				<version>2.0.1.RELEASE</version>
				<exclusions>
					<exclusion>
						<artifactId>nacos-client</artifactId>
						<groupId>com.alibaba.nacos</groupId>
					</exclusion>
					<exclusion>
						<groupId>com.google.guava</groupId>
						<artifactId>guava</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.alibaba.nacos</groupId>
				<artifactId>nacos-client</artifactId>
				<version>1.2.1</version>
			</dependency>

			<!-- utils -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>3.17</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>3.17</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml-schemas</artifactId>
				<version>3.17</version>
			</dependency>
			<dependency>
				<groupId>commons-lang</groupId>
				<artifactId>commons-lang</artifactId>
				<version>2.5</version>
			</dependency>
			<dependency>
				<groupId>org.elasticsearch.client</groupId>
				<artifactId>elasticsearch-rest-high-level-client</artifactId>
				<version>5.6.12</version>
			</dependency>

			<dependency>
				<groupId>com.yomahub</groupId>
				<artifactId>tlog-web-spring-boot-starter</artifactId>
				<version>1.5.2-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.yomahub</groupId>
				<artifactId>tlog-dubbo-spring-boot-starter</artifactId>
				<version>1.5.2-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.burgeon.r3</groupId>
				<artifactId>ryytn-xxl-job-spring-boot-starter</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.20</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.10</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>18.0</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.20</version>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>1.2.13</version>
		</dependency>
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo</artifactId>
			<version>2.7.7</version>
		</dependency>
	</dependencies>

	<distributionManagement>
		<snapshotRepository>
			<id>rdc-snapshots</id>
			<url>https://packages.aliyun.com/maven/repository/2228469-snapshot-tknOVW/</url>
		</snapshotRepository>
		<repository>
			<id>rdc-releases</id>
			<url>https://packages.aliyun.com/maven/repository/2228469-release-3W5OzY/</url>
		</repository>
	</distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
					<verbose>true</verbose>
					<fork>true</fork>
					<compilerArgs>
						<arg>-parameters</arg>
					</compilerArgs>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>