package com.jackrain.nea;

import cn.hutool.extra.spring.EnableSpringUtil;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Main注册程序
 *
 * @author: 易邵峰
 * @since: 2019-01-09
 * create at : 2019-01-09 21:30
 */
@SpringBootApplication
@DubboComponentScan({"com.burgeon.r3", "com.jackrain.nea"})
@EnableAsync
@EnableSpringUtil
public class MainApplication extends SpringBootServletInitializer {
    static {
        System.setProperty("dubbo.application.logger", "slf4j");
    }

    public static void main(String[] args) {
        // 若将devtools.enabled设置为true，会导致无法加载Dubbo
        System.setProperty("spring.devtools.restart.enabled", "false");
        ApplicationContext context = SpringApplication.run(applicationClass, args);
        System.out.println("Start SprintBoot Success ContextId=" + context.getId());
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(applicationClass);
    }

    private static final Class<MainApplication> applicationClass = MainApplication.class;

}
